global
    daemon
    log stdout local0 info
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy
    
    # SSL Configuration
    ssl-default-bind-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets
    ssl-default-server-ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384
    ssl-default-server-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option log-health-checks
    option forwardfor
    option http-server-close
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms
    timeout http-request 10s
    timeout http-keep-alive 2s
    timeout check 10s
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

# Frontend for HTTP traffic
frontend ndvn_frontend_http
    bind *:80
    mode http
    
    # Redirect HTTP to HTTPS
    redirect scheme https code 301 if !{ ssl_fc }

# Frontend for HTTPS traffic
frontend ndvn_frontend_https
    bind *:443 ssl crt /etc/ssl/certs/ndvn.pem
    mode http
    
    # Security headers
    http-response set-header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    http-response set-header X-Frame-Options "DENY"
    http-response set-header X-Content-Type-Options "nosniff"
    http-response set-header X-XSS-Protection "1; mode=block"
    http-response set-header Referrer-Policy "strict-origin-when-cross-origin"
    
    # Rate limiting
    stick-table type ip size 100k expire 30s store http_req_rate(10s)
    http-request track-sc0 src
    http-request reject if { sc_http_req_rate(0) gt 20 }
    
    # Health check endpoint
    acl is_health_check path_beg /health
    use_backend health_check if is_health_check
    
    # API Gateway routing
    acl is_api path_beg /api
    use_backend api_gateway if is_api
    
    # Default backend
    default_backend api_gateway

# Backend for API Gateway
backend api_gateway
    mode http
    balance roundrobin
    option httpchk GET /actuator/health
    http-check expect status 200
    
    # API Gateway instances
    server gateway1 api-gateway:8080 check inter 30s rise 2 fall 3
    # server gateway2 api-gateway-2:8080 check inter 30s rise 2 fall 3
    # server gateway3 api-gateway-3:8080 check inter 30s rise 2 fall 3

# Backend for health checks
backend health_check
    mode http
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    server health1 api-gateway:8080 check inter 10s rise 2 fall 2

# Statistics interface
listen stats
    bind *:8404
    mode http
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    stats auth admin:ndvn-admin-2024
    
    # Statistics page styling
    stats show-legends
    stats show-node
    stats show-desc NDVN SCS Platform Load Balancer
    
    # Enable CSV export
    stats csv-header

# Error handling
backend error_503
    mode http
    errorfile 503 /etc/haproxy/errors/503.http
