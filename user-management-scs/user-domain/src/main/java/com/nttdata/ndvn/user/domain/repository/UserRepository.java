package com.nttdata.ndvn.user.domain.repository;

import com.nttdata.ndvn.user.domain.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for User aggregate root.
 * 
 * This interface defines the contract for persisting and retrieving User entities
 * within the User Management bounded context, following Domain-Driven Design principles.
 */
public interface UserRepository {
    
    /**
     * Finds a user by their unique identifier.
     * 
     * @param id the user ID
     * @return an Optional containing the user if found, empty otherwise
     */
    Optional<User> findById(UUID id);
    
    /**
     * Finds a user by their username.
     * 
     * @param username the username
     * @return an Optional containing the user if found, empty otherwise
     */
    Optional<User> findByUsername(String username);
    
    /**
     * Finds a user by their email address.
     * 
     * @param email the email address
     * @return an Optional containing the user if found, empty otherwise
     */
    Optional<User> findByEmail(String email);
    
    /**
     * Finds users by their enabled status.
     * 
     * @param enabled the enabled status
     * @param pageable pagination information
     * @return a page of users with the specified enabled status
     */
    Page<User> findByEnabled(boolean enabled, Pageable pageable);
    
    /**
     * Finds users by their email verification status.
     * 
     * @param emailVerified the email verification status
     * @param pageable pagination information
     * @return a page of users with the specified email verification status
     */
    Page<User> findByEmailVerified(boolean emailVerified, Pageable pageable);
    
    /**
     * Finds users who have a specific role.
     * 
     * @param roleName the name of the role
     * @param pageable pagination information
     * @return a page of users with the specified role
     */
    Page<User> findByRolesName(String roleName, Pageable pageable);
    
    /**
     * Finds users created after a specific date.
     * 
     * @param createdAfter the date after which users were created
     * @param pageable pagination information
     * @return a page of users created after the specified date
     */
    Page<User> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Finds users who haven't logged in since a specific date.
     * 
     * @param lastLoginBefore the date before which users last logged in
     * @param pageable pagination information
     * @return a page of users who haven't logged in since the specified date
     */
    Page<User> findByLastLoginAtBeforeOrLastLoginAtIsNull(LocalDateTime lastLoginBefore, Pageable pageable);
    
    /**
     * Finds users with failed login attempts greater than or equal to the specified count.
     * 
     * @param failedAttempts the minimum number of failed login attempts
     * @param pageable pagination information
     * @return a page of users with the specified number of failed login attempts
     */
    Page<User> findByFailedLoginAttemptsGreaterThanEqual(int failedAttempts, Pageable pageable);
    
    /**
     * Finds users whose accounts are locked.
     * 
     * @param pageable pagination information
     * @return a page of locked user accounts
     */
    Page<User> findByAccountNonLocked(boolean accountNonLocked, Pageable pageable);
    
    /**
     * Searches users by username or email containing the specified text.
     * 
     * @param searchText the text to search for
     * @param pageable pagination information
     * @return a page of users matching the search criteria
     */
    Page<User> findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCase(
            String searchText, String searchText2, Pageable pageable);
    
    /**
     * Finds all users with pagination.
     * 
     * @param pageable pagination information
     * @return a page of all users
     */
    Page<User> findAll(Pageable pageable);
    
    /**
     * Saves a user entity.
     * 
     * @param user the user to save
     * @return the saved user
     */
    User save(User user);
    
    /**
     * Deletes a user entity.
     * 
     * @param user the user to delete
     */
    void delete(User user);
    
    /**
     * Deletes a user by their ID.
     * 
     * @param id the user ID
     */
    void deleteById(UUID id);
    
    /**
     * Checks if a user exists with the specified username.
     * 
     * @param username the username to check
     * @return true if a user exists with the username, false otherwise
     */
    boolean existsByUsername(String username);
    
    /**
     * Checks if a user exists with the specified email.
     * 
     * @param email the email to check
     * @return true if a user exists with the email, false otherwise
     */
    boolean existsByEmail(String email);
    
    /**
     * Checks if a user exists with the specified ID.
     * 
     * @param id the user ID to check
     * @return true if a user exists with the ID, false otherwise
     */
    boolean existsById(UUID id);
    
    /**
     * Counts the total number of users.
     * 
     * @return the total number of users
     */
    long count();
    
    /**
     * Counts users by their enabled status.
     * 
     * @param enabled the enabled status
     * @return the number of users with the specified enabled status
     */
    long countByEnabled(boolean enabled);
    
    /**
     * Counts users by their email verification status.
     * 
     * @param emailVerified the email verification status
     * @return the number of users with the specified email verification status
     */
    long countByEmailVerified(boolean emailVerified);
    
    /**
     * Counts users created after a specific date.
     * 
     * @param createdAfter the date after which users were created
     * @return the number of users created after the specified date
     */
    long countByCreatedAtAfter(LocalDateTime createdAfter);
    
    /**
     * Finds users with active sessions.
     * 
     * @param pageable pagination information
     * @return a page of users with active sessions
     */
    Page<User> findUsersWithActiveSessions(Pageable pageable);
    
    /**
     * Finds users who need password reset (credentials expired).
     * 
     * @param pageable pagination information
     * @return a page of users who need password reset
     */
    Page<User> findByCredentialsNonExpired(boolean credentialsNonExpired, Pageable pageable);
    
    /**
     * Batch saves multiple users.
     *
     * @param users the users to save
     * @return the saved users
     */
    List<User> saveAllUsers(Iterable<User> users);
    
    /**
     * Flushes any pending changes to the database.
     */
    void flush();
}
