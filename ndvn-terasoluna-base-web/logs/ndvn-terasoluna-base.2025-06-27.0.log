2025-06-27 10:29:16.526 [restartedMain] INFO  com.nttdata.ndvn.web.Application - Starting Application using Java 21.0.7 with PID 56985 (/home/<USER>/project/ndvn-terasoluna-base/ndvn-terasoluna-base-web/build/classes/java/main started by dunghc in /home/<USER>/project/ndvn-terasoluna-base/ndvn-terasoluna-base-web)
2025-06-27 10:29:16.527 [restartedMain] DEBUG com.nttdata.ndvn.web.Application - Running with Spring Boot v3.4.1, Spring v6.2.1
2025-06-27 10:29:16.528 [restartedMain] INFO  com.nttdata.ndvn.web.Application - The following 1 profile is active: "development"
2025-06-27 10:29:16.552 [restartedMain] INFO  o.s.b.d.restart.ChangeableUrls - The Class-Path manifest attribute in /home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.xml.bind/jaxb-impl/4.0.5/b70ad3db43ee72d7a35ae3c4d1d6d2e08ce7623/jaxb-impl-4.0.5.jar referenced one or more files that do not exist: file:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.xml.bind/jaxb-impl/4.0.5/b70ad3db43ee72d7a35ae3c4d1d6d2e08ce7623/jaxb-core.jar,file:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.xml.bind/jaxb-impl/4.0.5/b70ad3db43ee72d7a35ae3c4d1d6d2e08ce7623/angus-activation.jar
2025-06-27 10:29:16.552 [restartedMain] INFO  o.s.b.d.restart.ChangeableUrls - The Class-Path manifest attribute in /home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.xml.bind/jaxb-core/4.0.5/ad427d8777ae2495bfcb37069d611e8379867e6d/jaxb-core-4.0.5.jar referenced one or more files that do not exist: file:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.xml.bind/jaxb-core/4.0.5/ad427d8777ae2495bfcb37069d611e8379867e6d/jakarta.activation-api.jar,file:/home/<USER>/.gradle/caches/modules-2/files-2.1/com.sun.xml.bind/jaxb-core/4.0.5/ad427d8777ae2495bfcb37069d611e8379867e6d/jakarta.xml.bind-api.jar
2025-06-27 10:29:16.552 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-27 10:29:16.552 [restartedMain] INFO  o.s.b.d.e.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-27 10:29:17.134 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-27 10:29:17.150 [restartedMain] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 8 ms. Found 0 JPA repository interfaces.
2025-06-27 10:29:17.591 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-06-27 10:29:17.600 [restartedMain] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-06-27 10:29:17.600 [restartedMain] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.34]
2025-06-27 10:29:17.625 [restartedMain] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-27 10:29:17.625 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1073 ms
2025-06-27 10:29:17.883 [restartedMain] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-27 10:29:17.912 [restartedMain] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 6.6.4.Final
2025-06-27 10:29:17.929 [restartedMain] INFO  o.h.c.i.RegionFactoryInitiator - HHH000026: Second-level cache disabled
2025-06-27 10:29:18.082 [restartedMain] INFO  o.s.o.j.p.SpringPersistenceUnitInfo - No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-27 10:29:18.303 [restartedMain] WARN  org.hibernate.orm.deprecation - HHH90000025: H2Dialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-06-27 10:29:18.337 [restartedMain] INFO  o.hibernate.orm.connections.pooling - HHH10001005: Database info:
	Database JDBC URL [Connecting through datasource 'org.apache.commons.dbcp2.BasicDataSource@35c0f302']
	Database driver: undefined/unknown
	Database version: 2.3.232
	Autocommit mode: undefined/unknown
	Isolation level: undefined/unknown
	Minimum pool size: undefined/unknown
	Maximum pool size: undefined/unknown
2025-06-27 10:29:18.520 [restartedMain] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-27 10:29:18.525 [restartedMain] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-27 10:29:18.544 [restartedMain] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-27 10:29:19.138 [restartedMain] INFO  o.s.b.a.h.H2ConsoleAutoConfiguration - H2 console available at '/h2-console'. Database available at 'jdbc:h2:mem:devdb'
2025-06-27 10:29:19.203 [restartedMain] INFO  o.s.b.d.a.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-27 10:29:19.218 [restartedMain] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 3 endpoints beneath base path '/actuator'
2025-06-27 10:29:19.290 [restartedMain] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port 8080 (http) with context path '/'
2025-06-27 10:29:19.312 [restartedMain] INFO  com.nttdata.ndvn.web.Application - Started Application in 3.091 seconds (process running for 3.356)
2025-06-27 10:29:23.754 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-27 10:29:23.754 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-27 10:29:23.755 [http-nio-8080-exec-1] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-27 10:29:23.791 [http-nio-8080-exec-1] WARN  o.s.web.servlet.PageNotFound - No mapping for GET /
2025-06-27 10:29:23.799 [http-nio-8080-exec-1] WARN  o.s.web.servlet.PageNotFound - No endpoint GET /.
2025-06-27 10:29:38.550 [http-nio-8080-exec-2] WARN  o.s.web.servlet.PageNotFound - No mapping for GET /
2025-06-27 10:29:38.551 [http-nio-8080-exec-2] WARN  o.s.web.servlet.PageNotFound - No endpoint GET /.
