package com.nttdata.ndvn.customer.application.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Data Transfer Object for Customer entity.
 */
public class CustomerDto {
    
    private UUID id;
    
    @NotBlank(message = "Customer number is required")
    private String customerNumber;
    
    private UUID userId;
    
    @NotNull(message = "Customer type is required")
    private CustomerType customerType;
    
    private String companyName;
    
    private String firstName;
    
    private String lastName;
    
    @NotBlank(message = "Email is required")
    @Email(message = "Invalid email format")
    private String email;
    
    private String phone;
    
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate dateOfBirth;
    
    private String taxId;
    
    @NotNull(message = "Status is required")
    private CustomerStatus status;
    
    private String classification;
    
    private BigDecimal creditLimit;
    
    private List<CustomerAddressDto> addresses;
    
    private List<CustomerNoteDto> notes;
    
    private List<String> segmentNames;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;
    
    // Constructors
    public CustomerDto() {}
    
    // Getters and setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }
    
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    
    public UUID getUserId() { return userId; }
    public void setUserId(UUID userId) { this.userId = userId; }
    
    public CustomerType getCustomerType() { return customerType; }
    public void setCustomerType(CustomerType customerType) { this.customerType = customerType; }
    
    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }
    
    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }
    
    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }
    
    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }
    
    public String getTaxId() { return taxId; }
    public void setTaxId(String taxId) { this.taxId = taxId; }
    
    public CustomerStatus getStatus() { return status; }
    public void setStatus(CustomerStatus status) { this.status = status; }
    
    public String getClassification() { return classification; }
    public void setClassification(String classification) { this.classification = classification; }
    
    public BigDecimal getCreditLimit() { return creditLimit; }
    public void setCreditLimit(BigDecimal creditLimit) { this.creditLimit = creditLimit; }
    
    public List<CustomerAddressDto> getAddresses() { return addresses; }
    public void setAddresses(List<CustomerAddressDto> addresses) { this.addresses = addresses; }
    
    public List<CustomerNoteDto> getNotes() { return notes; }
    public void setNotes(List<CustomerNoteDto> notes) { this.notes = notes; }
    
    public List<String> getSegmentNames() { return segmentNames; }
    public void setSegmentNames(List<String> segmentNames) { this.segmentNames = segmentNames; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    // Helper methods
    public String getDisplayName() {
        if (customerType == CustomerType.BUSINESS && companyName != null) {
            return companyName;
        }
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        }
        if (firstName != null) {
            return firstName;
        }
        return email;
    }
    
    public boolean isBusiness() {
        return customerType == CustomerType.BUSINESS;
    }
    
    public boolean isActive() {
        return status == CustomerStatus.ACTIVE;
    }
}
