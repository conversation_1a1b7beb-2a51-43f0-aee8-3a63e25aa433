package com.nttdata.ndvn.customer.infrastructure.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis cache configuration for customer management.
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    public static final String CUSTOMER_CACHE = "customers";
    public static final String CUSTOMER_BY_EMAIL_CACHE = "customers-by-email";
    public static final String CUSTOMER_BY_NUMBER_CACHE = "customers-by-number";
    public static final String CUSTOMER_SEGMENTS_CACHE = "customer-segments";
    public static final String CUSTOMER_SEARCH_CACHE = "customer-search";
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()))
            .disableCachingNullValues();
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // Customer cache - 1 hour TTL
        cacheConfigurations.put(CUSTOMER_CACHE, defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // Customer lookup caches - 2 hours TTL
        cacheConfigurations.put(CUSTOMER_BY_EMAIL_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        cacheConfigurations.put(CUSTOMER_BY_NUMBER_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // Customer segments cache - 4 hours TTL (segments change less frequently)
        cacheConfigurations.put(CUSTOMER_SEGMENTS_CACHE, defaultConfig.entryTtl(Duration.ofHours(4)));
        
        // Search results cache - 15 minutes TTL
        cacheConfigurations.put(CUSTOMER_SEARCH_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
            .cacheDefaults(defaultConfig)
            .withInitialCacheConfigurations(cacheConfigurations)
            .build();
    }
}
