package com.nttdata.ndvn.customer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.UUID;

public class CustomerNoteAddedEvent {
    private UUID eventId;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    private UUID customerId;
    private String customerNumber;
    private UUID noteId;
    private String noteType;
    private String subject;
    private boolean isInternal;
    private String createdBy;

    public CustomerNoteAddedEvent() {}

    private CustomerNoteAddedEvent(Builder builder) {
        this.eventId = builder.eventId;
        this.eventTime = builder.eventTime;
        this.customerId = builder.customerId;
        this.customerNumber = builder.customerNumber;
        this.noteId = builder.noteId;
        this.noteType = builder.noteType;
        this.subject = builder.subject;
        this.isInternal = builder.isInternal;
        this.createdBy = builder.createdBy;
    }

    public static Builder builder() { return new Builder(); }

    public static class Builder {
        private UUID eventId;
        private LocalDateTime eventTime;
        private UUID customerId;
        private String customerNumber;
        private UUID noteId;
        private String noteType;
        private String subject;
        private boolean isInternal;
        private String createdBy;

        public Builder eventId(UUID eventId) { this.eventId = eventId; return this; }
        public Builder eventTime(LocalDateTime eventTime) { this.eventTime = eventTime; return this; }
        public Builder customerId(UUID customerId) { this.customerId = customerId; return this; }
        public Builder customerNumber(String customerNumber) { this.customerNumber = customerNumber; return this; }
        public Builder noteId(UUID noteId) { this.noteId = noteId; return this; }
        public Builder noteType(String noteType) { this.noteType = noteType; return this; }
        public Builder subject(String subject) { this.subject = subject; return this; }
        public Builder isInternal(boolean isInternal) { this.isInternal = isInternal; return this; }
        public Builder createdBy(String createdBy) { this.createdBy = createdBy; return this; }
        public CustomerNoteAddedEvent build() { return new CustomerNoteAddedEvent(this); }
    }

    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    public UUID getNoteId() { return noteId; }
    public void setNoteId(UUID noteId) { this.noteId = noteId; }
    public String getNoteType() { return noteType; }
    public void setNoteType(String noteType) { this.noteType = noteType; }
    public String getSubject() { return subject; }
    public void setSubject(String subject) { this.subject = subject; }
    public boolean isInternal() { return isInternal; }
    public void setInternal(boolean internal) { isInternal = internal; }
    public String getCreatedBy() { return createdBy; }
    public void setCreatedBy(String createdBy) { this.createdBy = createdBy; }
}
