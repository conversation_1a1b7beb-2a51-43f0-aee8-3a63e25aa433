package com.nttdata.ndvn.gateway.config;

import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;

/**
 * Gateway routing configuration for NDVN SCS services
 */
@Configuration
public class GatewayConfig {

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder.routes()
            // User Management Service Routes
            .route("user-service", r -> r
                .path("/api/v1/users/**", "/api/v1/auth/**")
                .and()
                .method(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE)
                .filters(f -> f
                    .stripPrefix(0)
                    .addRequestHeader("X-Gateway-Source", "api-gateway")
                    .addResponseHeader("X-Service", "user-management")
                    .circuitBreaker(config -> config
                        .setName("user-service-cb")
                        .setFallbackUri("forward:/fallback/user-service"))
                    .retry(config -> config
                        .setRetries(3)
                        .setMethods(HttpMethod.GET)
                        .setBackoff(java.time.Duration.ofMillis(100), 
                                   java.time.Duration.ofMillis(1000), 2, false)))
                .uri("lb://user-management-service"))
            
            // Customer Management Service Routes
            .route("customer-service", r -> r
                .path("/api/v1/customers/**")
                .and()
                .method(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE)
                .filters(f -> f
                    .stripPrefix(0)
                    .addRequestHeader("X-Gateway-Source", "api-gateway")
                    .addResponseHeader("X-Service", "customer-management")
                    .circuitBreaker(config -> config
                        .setName("customer-service-cb")
                        .setFallbackUri("forward:/fallback/customer-service"))
                    .retry(config -> config
                        .setRetries(3)
                        .setMethods(HttpMethod.GET)
                        .setBackoff(java.time.Duration.ofMillis(100), 
                                   java.time.Duration.ofMillis(1000), 2, false)))
                .uri("lb://customer-management-service"))
            
            // Product Catalog Service Routes
            .route("catalog-service", r -> r
                .path("/api/v1/products/**", "/api/v1/categories/**", "/api/v1/inventory/**")
                .and()
                .method(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE)
                .filters(f -> f
                    .stripPrefix(0)
                    .addRequestHeader("X-Gateway-Source", "api-gateway")
                    .addResponseHeader("X-Service", "product-catalog")
                    .circuitBreaker(config -> config
                        .setName("catalog-service-cb")
                        .setFallbackUri("forward:/fallback/catalog-service"))
                    .retry(config -> config
                        .setRetries(3)
                        .setMethods(HttpMethod.GET)
                        .setBackoff(java.time.Duration.ofMillis(100), 
                                   java.time.Duration.ofMillis(1000), 2, false)))
                .uri("lb://product-catalog-service"))
            
            // Order Management Service Routes
            .route("order-service", r -> r
                .path("/api/v1/orders/**")
                .and()
                .method(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE)
                .filters(f -> f
                    .stripPrefix(0)
                    .addRequestHeader("X-Gateway-Source", "api-gateway")
                    .addResponseHeader("X-Service", "order-management")
                    .circuitBreaker(config -> config
                        .setName("order-service-cb")
                        .setFallbackUri("forward:/fallback/order-service"))
                    .retry(config -> config
                        .setRetries(3)
                        .setMethods(HttpMethod.GET)
                        .setBackoff(java.time.Duration.ofMillis(100), 
                                   java.time.Duration.ofMillis(1000), 2, false)))
                .uri("lb://order-management-service"))
            
            // Notification Service Routes
            .route("notification-service", r -> r
                .path("/api/v1/notifications/**", "/api/v1/templates/**")
                .and()
                .method(HttpMethod.GET, HttpMethod.POST, HttpMethod.PUT, HttpMethod.DELETE)
                .filters(f -> f
                    .stripPrefix(0)
                    .addRequestHeader("X-Gateway-Source", "api-gateway")
                    .addResponseHeader("X-Service", "notification")
                    .circuitBreaker(config -> config
                        .setName("notification-service-cb")
                        .setFallbackUri("forward:/fallback/notification-service"))
                    .retry(config -> config
                        .setRetries(3)
                        .setMethods(HttpMethod.GET)
                        .setBackoff(java.time.Duration.ofMillis(100), 
                                   java.time.Duration.ofMillis(1000), 2, false)))
                .uri("lb://notification-service"))
            
            // Health Check Aggregation Route
            .route("health-check", r -> r
                .path("/health/**")
                .filters(f -> f
                    .stripPrefix(1)
                    .addRequestHeader("X-Gateway-Source", "api-gateway"))
                .uri("lb://health-aggregator"))
            
            // API Documentation Route
            .route("api-docs", r -> r
                .path("/v3/api-docs/**")
                .filters(f -> f
                    .addRequestHeader("X-Gateway-Source", "api-gateway"))
                .uri("lb://api-documentation"))
            
            .build();
    }
}
