# Prometheus configuration for NDVN SCS Platform
global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'ndvn-scs'
    environment: 'development'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them according to the global 'evaluation_interval'.
rule_files:
  - "/etc/prometheus/rules/*.yml"

# Scrape configuration
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    metrics_path: /metrics
    scrape_interval: 15s

  # Node Exporter for system metrics
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    metrics_path: /metrics
    scrape_interval: 15s

  # cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    metrics_path: /metrics
    scrape_interval: 15s

  # API Gateway metrics
  - job_name: 'api-gateway'
    consul_sd_configs:
      - server: 'consul:8500'
        services: ['api-gateway']
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_node]
        target_label: instance
      - source_labels: [__meta_consul_service_port]
        target_label: __address__
        replacement: '${1}:8081'
    metrics_path: /actuator/prometheus
    scrape_interval: 15s

  # User Management Service
  - job_name: 'user-management-service'
    consul_sd_configs:
      - server: 'consul:8500'
        services: ['user-management-service']
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_node]
        target_label: instance
      - source_labels: [__meta_consul_service_port]
        target_label: __address__
        replacement: '${1}:8081'
    metrics_path: /actuator/prometheus
    scrape_interval: 15s

  # Customer Management Service
  - job_name: 'customer-management-service'
    consul_sd_configs:
      - server: 'consul:8500'
        services: ['customer-management-service']
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_node]
        target_label: instance
      - source_labels: [__meta_consul_service_port]
        target_label: __address__
        replacement: '${1}:8082'
    metrics_path: /actuator/prometheus
    scrape_interval: 15s

  # Product Catalog Service
  - job_name: 'product-catalog-service'
    consul_sd_configs:
      - server: 'consul:8500'
        services: ['product-catalog-service']
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_node]
        target_label: instance
      - source_labels: [__meta_consul_service_port]
        target_label: __address__
        replacement: '${1}:8083'
    metrics_path: /actuator/prometheus
    scrape_interval: 15s

  # Order Management Service
  - job_name: 'order-management-service'
    consul_sd_configs:
      - server: 'consul:8500'
        services: ['order-management-service']
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_node]
        target_label: instance
      - source_labels: [__meta_consul_service_port]
        target_label: __address__
        replacement: '${1}:8084'
    metrics_path: /actuator/prometheus
    scrape_interval: 15s

  # Notification Service
  - job_name: 'notification-service'
    consul_sd_configs:
      - server: 'consul:8500'
        services: ['notification-service']
    relabel_configs:
      - source_labels: [__meta_consul_service]
        target_label: job
      - source_labels: [__meta_consul_node]
        target_label: instance
      - source_labels: [__meta_consul_service_port]
        target_label: __address__
        replacement: '${1}:8085'
    metrics_path: /actuator/prometheus
    scrape_interval: 15s

  # Kafka metrics (if JMX exporter is configured)
  - job_name: 'kafka'
    static_configs:
      - targets: ['kafka:9308']  # Assuming JMX exporter on port 9308
    metrics_path: /metrics
    scrape_interval: 30s

  # Keycloak metrics
  - job_name: 'keycloak'
    static_configs:
      - targets: ['keycloak:9000']
    metrics_path: /metrics
    scrape_interval: 30s

  # Redis metrics (if redis_exporter is configured)
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']  # Assuming redis_exporter on port 9121
    metrics_path: /metrics
    scrape_interval: 30s

  # Elasticsearch metrics
  - job_name: 'elasticsearch'
    static_configs:
      - targets: ['elasticsearch:9200']
    metrics_path: /_prometheus/metrics
    scrape_interval: 30s

# Remote write configuration (for long-term storage)
# remote_write:
#   - url: "https://prometheus-remote-write-endpoint"
#     basic_auth:
#       username: "username"
#       password: "password"

# Storage configuration
storage:
  tsdb:
    retention.time: 15d
    retention.size: 10GB
