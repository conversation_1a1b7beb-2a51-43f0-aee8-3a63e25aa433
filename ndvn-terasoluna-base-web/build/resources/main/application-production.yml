# Production environment configuration
spring:
  # Database configuration for production (example with PostgreSQL)
  datasource:
    url: ${DATABASE_URL:*****************************************************}
    username: ${DATABASE_USERNAME:ndvn_user}
    password: ${DATABASE_PASSWORD:ndvn_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  # H2 Console disabled for production
  h2:
    console:
      enabled: false
  
  # JPA configuration for production
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: false
        dialect: org.hibernate.dialect.PostgreSQLDialect

# Production server configuration
server:
  port: ${SERVER_PORT:8080}
  error:
    include-stacktrace: never
    include-message: never

# Management endpoints for production
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: never

# Logging configuration for production
logging:
  level:
    com.nttdata.ndvn: INFO
    org.springframework: WARN
    org.hibernate.SQL: WARN
    root: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
