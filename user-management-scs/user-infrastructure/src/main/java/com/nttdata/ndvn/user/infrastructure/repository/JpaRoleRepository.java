package com.nttdata.ndvn.user.infrastructure.repository;

import com.nttdata.ndvn.user.domain.model.Role;
import com.nttdata.ndvn.user.domain.repository.RoleRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * JPA implementation of RoleRepository.
 * 
 * This repository provides data access operations for Role entities using Spring Data JPA,
 * implementing the domain repository interface to maintain clean architecture boundaries.
 */
@Repository
public interface JpaRoleRepository extends JpaRepository<Role, UUID>, RoleRepository {
    
    @Override
    Optional<Role> findByName(String name);
    
    @Override
    Page<Role> findByActive(boolean active, Pageable pageable);
    
    @Override
    Page<Role> findBySystemRole(boolean systemRole, Pageable pageable);
    
    @Override
    Page<Role> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    @Override
    @Query("SELECT r FROM Role r JOIN r.permissions p WHERE p.name = :permissionName")
    Page<Role> findByPermissionsName(@Param("permissionName") String permissionName, Pageable pageable);
    
    @Override
    List<Role> findByNameIn(Set<String> names);
    
    @Override
    boolean existsByName(String name);
    
    @Override
    long countByActive(boolean active);
    
    @Override
    long countBySystemRole(boolean systemRole);
    
    @Override
    @Query("SELECT r FROM Role r WHERE r.systemRole = false AND SIZE(r.users) = 0")
    Page<Role> findDeletableRoles(Pageable pageable);
    
    // Additional JPA-specific queries
    
    /**
     * Finds roles by active status and system role status.
     */
    @Query("SELECT r FROM Role r WHERE r.active = :active AND r.systemRole = :systemRole")
    Page<Role> findByActiveAndSystemRole(@Param("active") boolean active, 
                                        @Param("systemRole") boolean systemRole, 
                                        Pageable pageable);
    
    /**
     * Finds roles with specific permission.
     */
    @Query("SELECT r FROM Role r JOIN r.permissions p WHERE p.name = :permissionName AND r.active = true")
    List<Role> findActiveRolesWithPermission(@Param("permissionName") String permissionName);
    
    /**
     * Finds roles assigned to a specific user.
     */
    @Query("SELECT r FROM Role r JOIN r.users u WHERE u.id = :userId")
    List<Role> findRolesByUserId(@Param("userId") UUID userId);
    
    /**
     * Counts users assigned to a specific role.
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.roles r WHERE r.id = :roleId")
    long countUsersByRoleId(@Param("roleId") UUID roleId);
    
    /**
     * Finds roles with no permissions assigned.
     */
    @Query("SELECT r FROM Role r WHERE r.permissions IS EMPTY")
    Page<Role> findRolesWithoutPermissions(Pageable pageable);
    
    /**
     * Finds roles with multiple permissions.
     */
    @Query("SELECT r FROM Role r WHERE SIZE(r.permissions) > 1")
    Page<Role> findRolesWithMultiplePermissions(Pageable pageable);
    
    /**
     * Searches roles by multiple criteria.
     */
    @Query("SELECT r FROM Role r WHERE " +
           "(:name IS NULL OR LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:description IS NULL OR LOWER(r.description) LIKE LOWER(CONCAT('%', :description, '%'))) AND " +
           "(:active IS NULL OR r.active = :active) AND " +
           "(:systemRole IS NULL OR r.systemRole = :systemRole)")
    Page<Role> searchRoles(@Param("name") String name,
                          @Param("description") String description,
                          @Param("active") Boolean active,
                          @Param("systemRole") Boolean systemRole,
                          Pageable pageable);
    
    /**
     * Gets role statistics.
     */
    @Query("SELECT " +
           "COUNT(r) as totalRoles, " +
           "COUNT(CASE WHEN r.active = true THEN 1 END) as activeRoles, " +
           "COUNT(CASE WHEN r.systemRole = true THEN 1 END) as systemRoles, " +
           "AVG(SIZE(r.permissions)) as avgPermissionsPerRole " +
           "FROM Role r")
    Object[] getRoleStatistics();
    
    /**
     * Finds roles that can be safely deleted.
     */
    @Query("SELECT r FROM Role r WHERE r.systemRole = false AND SIZE(r.users) = 0 AND r.active = false")
    List<Role> findSafelyDeletableRoles();
    
    /**
     * Bulk updates role active status.
     */
    @Query("UPDATE Role r SET r.active = :active, r.updatedAt = CURRENT_TIMESTAMP WHERE r.id IN :roleIds AND r.systemRole = false")
    int bulkUpdateActiveStatus(@Param("roleIds") List<UUID> roleIds, @Param("active") boolean active);

    /**
     * Implementation of saveAllRoles from domain repository.
     */
    default List<Role> saveAllRoles(Iterable<Role> roles) {
        return saveAll(roles);
    }
}
