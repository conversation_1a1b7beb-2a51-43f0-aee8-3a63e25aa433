package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Product aggregate root representing a product entity in the catalog system.
 * 
 * This entity encapsulates all product-related business logic and maintains
 * consistency within the product catalog bounded context. It supports complex
 * product hierarchies, variants, and lifecycle management.
 */
@Entity
@Table(name = "products", indexes = {
    @Index(name = "idx_product_sku", columnList = "sku", unique = true),
    @Index(name = "idx_product_name", columnList = "name"),
    @Index(name = "idx_product_category", columnList = "categoryId"),
    @Index(name = "idx_product_brand", columnList = "brandId"),
    @Index(name = "idx_product_status", columnList = "status"),
    @Index(name = "idx_product_created_at", columnList = "createdAt")
})
@Getter
@Setter
@NoArgsConstructor
public class Product {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotBlank(message = "SKU is required")
    @Size(max = 100, message = "SKU must not exceed 100 characters")
    @Column(name = "sku", unique = true, nullable = false, length = 100)
    private String sku;
    
    @NotBlank(message = "Product name is required")
    @Size(max = 255, message = "Product name must not exceed 255 characters")
    @Column(name = "name", nullable = false)
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @Size(max = 2000, message = "Long description must not exceed 2000 characters")
    @Column(name = "long_description", length = 2000)
    private String longDescription;
    
    @NotNull(message = "Category is required")
    @Column(name = "category_id", nullable = false)
    private UUID categoryId;
    
    @Column(name = "brand_id")
    private UUID brandId;
    
    @NotNull(message = "Base price is required")
    @DecimalMin(value = "0.0", inclusive = false, message = "Base price must be greater than 0")
    @Digits(integer = 10, fraction = 2, message = "Base price must have at most 10 integer digits and 2 decimal places")
    @Column(name = "base_price", nullable = false, precision = 12, scale = 2)
    private BigDecimal basePrice;
    
    @DecimalMin(value = "0.0", message = "Sale price must be non-negative")
    @Digits(integer = 10, fraction = 2, message = "Sale price must have at most 10 integer digits and 2 decimal places")
    @Column(name = "sale_price", precision = 12, scale = 2)
    private BigDecimal salePrice;
    
    @NotNull(message = "Product status is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ProductStatus status = ProductStatus.DRAFT;
    
    @DecimalMin(value = "0.0", message = "Weight must be non-negative")
    @Digits(integer = 8, fraction = 3, message = "Weight must have at most 8 integer digits and 3 decimal places")
    @Column(name = "weight", precision = 11, scale = 3)
    private BigDecimal weight;
    
    @Size(max = 50, message = "Weight unit must not exceed 50 characters")
    @Column(name = "weight_unit", length = 50)
    private String weightUnit = "kg";
    
    @DecimalMin(value = "0.0", message = "Length must be non-negative")
    @Digits(integer = 8, fraction = 2, message = "Length must have at most 8 integer digits and 2 decimal places")
    @Column(name = "length", precision = 10, scale = 2)
    private BigDecimal length;
    
    @DecimalMin(value = "0.0", message = "Width must be non-negative")
    @Digits(integer = 8, fraction = 2, message = "Width must have at most 8 integer digits and 2 decimal places")
    @Column(name = "width", precision = 10, scale = 2)
    private BigDecimal width;
    
    @DecimalMin(value = "0.0", message = "Height must be non-negative")
    @Digits(integer = 8, fraction = 2, message = "Height must have at most 8 integer digits and 2 decimal places")
    @Column(name = "height", precision = 10, scale = 2)
    private BigDecimal height;
    
    @Size(max = 50, message = "Dimension unit must not exceed 50 characters")
    @Column(name = "dimension_unit", length = 50)
    private String dimensionUnit = "cm";
    
    @Column(name = "is_featured")
    private boolean featured = false;
    
    @Column(name = "is_digital")
    private boolean digital = false;
    
    @Column(name = "requires_shipping")
    private boolean requiresShipping = true;
    
    @Column(name = "is_taxable")
    private boolean taxable = true;
    
    @Size(max = 500, message = "Meta title must not exceed 500 characters")
    @Column(name = "meta_title", length = 500)
    private String metaTitle;
    
    @Size(max = 1000, message = "Meta description must not exceed 1000 characters")
    @Column(name = "meta_description", length = 1000)
    private String metaDescription;
    
    @Size(max = 500, message = "Meta keywords must not exceed 500 characters")
    @Column(name = "meta_keywords", length = 500)
    private String metaKeywords;
    
    @Min(value = 0, message = "Sort order must be non-negative")
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProductVariant> variants = new ArrayList<>();
    
    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProductAttribute> attributes = new ArrayList<>();
    
    @OneToMany(mappedBy = "product", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<ProductImage> images = new ArrayList<>();
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Business methods
    
    /**
     * Updates the product's basic information.
     */
    public void updateBasicInfo(String name, String description, String longDescription) {
        this.name = name;
        this.description = description;
        this.longDescription = longDescription;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the product's pricing information.
     */
    public void updatePricing(BigDecimal basePrice, BigDecimal salePrice) {
        if (basePrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Base price must be greater than 0");
        }
        if (salePrice != null && salePrice.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Sale price must be non-negative");
        }
        if (salePrice != null && salePrice.compareTo(basePrice) > 0) {
            throw new IllegalArgumentException("Sale price cannot be greater than base price");
        }
        
        this.basePrice = basePrice;
        this.salePrice = salePrice;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Changes the product status with business rule validation.
     */
    public void changeStatus(ProductStatus newStatus) {
        if (this.status == newStatus) {
            return;
        }
        
        validateStatusTransition(this.status, newStatus);
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Activates the product if it's in a valid state.
     */
    public void activate() {
        if (this.status == ProductStatus.ARCHIVED) {
            throw new IllegalStateException("Cannot activate an archived product");
        }
        changeStatus(ProductStatus.ACTIVE);
    }
    
    /**
     * Deactivates the product.
     */
    public void deactivate() {
        changeStatus(ProductStatus.INACTIVE);
    }
    
    /**
     * Discontinues the product.
     */
    public void discontinue() {
        if (this.status == ProductStatus.ARCHIVED) {
            throw new IllegalStateException("Cannot discontinue an archived product");
        }
        changeStatus(ProductStatus.DISCONTINUED);
    }
    
    /**
     * Archives the product.
     */
    public void archive() {
        changeStatus(ProductStatus.ARCHIVED);
    }
    
    /**
     * Gets the effective selling price (sale price if available, otherwise base price).
     */
    public BigDecimal getEffectivePrice() {
        return salePrice != null ? salePrice : basePrice;
    }
    
    /**
     * Checks if the product is currently on sale.
     */
    public boolean isOnSale() {
        return salePrice != null && salePrice.compareTo(basePrice) < 0;
    }
    
    /**
     * Checks if the product is available for purchase.
     */
    public boolean isAvailable() {
        return status == ProductStatus.ACTIVE;
    }
    
    /**
     * Updates the product's physical dimensions.
     */
    public void updateDimensions(BigDecimal length, BigDecimal width, BigDecimal height, String unit) {
        this.length = length;
        this.width = width;
        this.height = height;
        this.dimensionUnit = unit;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the product's weight.
     */
    public void updateWeight(BigDecimal weight, String unit) {
        this.weight = weight;
        this.weightUnit = unit;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates SEO metadata.
     */
    public void updateSeoMetadata(String metaTitle, String metaDescription, String metaKeywords) {
        this.metaTitle = metaTitle;
        this.metaDescription = metaDescription;
        this.metaKeywords = metaKeywords;
        this.updatedAt = LocalDateTime.now();
    }
    
    private void validateStatusTransition(ProductStatus from, ProductStatus to) {
        // Business rules for status transitions
        switch (from) {
            case DRAFT:
                // Draft can transition to any status
                break;
            case ACTIVE:
                if (to == ProductStatus.DRAFT) {
                    throw new IllegalStateException("Cannot change active product back to draft");
                }
                break;
            case INACTIVE:
                if (to == ProductStatus.DRAFT) {
                    throw new IllegalStateException("Cannot change inactive product back to draft");
                }
                break;
            case DISCONTINUED:
                if (to == ProductStatus.DRAFT || to == ProductStatus.ACTIVE) {
                    throw new IllegalStateException("Cannot reactivate discontinued product");
                }
                break;
            case ARCHIVED:
                throw new IllegalStateException("Cannot change status of archived product");
        }
    }
    
    // Builder pattern
    public static class Builder {
        private String sku;
        private String name;
        private String description;
        private String longDescription;
        private UUID categoryId;
        private UUID brandId;
        private BigDecimal basePrice;
        private BigDecimal salePrice;
        private ProductStatus status = ProductStatus.DRAFT;
        private boolean featured = false;
        private boolean digital = false;
        private boolean requiresShipping = true;
        private boolean taxable = true;
        
        public Builder sku(String sku) {
            this.sku = sku;
            return this;
        }
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder description(String description) {
            this.description = description;
            return this;
        }
        
        public Builder longDescription(String longDescription) {
            this.longDescription = longDescription;
            return this;
        }
        
        public Builder categoryId(UUID categoryId) {
            this.categoryId = categoryId;
            return this;
        }
        
        public Builder brandId(UUID brandId) {
            this.brandId = brandId;
            return this;
        }
        
        public Builder basePrice(BigDecimal basePrice) {
            this.basePrice = basePrice;
            return this;
        }
        
        public Builder salePrice(BigDecimal salePrice) {
            this.salePrice = salePrice;
            return this;
        }
        
        public Builder status(ProductStatus status) {
            this.status = status;
            return this;
        }
        
        public Builder featured(boolean featured) {
            this.featured = featured;
            return this;
        }
        
        public Builder digital(boolean digital) {
            this.digital = digital;
            return this;
        }
        
        public Builder requiresShipping(boolean requiresShipping) {
            this.requiresShipping = requiresShipping;
            return this;
        }
        
        public Builder taxable(boolean taxable) {
            this.taxable = taxable;
            return this;
        }
        
        public Product build() {
            Product product = new Product();
            product.sku = this.sku;
            product.name = this.name;
            product.description = this.description;
            product.longDescription = this.longDescription;
            product.categoryId = this.categoryId;
            product.brandId = this.brandId;
            product.basePrice = this.basePrice;
            product.salePrice = this.salePrice;
            product.status = this.status;
            product.featured = this.featured;
            product.digital = this.digital;
            product.requiresShipping = this.requiresShipping;
            product.taxable = this.taxable;
            return product;
        }
    }
}
