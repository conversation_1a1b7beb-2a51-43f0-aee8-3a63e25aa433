package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Category entity representing a product category in the catalog system.
 * 
 * This entity supports hierarchical category structures with parent-child relationships
 * and provides comprehensive category management capabilities.
 */
@Entity
@Table(name = "categories", indexes = {
    @Index(name = "idx_category_name", columnList = "name"),
    @Index(name = "idx_category_slug", columnList = "slug", unique = true),
    @Index(name = "idx_category_parent", columnList = "parentId"),
    @Index(name = "idx_category_active", columnList = "active"),
    @Index(name = "idx_category_sort_order", columnList = "sortOrder")
})
@Getter
@Setter
@NoArgsConstructor
public class Category {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotBlank(message = "Category name is required")
    @Size(max = 255, message = "Category name must not exceed 255 characters")
    @Column(name = "name", nullable = false)
    private String name;
    
    @NotBlank(message = "Category slug is required")
    @Size(max = 255, message = "Category slug must not exceed 255 characters")
    @Pattern(regexp = "^[a-z0-9-]+$", message = "Slug must contain only lowercase letters, numbers, and hyphens")
    @Column(name = "slug", unique = true, nullable = false)
    private String slug;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @Column(name = "parent_id")
    private UUID parentId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Category parent;
    
    @OneToMany(mappedBy = "parent", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Category> children = new ArrayList<>();
    
    @Column(name = "active", nullable = false)
    private boolean active = true;
    
    @Min(value = 0, message = "Sort order must be non-negative")
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Min(value = 0, message = "Level must be non-negative")
    @Column(name = "level")
    private Integer level = 0;
    
    @Size(max = 1000, message = "Path must not exceed 1000 characters")
    @Column(name = "path", length = 1000)
    private String path;
    
    @Size(max = 500, message = "Image URL must not exceed 500 characters")
    @Column(name = "image_url", length = 500)
    private String imageUrl;
    
    @Size(max = 500, message = "Icon must not exceed 500 characters")
    @Column(name = "icon", length = 500)
    private String icon;
    
    @Size(max = 500, message = "Meta title must not exceed 500 characters")
    @Column(name = "meta_title", length = 500)
    private String metaTitle;
    
    @Size(max = 1000, message = "Meta description must not exceed 1000 characters")
    @Column(name = "meta_description", length = 1000)
    private String metaDescription;
    
    @Size(max = 500, message = "Meta keywords must not exceed 500 characters")
    @Column(name = "meta_keywords", length = 500)
    private String metaKeywords;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Business methods
    
    /**
     * Updates the category's basic information.
     */
    public void updateBasicInfo(String name, String slug, String description) {
        this.name = name;
        this.slug = slug;
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Sets the parent category and updates the hierarchy.
     */
    public void setParent(Category parent) {
        if (parent != null && parent.getId().equals(this.id)) {
            throw new IllegalArgumentException("Category cannot be its own parent");
        }
        
        if (parent != null && isDescendantOf(parent)) {
            throw new IllegalArgumentException("Cannot set descendant as parent - would create circular reference");
        }
        
        this.parent = parent;
        this.parentId = parent != null ? parent.getId() : null;
        updateHierarchy();
    }
    
    /**
     * Activates the category.
     */
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates the category.
     */
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the sort order.
     */
    public void updateSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates SEO metadata.
     */
    public void updateSeoMetadata(String metaTitle, String metaDescription, String metaKeywords) {
        this.metaTitle = metaTitle;
        this.metaDescription = metaDescription;
        this.metaKeywords = metaKeywords;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the category image.
     */
    public void updateImage(String imageUrl, String icon) {
        this.imageUrl = imageUrl;
        this.icon = icon;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Checks if this category is a root category (has no parent).
     */
    public boolean isRoot() {
        return parentId == null;
    }
    
    /**
     * Checks if this category is a leaf category (has no children).
     */
    public boolean isLeaf() {
        return children.isEmpty();
    }
    
    /**
     * Checks if this category is a descendant of the given category.
     */
    public boolean isDescendantOf(Category category) {
        if (category == null || this.parent == null) {
            return false;
        }
        
        if (this.parent.getId().equals(category.getId())) {
            return true;
        }
        
        return this.parent.isDescendantOf(category);
    }
    
    /**
     * Gets the full path from root to this category.
     */
    public String getFullPath() {
        if (path != null) {
            return path;
        }
        
        if (isRoot()) {
            return slug;
        }
        
        return parent.getFullPath() + "/" + slug;
    }
    
    /**
     * Gets all ancestor categories.
     */
    public List<Category> getAncestors() {
        List<Category> ancestors = new ArrayList<>();
        Category current = this.parent;
        
        while (current != null) {
            ancestors.add(0, current); // Add at beginning to maintain order
            current = current.getParent();
        }
        
        return ancestors;
    }
    
    /**
     * Gets all descendant categories (recursive).
     */
    public List<Category> getDescendants() {
        List<Category> descendants = new ArrayList<>();
        
        for (Category child : children) {
            descendants.add(child);
            descendants.addAll(child.getDescendants());
        }
        
        return descendants;
    }
    
    /**
     * Updates the hierarchy information (level and path).
     */
    private void updateHierarchy() {
        if (isRoot()) {
            this.level = 0;
            this.path = this.slug;
        } else {
            this.level = parent.getLevel() + 1;
            this.path = parent.getFullPath() + "/" + this.slug;
        }
        
        // Update children hierarchy
        for (Category child : children) {
            child.updateHierarchy();
        }
        
        this.updatedAt = LocalDateTime.now();
    }
    
    // Builder pattern
    public static class Builder {
        private String name;
        private String slug;
        private String description;
        private UUID parentId;
        private boolean active = true;
        private Integer sortOrder = 0;
        private String imageUrl;
        private String icon;
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder slug(String slug) {
            this.slug = slug;
            return this;
        }
        
        public Builder description(String description) {
            this.description = description;
            return this;
        }
        
        public Builder parentId(UUID parentId) {
            this.parentId = parentId;
            return this;
        }
        
        public Builder active(boolean active) {
            this.active = active;
            return this;
        }
        
        public Builder sortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
            return this;
        }
        
        public Builder imageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
            return this;
        }
        
        public Builder icon(String icon) {
            this.icon = icon;
            return this;
        }
        
        public Category build() {
            Category category = new Category();
            category.name = this.name;
            category.slug = this.slug;
            category.description = this.description;
            category.parentId = this.parentId;
            category.active = this.active;
            category.sortOrder = this.sortOrder;
            category.imageUrl = this.imageUrl;
            category.icon = this.icon;
            return category;
        }
    }
}
