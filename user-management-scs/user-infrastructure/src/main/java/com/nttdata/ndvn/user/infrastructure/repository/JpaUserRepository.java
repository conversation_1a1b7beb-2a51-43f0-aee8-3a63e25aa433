package com.nttdata.ndvn.user.infrastructure.repository;

import com.nttdata.ndvn.user.domain.model.User;
import com.nttdata.ndvn.user.domain.repository.UserRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA implementation of UserRepository.
 * 
 * This repository provides data access operations for User entities using Spring Data JPA,
 * implementing the domain repository interface to maintain clean architecture boundaries.
 */
@Repository
public interface JpaUserRepository extends JpaRepository<User, UUID>, UserRepository {
    
    @Override
    Optional<User> findByUsername(String username);
    
    @Override
    Optional<User> findByEmail(String email);
    
    @Override
    Page<User> findByEnabled(boolean enabled, Pageable pageable);
    
    @Override
    Page<User> findByEmailVerified(boolean emailVerified, Pageable pageable);
    
    @Override
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name = :roleName")
    Page<User> findByRolesName(@Param("roleName") String roleName, Pageable pageable);
    
    @Override
    Page<User> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    @Override
    Page<User> findByLastLoginAtBeforeOrLastLoginAtIsNull(LocalDateTime lastLoginBefore, Pageable pageable);
    
    @Override
    Page<User> findByFailedLoginAttemptsGreaterThanEqual(int failedAttempts, Pageable pageable);
    
    @Override
    Page<User> findByAccountNonLocked(boolean accountNonLocked, Pageable pageable);
    
    @Override
    Page<User> findByUsernameContainingIgnoreCaseOrEmailContainingIgnoreCase(
            String searchText, String searchText2, Pageable pageable);
    
    @Override
    boolean existsByUsername(String username);
    
    @Override
    boolean existsByEmail(String email);
    
    @Override
    long countByEnabled(boolean enabled);
    
    @Override
    long countByEmailVerified(boolean emailVerified);
    
    @Override
    long countByCreatedAtAfter(LocalDateTime createdAfter);
    
    @Override
    @Query("SELECT u FROM User u WHERE EXISTS (SELECT s FROM UserSession s WHERE s.user = u AND s.active = true)")
    Page<User> findUsersWithActiveSessions(Pageable pageable);
    
    @Override
    Page<User> findByCredentialsNonExpired(boolean credentialsNonExpired, Pageable pageable);
    
    // Additional JPA-specific queries
    
    /**
     * Finds users by role name and enabled status.
     */
    @Query("SELECT u FROM User u JOIN u.roles r WHERE r.name = :roleName AND u.enabled = :enabled")
    Page<User> findByRolesNameAndEnabled(@Param("roleName") String roleName, 
                                        @Param("enabled") boolean enabled, 
                                        Pageable pageable);
    
    /**
     * Finds users who have multiple failed login attempts and are still enabled.
     */
    @Query("SELECT u FROM User u WHERE u.failedLoginAttempts >= :minAttempts AND u.enabled = true AND u.accountNonLocked = true")
    List<User> findUsersWithMultipleFailedAttempts(@Param("minAttempts") int minAttempts);
    
    /**
     * Finds users whose passwords are about to expire.
     */
    @Query("SELECT u FROM User u WHERE u.passwordChangedAt < :expirationDate AND u.enabled = true")
    List<User> findUsersWithExpiringPasswords(@Param("expirationDate") LocalDateTime expirationDate);
    
    /**
     * Counts users by role name.
     */
    @Query("SELECT COUNT(u) FROM User u JOIN u.roles r WHERE r.name = :roleName")
    long countByRoleName(@Param("roleName") String roleName);
    
    /**
     * Finds users created between two dates.
     */
    @Query("SELECT u FROM User u WHERE u.createdAt BETWEEN :startDate AND :endDate")
    Page<User> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                     @Param("endDate") LocalDateTime endDate, 
                                     Pageable pageable);
    
    /**
     * Finds users who logged in within a specific time range.
     */
    @Query("SELECT u FROM User u WHERE u.lastLoginAt BETWEEN :startDate AND :endDate")
    Page<User> findByLastLoginAtBetween(@Param("startDate") LocalDateTime startDate, 
                                       @Param("endDate") LocalDateTime endDate, 
                                       Pageable pageable);
    
    /**
     * Finds users with specific account status combinations.
     */
    @Query("SELECT u FROM User u WHERE u.enabled = :enabled AND u.accountNonLocked = :accountNonLocked AND u.credentialsNonExpired = :credentialsNonExpired")
    Page<User> findByAccountStatus(@Param("enabled") boolean enabled,
                                  @Param("accountNonLocked") boolean accountNonLocked,
                                  @Param("credentialsNonExpired") boolean credentialsNonExpired,
                                  Pageable pageable);
    
    /**
     * Searches users by multiple criteria.
     */
    @Query("SELECT u FROM User u WHERE " +
           "(:username IS NULL OR LOWER(u.username) LIKE LOWER(CONCAT('%', :username, '%'))) AND " +
           "(:email IS NULL OR LOWER(u.email) LIKE LOWER(CONCAT('%', :email, '%'))) AND " +
           "(:enabled IS NULL OR u.enabled = :enabled) AND " +
           "(:emailVerified IS NULL OR u.emailVerified = :emailVerified)")
    Page<User> searchUsers(@Param("username") String username,
                          @Param("email") String email,
                          @Param("enabled") Boolean enabled,
                          @Param("emailVerified") Boolean emailVerified,
                          Pageable pageable);
    
    /**
     * Finds users with no roles assigned.
     */
    @Query("SELECT u FROM User u WHERE u.roles IS EMPTY")
    Page<User> findUsersWithoutRoles(Pageable pageable);
    
    /**
     * Finds users with multiple roles.
     */
    @Query("SELECT u FROM User u WHERE SIZE(u.roles) > 1")
    Page<User> findUsersWithMultipleRoles(Pageable pageable);
    
    /**
     * Gets user statistics.
     */
    @Query("SELECT " +
           "COUNT(u) as totalUsers, " +
           "COUNT(CASE WHEN u.enabled = true THEN 1 END) as enabledUsers, " +
           "COUNT(CASE WHEN u.emailVerified = true THEN 1 END) as verifiedUsers, " +
           "COUNT(CASE WHEN u.lastLoginAt IS NOT NULL THEN 1 END) as usersWithLogin " +
           "FROM User u")
    Object[] getUserStatistics();
    
    /**
     * Bulk updates user enabled status.
     */
    @Query("UPDATE User u SET u.enabled = :enabled, u.updatedAt = CURRENT_TIMESTAMP WHERE u.id IN :userIds")
    int bulkUpdateEnabledStatus(@Param("userIds") List<UUID> userIds, @Param("enabled") boolean enabled);
    
    /**
     * Bulk resets failed login attempts.
     */
    @Query("UPDATE User u SET u.failedLoginAttempts = 0, u.accountNonLocked = true, u.updatedAt = CURRENT_TIMESTAMP WHERE u.id IN :userIds")
    int bulkResetFailedLoginAttempts(@Param("userIds") List<UUID> userIds);

    /**
     * Implementation of saveAllUsers from domain repository.
     */
    default List<User> saveAllUsers(Iterable<User> users) {
        return saveAll(users);
    }
}
