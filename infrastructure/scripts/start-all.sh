#!/bin/bash

# NDVN SCS Platform - Master Infrastructure Setup Script
# This script orchestrates the setup of all infrastructure components

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INFRASTRUCTURE_DIR="$(dirname "$SCRIPT_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Print banner
print_banner() {
    echo ""
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║           NDVN SCS Platform Infrastructure Setup            ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}║  Self-Contained Systems Architecture for Terasoluna Base    ║${NC}"
    echo -e "${BLUE}║                                                              ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check Docker
    if ! command -v docker > /dev/null 2>&1; then
        log_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    
    # Check Docker Compose
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    
    # Check available memory
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 4096 ]; then
        log_warning "Available memory is ${available_memory}MB. Recommended minimum is 4GB."
        log_warning "Some services may not start properly with insufficient memory."
    fi
    
    log_success "Prerequisites check completed"
}

# Setup infrastructure components
setup_kafka() {
    log_info "Setting up Kafka infrastructure..."
    cd "$INFRASTRUCTURE_DIR/kafka"
    if [ -f "scripts/setup-kafka.sh" ]; then
        ./scripts/setup-kafka.sh start
    else
        log_error "Kafka setup script not found"
        return 1
    fi
}

setup_gateway() {
    log_info "Setting up API Gateway and Service Discovery..."
    cd "$INFRASTRUCTURE_DIR/gateway"
    if [ -f "scripts/setup-gateway.sh" ]; then
        ./scripts/setup-gateway.sh start
    else
        log_error "Gateway setup script not found"
        return 1
    fi
}

setup_auth() {
    log_info "Setting up Authentication infrastructure..."
    cd "$INFRASTRUCTURE_DIR/auth"
    if [ -f "scripts/setup-auth.sh" ]; then
        ./scripts/setup-auth.sh start
    else
        log_error "Auth setup script not found"
        return 1
    fi
}

setup_monitoring() {
    log_info "Setting up Monitoring and Observability..."
    cd "$INFRASTRUCTURE_DIR/monitoring"
    if [ -f "scripts/setup-monitoring.sh" ]; then
        ./scripts/setup-monitoring.sh start
    else
        log_error "Monitoring setup script not found"
        return 1
    fi
}

setup_development() {
    log_info "Setting up Development environment..."
    cd "$INFRASTRUCTURE_DIR/development"
    if [ -f "scripts/setup-dev-env.sh" ]; then
        ./scripts/setup-dev-env.sh start
    else
        log_error "Development setup script not found"
        return 1
    fi
}

# Wait for all services to be healthy
wait_for_services() {
    log_info "Waiting for all services to be healthy..."
    
    local services=(
        "http://localhost:9092"     # Kafka
        "http://localhost:8500"     # Consul
        "http://localhost:8080"     # API Gateway
        "http://localhost:8090"     # Keycloak
        "http://localhost:9090"     # Prometheus
        "http://localhost:3000"     # Grafana
        "http://localhost:5601"     # Kibana
        "http://localhost:16686"    # Jaeger
    )
    
    local max_attempts=60
    local all_healthy=false
    
    for attempt in $(seq 1 $max_attempts); do
        local healthy_count=0
        
        for service in "${services[@]}"; do
            if curl -f "$service" > /dev/null 2>&1 || \
               curl -f "$service/health" > /dev/null 2>&1 || \
               curl -f "$service/actuator/health" > /dev/null 2>&1; then
                ((healthy_count++))
            fi
        done
        
        if [ $healthy_count -eq ${#services[@]} ]; then
            all_healthy=true
            break
        fi
        
        log_info "Attempt $attempt/$max_attempts: $healthy_count/${#services[@]} services healthy, waiting 10 seconds..."
        sleep 10
    done
    
    if [ "$all_healthy" = true ]; then
        log_success "All services are healthy"
    else
        log_warning "Not all services are healthy after $max_attempts attempts"
        log_warning "Some services may still be starting up"
    fi
}

# Show comprehensive status
show_status() {
    log_info "Infrastructure Status Summary:"
    echo ""
    
    echo -e "${BLUE}Core Infrastructure:${NC}"
    echo "  - Kafka Message Broker: http://localhost:8080 (Kafka UI)"
    echo "  - Service Discovery: http://localhost:8500 (Consul)"
    echo "  - API Gateway: http://localhost:8080"
    echo "  - Authentication: http://localhost:8090 (Keycloak)"
    echo ""
    
    echo -e "${BLUE}Monitoring & Observability:${NC}"
    echo "  - Metrics Dashboard: http://localhost:3000 (Grafana - admin/admin123)"
    echo "  - Metrics Collection: http://localhost:9090 (Prometheus)"
    echo "  - Log Analysis: http://localhost:5601 (Kibana)"
    echo "  - Distributed Tracing: http://localhost:16686 (Jaeger)"
    echo "  - Alerting: http://localhost:9093 (AlertManager)"
    echo ""
    
    echo -e "${BLUE}Development Tools:${NC}"
    echo "  - Development Dashboard: http://localhost:8084"
    echo "  - Database Management: http://localhost:8082 (Adminer)"
    echo "  - Redis Management: http://localhost:8083 (Redis Commander)"
    echo "  - Email Testing: http://localhost:8025 (MailHog)"
    echo "  - Object Storage: http://localhost:9001 (MinIO Console)"
    echo ""
    
    echo -e "${BLUE}Database Connections:${NC}"
    echo "  - User Management DB: localhost:5432"
    echo "  - Customer Management DB: localhost:5433"
    echo "  - Product Catalog DB: localhost:5434"
    echo "  - Order Management DB: localhost:5435"
    echo "  - Notification DB: localhost:5436"
    echo ""
    
    echo -e "${BLUE}Quick Commands:${NC}"
    echo "  - Access dev tools: docker exec -it ndvn-dev-tools bash"
    echo "  - Stop all services: $0 stop"
    echo "  - Restart all services: $0 restart"
    echo "  - Check service status: $0 status"
    echo ""
}

# Stop all services
stop_all() {
    log_info "Stopping all infrastructure services..."
    
    # Stop in reverse order
    cd "$INFRASTRUCTURE_DIR/development" && ./scripts/setup-dev-env.sh stop 2>/dev/null || true
    cd "$INFRASTRUCTURE_DIR/monitoring" && ./scripts/setup-monitoring.sh stop 2>/dev/null || true
    cd "$INFRASTRUCTURE_DIR/auth" && ./scripts/setup-auth.sh stop 2>/dev/null || true
    cd "$INFRASTRUCTURE_DIR/gateway" && ./scripts/setup-gateway.sh stop 2>/dev/null || true
    cd "$INFRASTRUCTURE_DIR/kafka" && ./scripts/setup-kafka.sh stop 2>/dev/null || true
    
    log_success "All services stopped"
}

# Clean up all services
cleanup_all() {
    log_warning "This will remove ALL infrastructure data and containers. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up all infrastructure..."
        
        cd "$INFRASTRUCTURE_DIR/development" && ./scripts/setup-dev-env.sh cleanup 2>/dev/null || true
        cd "$INFRASTRUCTURE_DIR/monitoring" && ./scripts/setup-monitoring.sh cleanup 2>/dev/null || true
        cd "$INFRASTRUCTURE_DIR/auth" && ./scripts/setup-auth.sh cleanup 2>/dev/null || true
        cd "$INFRASTRUCTURE_DIR/gateway" && ./scripts/setup-gateway.sh cleanup 2>/dev/null || true
        cd "$INFRASTRUCTURE_DIR/kafka" && ./scripts/setup-kafka.sh cleanup 2>/dev/null || true
        
        # Remove network
        docker network rm ndvn-scs-network 2>/dev/null || true
        
        log_success "All infrastructure cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# Check status of all services
check_status() {
    log_info "Checking status of all services..."
    
    echo ""
    echo -e "${BLUE}Service Health Check:${NC}"
    
    local services=(
        "Kafka:http://localhost:9092"
        "Kafka UI:http://localhost:8080"
        "Consul:http://localhost:8500"
        "API Gateway:http://localhost:8080/actuator/health"
        "Keycloak:http://localhost:8090/health/ready"
        "Prometheus:http://localhost:9090/-/healthy"
        "Grafana:http://localhost:3000/api/health"
        "Kibana:http://localhost:5601/api/status"
        "Jaeger:http://localhost:16686"
        "Development Dashboard:http://localhost:8084"
    )
    
    for service in "${services[@]}"; do
        local name=$(echo $service | cut -d: -f1)
        local url=$(echo $service | cut -d: -f2-)
        
        if curl -f "$url" > /dev/null 2>&1; then
            echo -e "  ✅ $name: ${GREEN}Healthy${NC}"
        else
            echo -e "  ❌ $name: ${RED}Unhealthy${NC}"
        fi
    done
    
    echo ""
}

# Main function
main() {
    case "${1:-start}" in
        start)
            print_banner
            check_prerequisites
            setup_kafka
            setup_gateway
            setup_auth
            setup_monitoring
            setup_development
            wait_for_services
            show_status
            ;;
        stop)
            stop_all
            ;;
        restart)
            stop_all
            sleep 10
            main start
            ;;
        status)
            check_status
            show_status
            ;;
        cleanup)
            cleanup_all
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|cleanup}"
            echo ""
            echo "Commands:"
            echo "  start    - Start all infrastructure services"
            echo "  stop     - Stop all infrastructure services"
            echo "  restart  - Restart all infrastructure services"
            echo "  status   - Check status of all services"
            echo "  cleanup  - Remove all infrastructure data and containers"
            echo ""
            echo "This script sets up the complete NDVN SCS Platform infrastructure including:"
            echo "  - Apache Kafka (Message Broker)"
            echo "  - Consul (Service Discovery)"
            echo "  - Spring Cloud Gateway (API Gateway)"
            echo "  - Keycloak (Authentication)"
            echo "  - Prometheus + Grafana (Monitoring)"
            echo "  - ELK Stack (Logging)"
            echo "  - Jaeger (Tracing)"
            echo "  - Development Tools & Databases"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
