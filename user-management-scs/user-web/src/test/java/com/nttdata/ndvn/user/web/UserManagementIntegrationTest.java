package com.nttdata.ndvn.user.web;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.nttdata.ndvn.user.application.dto.CreateUserRequest;
import com.nttdata.ndvn.user.application.dto.UserDto;
import com.nttdata.ndvn.user.domain.repository.UserRepository;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration test for User Management SCS.
 * 
 * This test verifies the complete functionality of the User Management service
 * including REST API endpoints, security, and database operations.
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class UserManagementIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private UserRepository userRepository;
    
    @Test
    @WithMockUser(authorities = {"USER_WRITE"})
    public void testCreateUser() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("newuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.username", is("newuser")))
                .andExpect(jsonPath("$.email", is("<EMAIL>")))
                .andExpect(jsonPath("$.enabled", is(true)))
                .andExpect(jsonPath("$.emailVerified", is(false)));
    }
    
    @Test
    @WithMockUser(authorities = {"USER_READ"})
    public void testGetUserByUsername() throws Exception {
        mockMvc.perform(get("/api/v1/users/username/admin"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.username", is("admin")))
                .andExpect(jsonPath("$.email", is("<EMAIL>")))
                .andExpect(jsonPath("$.enabled", is(true)));
    }
    
    @Test
    @WithMockUser(authorities = {"USER_READ"})
    public void testGetAllUsers() throws Exception {
        mockMvc.perform(get("/api/v1/users")
                .param("page", "0")
                .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThan(0))))
                .andExpect(jsonPath("$.totalElements", greaterThan(0)));
    }
    
    @Test
    @WithMockUser(authorities = {"USER_READ"})
    public void testSearchUsers() throws Exception {
        mockMvc.perform(get("/api/v1/users/search")
                .param("q", "admin"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content", hasSize(greaterThan(0))))
                .andExpect(jsonPath("$.content[0].username", containsString("admin")));
    }
    
    @Test
    public void testCheckUsernameAvailability() throws Exception {
        // Test existing username
        mockMvc.perform(get("/api/v1/users/check-username/admin"))
                .andExpect(status().isOk())
                .andExpect(content().string("false"));
        
        // Test available username
        mockMvc.perform(get("/api/v1/users/check-username/availableuser"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }
    
    @Test
    public void testCheckEmailAvailability() throws Exception {
        // Test existing email
        mockMvc.perform(get("/api/v1/users/check-email/<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(content().string("false"));
        
        // Test available email
        mockMvc.perform(get("/api/v1/users/check-email/<EMAIL>"))
                .andExpect(status().isOk())
                .andExpect(content().string("true"));
    }
    
    @Test
    @WithMockUser(authorities = {"USER_WRITE"})
    public void testCreateUserWithValidation() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername(""); // Invalid - empty username
        request.setEmail("invalid-email"); // Invalid email format
        request.setPassword("123"); // Invalid - too short
        
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest());
    }
    
    @Test
    @WithMockUser(authorities = {"USER_WRITE"})
    public void testCreateDuplicateUser() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("admin"); // Existing username
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isConflict());
    }
    
    @Test
    public void testUnauthorizedAccess() throws Exception {
        mockMvc.perform(get("/api/v1/users"))
                .andExpect(status().isUnauthorized());
    }
    
    @Test
    @WithMockUser(authorities = {"WRONG_PERMISSION"})
    public void testInsufficientPermissions() throws Exception {
        CreateUserRequest request = new CreateUserRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPassword("password123");
        
        mockMvc.perform(post("/api/v1/users")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }
    
    @Test
    public void testHealthEndpoint() throws Exception {
        mockMvc.perform(get("/actuator/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status", is("UP")));
    }
    
    @Test
    public void testSwaggerDocumentation() throws Exception {
        mockMvc.perform(get("/api-docs"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.info.title", containsString("User Management")));
    }
}
