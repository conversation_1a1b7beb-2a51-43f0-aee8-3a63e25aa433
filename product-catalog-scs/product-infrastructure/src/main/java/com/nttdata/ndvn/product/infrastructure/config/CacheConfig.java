package com.nttdata.ndvn.product.infrastructure.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis cache configuration for Product Catalog SCS.
 * 
 * This configuration sets up Redis caching with different TTL values
 * for different types of data.
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    // Cache names
    public static final String PRODUCT_CACHE = "products";
    public static final String PRODUCT_BY_SKU_CACHE = "products_by_sku";
    public static final String PRODUCT_SEARCH_CACHE = "product_search";
    public static final String CATEGORY_CACHE = "categories";
    public static final String CATEGORY_BY_SLUG_CACHE = "categories_by_slug";
    public static final String CATEGORY_TREE_CACHE = "category_tree";
    public static final String BRAND_CACHE = "brands";
    public static final String BRAND_BY_SLUG_CACHE = "brands_by_slug";
    public static final String INVENTORY_CACHE = "inventory";
    public static final String INVENTORY_SUMMARY_CACHE = "inventory_summary";
    public static final String PRODUCT_RECOMMENDATIONS_CACHE = "product_recommendations";
    public static final String POPULAR_PRODUCTS_CACHE = "popular_products";
    public static final String FEATURED_PRODUCTS_CACHE = "featured_products";
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // Product caches - 1 hour TTL
        cacheConfigurations.put(PRODUCT_CACHE, defaultConfig.entryTtl(Duration.ofHours(1)));
        cacheConfigurations.put(PRODUCT_BY_SKU_CACHE, defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // Search caches - 15 minutes TTL (more dynamic)
        cacheConfigurations.put(PRODUCT_SEARCH_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // Category caches - 2 hours TTL (less frequently changed)
        cacheConfigurations.put(CATEGORY_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        cacheConfigurations.put(CATEGORY_BY_SLUG_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        cacheConfigurations.put(CATEGORY_TREE_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // Brand caches - 2 hours TTL
        cacheConfigurations.put(BRAND_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        cacheConfigurations.put(BRAND_BY_SLUG_CACHE, defaultConfig.entryTtl(Duration.ofHours(2)));
        
        // Inventory caches - 5 minutes TTL (highly dynamic)
        cacheConfigurations.put(INVENTORY_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(5)));
        cacheConfigurations.put(INVENTORY_SUMMARY_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(10)));
        
        // Recommendation caches - 30 minutes TTL
        cacheConfigurations.put(PRODUCT_RECOMMENDATIONS_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put(POPULAR_PRODUCTS_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put(FEATURED_PRODUCTS_CACHE, defaultConfig.entryTtl(Duration.ofHours(1)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
