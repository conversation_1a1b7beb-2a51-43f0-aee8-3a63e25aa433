package com.nttdata.ndvn.product.domain.service;

import com.nttdata.ndvn.product.domain.model.Inventory;
import com.nttdata.ndvn.product.domain.repository.InventoryRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;

/**
 * Domain service for Inventory-related business logic.
 * 
 * This service encapsulates complex inventory management rules and operations.
 */
@Service
public class InventoryDomainService {
    
    private final InventoryRepository inventoryRepository;
    
    public InventoryDomainService(InventoryRepository inventoryRepository) {
        this.inventoryRepository = inventoryRepository;
    }
    
    /**
     * Validates inventory data for creation.
     */
    public void validateInventoryForCreation(Inventory inventory) {
        if (inventory.getProductId() == null) {
            throw new IllegalArgumentException("Product ID is required");
        }
        
        if (inventory.getLocationCode() == null || inventory.getLocationCode().trim().isEmpty()) {
            throw new IllegalArgumentException("Location code is required");
        }
        
        if (inventory.getLocationName() == null || inventory.getLocationName().trim().isEmpty()) {
            throw new IllegalArgumentException("Location name is required");
        }
        
        if (inventoryRepository.existsByProductIdAndLocationCode(inventory.getProductId(), inventory.getLocationCode())) {
            throw new IllegalArgumentException("Inventory already exists for this product and location");
        }
        
        validateInventoryQuantities(inventory);
        validateInventorySettings(inventory);
    }
    
    /**
     * Validates inventory data for update.
     */
    public void validateInventoryForUpdate(Inventory inventory) {
        if (inventory.getId() == null) {
            throw new IllegalArgumentException("Inventory ID is required for update");
        }
        
        if (!inventoryRepository.existsById(inventory.getId())) {
            throw new IllegalArgumentException("Inventory not found: " + inventory.getId());
        }
        
        validateInventoryQuantities(inventory);
        validateInventorySettings(inventory);
    }
    
    /**
     * Validates inventory quantities.
     */
    public void validateInventoryQuantities(Inventory inventory) {
        if (inventory.getTotalQuantity() < 0) {
            throw new IllegalArgumentException("Total quantity must be non-negative");
        }
        
        if (inventory.getAvailableQuantity() < 0) {
            throw new IllegalArgumentException("Available quantity must be non-negative");
        }
        
        if (inventory.getReservedQuantity() < 0) {
            throw new IllegalArgumentException("Reserved quantity must be non-negative");
        }
        
        if (inventory.getDamagedQuantity() < 0) {
            throw new IllegalArgumentException("Damaged quantity must be non-negative");
        }
        
        int totalCommitted = inventory.getAvailableQuantity() + 
                           inventory.getReservedQuantity() + 
                           inventory.getDamagedQuantity();
        
        if (totalCommitted > inventory.getTotalQuantity()) {
            throw new IllegalArgumentException("Sum of available, reserved, and damaged quantities cannot exceed total quantity");
        }
    }
    
    /**
     * Validates inventory settings.
     */
    public void validateInventorySettings(Inventory inventory) {
        if (inventory.getMinimumStockLevel() < 0) {
            throw new IllegalArgumentException("Minimum stock level must be non-negative");
        }
        
        if (inventory.getMaximumStockLevel() < 0) {
            throw new IllegalArgumentException("Maximum stock level must be non-negative");
        }
        
        if (inventory.getReorderPoint() < 0) {
            throw new IllegalArgumentException("Reorder point must be non-negative");
        }
        
        if (inventory.getReorderQuantity() < 0) {
            throw new IllegalArgumentException("Reorder quantity must be non-negative");
        }
        
        if (inventory.getMaximumStockLevel() < inventory.getMinimumStockLevel()) {
            throw new IllegalArgumentException("Maximum stock level cannot be less than minimum stock level");
        }
        
        if (inventory.getReorderPoint() > inventory.getMaximumStockLevel()) {
            throw new IllegalArgumentException("Reorder point cannot be greater than maximum stock level");
        }
    }
    
    /**
     * Checks if inventory can fulfill a reservation request.
     */
    public boolean canReserveStock(Inventory inventory, Integer quantity) {
        if (inventory == null || quantity == null || quantity <= 0) {
            return false;
        }
        
        if (!inventory.isTrackInventory()) {
            return true; // If not tracking inventory, assume available
        }
        
        return inventory.getAvailableQuantity() >= quantity;
    }
    
    /**
     * Checks if inventory allows backorder for a quantity.
     */
    public boolean canBackorder(Inventory inventory, Integer quantity) {
        if (inventory == null || quantity == null || quantity <= 0) {
            return false;
        }
        
        return inventory.isAllowBackorder();
    }
    
    /**
     * Calculates total available stock across all locations for a product.
     */
    public Integer calculateTotalAvailableStock(UUID productId) {
        List<Inventory> inventories = inventoryRepository.findByProductId(productId);
        return inventories.stream()
                         .filter(Inventory::isActive)
                         .mapToInt(Inventory::getAvailableQuantity)
                         .sum();
    }
    
    /**
     * Calculates total stock across all locations for a product.
     */
    public Integer calculateTotalStock(UUID productId) {
        List<Inventory> inventories = inventoryRepository.findByProductId(productId);
        return inventories.stream()
                         .filter(Inventory::isActive)
                         .mapToInt(Inventory::getTotalQuantity)
                         .sum();
    }
    
    /**
     * Finds the best location to fulfill an order based on available stock.
     */
    public Inventory findBestLocationForFulfillment(UUID productId, Integer quantity) {
        List<Inventory> inventories = inventoryRepository.findByProductId(productId);
        
        return inventories.stream()
                         .filter(Inventory::isActive)
                         .filter(inventory -> inventory.getAvailableQuantity() >= quantity)
                         .findFirst()
                         .orElse(null);
    }
    
    /**
     * Checks if a product is in stock across all locations.
     */
    public boolean isProductInStock(UUID productId) {
        return calculateTotalAvailableStock(productId) > 0;
    }
    
    /**
     * Checks if a product is low stock across all locations.
     */
    public boolean isProductLowStock(UUID productId) {
        List<Inventory> inventories = inventoryRepository.findByProductId(productId);
        
        return inventories.stream()
                         .filter(Inventory::isActive)
                         .anyMatch(Inventory::isLowStock);
    }
    
    /**
     * Checks if a product is out of stock across all locations.
     */
    public boolean isProductOutOfStock(UUID productId) {
        return calculateTotalAvailableStock(productId) == 0;
    }
    
    /**
     * Calculates inventory turnover rate.
     */
    public Double calculateInventoryTurnover(Inventory inventory, Integer soldQuantity, Integer days) {
        if (inventory == null || soldQuantity == null || days == null || days == 0) {
            return 0.0;
        }
        
        double averageInventory = (double) inventory.getTotalQuantity();
        if (averageInventory == 0) {
            return 0.0;
        }
        
        double dailySales = (double) soldQuantity / days;
        return dailySales / averageInventory;
    }
    
    /**
     * Calculates days of inventory remaining.
     */
    public Integer calculateDaysOfInventory(Inventory inventory, Integer dailySalesRate) {
        if (inventory == null || dailySalesRate == null || dailySalesRate == 0) {
            return Integer.MAX_VALUE;
        }
        
        return inventory.getAvailableQuantity() / dailySalesRate;
    }
    
    /**
     * Determines optimal reorder quantity based on sales velocity.
     */
    public Integer calculateOptimalReorderQuantity(Inventory inventory, Integer dailySalesRate, Integer leadTimeDays) {
        if (inventory == null || dailySalesRate == null || leadTimeDays == null) {
            return inventory != null ? inventory.getReorderQuantity() : 0;
        }
        
        // Safety stock = lead time demand + buffer
        int safetyStock = dailySalesRate * leadTimeDays;
        int bufferStock = (int) (safetyStock * 0.2); // 20% buffer
        
        return safetyStock + bufferStock;
    }
    
    /**
     * Validates stock movement operation.
     */
    public void validateStockMovement(Inventory inventory, Integer quantity, String movementType) {
        if (inventory == null) {
            throw new IllegalArgumentException("Inventory is required");
        }
        
        if (quantity == null || quantity <= 0) {
            throw new IllegalArgumentException("Quantity must be positive");
        }
        
        switch (movementType.toUpperCase()) {
            case "RESERVE":
                if (quantity > inventory.getAvailableQuantity()) {
                    throw new IllegalStateException("Cannot reserve more than available quantity");
                }
                break;
            case "RELEASE":
                if (quantity > inventory.getReservedQuantity()) {
                    throw new IllegalStateException("Cannot release more than reserved quantity");
                }
                break;
            case "CONFIRM_SALE":
                if (quantity > inventory.getReservedQuantity()) {
                    throw new IllegalStateException("Cannot confirm more than reserved quantity");
                }
                break;
            case "MARK_DAMAGED":
                if (quantity > inventory.getAvailableQuantity()) {
                    throw new IllegalStateException("Cannot mark more than available quantity as damaged");
                }
                break;
            case "REMOVE_DAMAGED":
                if (quantity > inventory.getDamagedQuantity()) {
                    throw new IllegalStateException("Cannot remove more than damaged quantity");
                }
                break;
            default:
                throw new IllegalArgumentException("Invalid movement type: " + movementType);
        }
    }
    
    /**
     * Checks if inventory location code is valid.
     */
    public boolean isValidLocationCode(String locationCode) {
        if (locationCode == null || locationCode.trim().isEmpty()) {
            return false;
        }
        
        // Location code should contain only alphanumeric characters and hyphens
        return locationCode.matches("^[A-Za-z0-9-]+$") && locationCode.length() <= 50;
    }
    
    /**
     * Generates a default location code.
     */
    public String generateDefaultLocationCode(String locationName) {
        if (locationName == null || locationName.trim().isEmpty()) {
            return "DEFAULT";
        }
        
        return locationName.toUpperCase()
                          .replaceAll("[^A-Z0-9\\s]", "")
                          .replaceAll("\\s+", "-")
                          .substring(0, Math.min(locationName.length(), 20));
    }
}
