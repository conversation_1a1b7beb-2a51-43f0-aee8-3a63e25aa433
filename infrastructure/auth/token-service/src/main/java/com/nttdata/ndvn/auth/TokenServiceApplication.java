package com.nttdata.ndvn.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Token Service Application for NDVN SCS Platform
 *
 * This service provides token introspection, validation, and caching
 * for the SCS platform authentication and authorization.
 *
 * Key Features:
 * - OAuth 2.0 token introspection
 * - JWT token validation and parsing
 * - Token caching for performance
 * - Integration with Keycloak
 * - Service discovery registration
 */
@SpringBootApplication
@EnableDiscoveryClient
@EnableCaching
public class TokenServiceApplication {

    public static void main(String[] args) {
        SpringApplication.run(TokenServiceApplication.class, args);
    }
}
