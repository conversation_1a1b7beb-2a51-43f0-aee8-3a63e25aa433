plugins {
    id 'java-library'
}

description = 'Product Catalog Events Layer - Event publishing and domain events'

dependencies {
    api project(':product-domain')
    
    // Kafka
    api 'org.springframework.kafka:spring-kafka'
    api "org.apache.kafka:kafka-clients:${kafkaVersion}"
    api 'io.confluent:kafka-avro-serializer:7.7.1'
    
    // JSON processing
    api 'com.fasterxml.jackson.core:jackson-databind'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Testing
    testImplementation 'org.springframework.kafka:spring-kafka-test'
    testImplementation 'org.testcontainers:kafka'
}
