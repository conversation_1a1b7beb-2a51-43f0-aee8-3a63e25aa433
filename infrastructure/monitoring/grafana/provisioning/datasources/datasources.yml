# Grafana datasource configuration for NDVN SCS Platform
apiVersion: 1

datasources:
  # Prometheus datasource
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      timeInterval: "15s"
      queryTimeout: "60s"
      httpMethod: "POST"
    secureJsonData: {}

  # Elasticsearch datasource for logs
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "logstash-*"
    editable: true
    jsonData:
      interval: "Daily"
      timeField: "@timestamp"
      esVersion: "8.11.0"
      maxConcurrentShardRequests: 5
      logMessageField: "message"
      logLevelField: "level"
    secureJsonData: {}

  # Jaeger datasource for tracing
  - name: Jaeger
    type: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: "elasticsearch"
        tags: ["traceID"]
        mappedTags:
          - key: "service.name"
            value: "service"
        mapTagNamesEnabled: true
        spanStartTimeShift: "-1h"
        spanEndTimeShift: "1h"
        filterByTraceID: true
        filterBySpanID: false
    secureJsonData: {}
