#!/bin/bash

# NDVN SCS Kafka Setup Script
# This script sets up Kafka infrastructure for the SCS platform

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
KAFKA_DIR="$(dirname "$SCRIPT_DIR")"
INFRASTRUCTURE_DIR="$(dirname "$KAFKA_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    log_info "Checking Docker Compose availability..."
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    log_success "Docker Compose is available"
}

# Start Kafka infrastructure
start_kafka() {
    log_info "Starting Kafka infrastructure..."
    
    cd "$KAFKA_DIR"
    
    # Create network if it doesn't exist
    if ! docker network ls | grep -q "ndvn-scs-network"; then
        log_info "Creating Docker network: ndvn-scs-network"
        docker network create ndvn-scs-network
    fi
    
    # Start services
    docker-compose -f docker-compose-kafka.yml up -d
    
    log_success "Kafka infrastructure started"
}

# Wait for Kafka to be ready
wait_for_kafka() {
    log_info "Waiting for Kafka to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if docker exec ndvn-kafka kafka-broker-api-versions --bootstrap-server localhost:9092 > /dev/null 2>&1; then
            log_success "Kafka is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Kafka not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Kafka failed to start within expected time"
    return 1
}

# Wait for Schema Registry to be ready
wait_for_schema_registry() {
    log_info "Waiting for Schema Registry to be ready..."
    
    local max_attempts=20
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8081/subjects > /dev/null 2>&1; then
            log_success "Schema Registry is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Schema Registry not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Schema Registry failed to start within expected time"
    return 1
}

# Register Avro schemas
register_schemas() {
    log_info "Registering Avro schemas..."
    
    local schema_dir="$KAFKA_DIR/schemas"
    
    # Register user events schema
    if [ -f "$schema_dir/user-events.avsc" ]; then
        log_info "Registering user events schema..."
        curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" \
            --data "{\"schema\":$(cat "$schema_dir/user-events.avsc" | jq -R -s .)}" \
            http://localhost:8081/subjects/user.events-value/versions
        log_success "User events schema registered"
    fi
    
    # Register customer events schema
    if [ -f "$schema_dir/customer-events.avsc" ]; then
        log_info "Registering customer events schema..."
        curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" \
            --data "{\"schema\":$(cat "$schema_dir/customer-events.avsc" | jq -R -s .)}" \
            http://localhost:8081/subjects/customer.events-value/versions
        log_success "Customer events schema registered"
    fi
    
    log_success "All schemas registered successfully"
}

# Verify Kafka setup
verify_setup() {
    log_info "Verifying Kafka setup..."
    
    # Check if topics were created
    log_info "Listing Kafka topics..."
    docker exec ndvn-kafka kafka-topics --list --bootstrap-server localhost:9092
    
    # Check Schema Registry subjects
    log_info "Listing Schema Registry subjects..."
    curl -s http://localhost:8081/subjects | jq .
    
    log_success "Kafka setup verification completed"
}

# Show status
show_status() {
    log_info "Kafka Infrastructure Status:"
    echo ""
    docker-compose -f "$KAFKA_DIR/docker-compose-kafka.yml" ps
    echo ""
    log_info "Access URLs:"
    echo "  - Kafka UI: http://localhost:8080"
    echo "  - Schema Registry: http://localhost:8081"
    echo "  - Kafka Bootstrap Servers: localhost:9092"
    echo ""
}

# Stop Kafka infrastructure
stop_kafka() {
    log_info "Stopping Kafka infrastructure..."
    cd "$KAFKA_DIR"
    docker-compose -f docker-compose-kafka.yml down
    log_success "Kafka infrastructure stopped"
}

# Clean up Kafka infrastructure
cleanup_kafka() {
    log_warning "This will remove all Kafka data and containers. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up Kafka infrastructure..."
        cd "$KAFKA_DIR"
        docker-compose -f docker-compose-kafka.yml down -v --remove-orphans
        docker volume prune -f
        log_success "Kafka infrastructure cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        start)
            check_docker
            check_docker_compose
            start_kafka
            wait_for_kafka
            wait_for_schema_registry
            register_schemas
            verify_setup
            show_status
            ;;
        stop)
            stop_kafka
            ;;
        restart)
            stop_kafka
            sleep 5
            main start
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup_kafka
            ;;
        verify)
            verify_setup
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|cleanup|verify}"
            echo ""
            echo "Commands:"
            echo "  start    - Start Kafka infrastructure"
            echo "  stop     - Stop Kafka infrastructure"
            echo "  restart  - Restart Kafka infrastructure"
            echo "  status   - Show infrastructure status"
            echo "  cleanup  - Remove all Kafka data and containers"
            echo "  verify   - Verify Kafka setup"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
