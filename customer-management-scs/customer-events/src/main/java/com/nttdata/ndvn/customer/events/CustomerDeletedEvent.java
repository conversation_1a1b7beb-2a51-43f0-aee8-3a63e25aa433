package com.nttdata.ndvn.customer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.UUID;

public class CustomerDeletedEvent {
    private UUID eventId;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    private UUID customerId;
    private String customerNumber;
    private String email;

    public CustomerDeletedEvent() {}

    private CustomerDeletedEvent(Builder builder) {
        this.eventId = builder.eventId;
        this.eventTime = builder.eventTime;
        this.customerId = builder.customerId;
        this.customerNumber = builder.customerNumber;
        this.email = builder.email;
    }

    public static Builder builder() { return new Builder(); }

    public static class Builder {
        private UUID eventId;
        private LocalDateTime eventTime;
        private UUID customerId;
        private String customerNumber;
        private String email;

        public Builder eventId(UUID eventId) { this.eventId = eventId; return this; }
        public Builder eventTime(LocalDateTime eventTime) { this.eventTime = eventTime; return this; }
        public Builder customerId(UUID customerId) { this.customerId = customerId; return this; }
        public Builder customerNumber(String customerNumber) { this.customerNumber = customerNumber; return this; }
        public Builder email(String email) { this.email = email; return this; }
        public CustomerDeletedEvent build() { return new CustomerDeletedEvent(this); }
    }

    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
}
