plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1'
    id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.nttdata.ndvn.gateway'
version = '1.0.0-SNAPSHOT'

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

repositories {
    mavenCentral()
}

ext {
    springCloudVersion = '2024.0.0'
}

dependencies {
    // Spring Cloud Gateway
    implementation 'org.springframework.cloud:spring-cloud-starter-gateway'
    
    // Service Discovery with Consul
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-discovery'
    implementation 'org.springframework.cloud:spring-cloud-starter-consul-config'
    
    // Load Balancing
    implementation 'org.springframework.cloud:spring-cloud-starter-loadbalancer'
    
    // Circuit Breaker
    implementation 'org.springframework.cloud:spring-cloud-starter-circuitbreaker-reactor-resilience4j'
    
    // Security
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-resource-server'
    
    // Monitoring and Observability
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'io.micrometer:micrometer-registry-prometheus'
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
    
    // Rate Limiting
    implementation 'org.springframework.boot:spring-boot-starter-data-redis-reactive'
    
    // JSON Processing
    implementation 'com.fasterxml.jackson.core:jackson-databind'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    
    // Validation
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    
    // Logging
    implementation 'net.logstash.logback:logstash-logback-encoder:7.4'
    
    // Testing
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.cloud:spring-cloud-starter-contract-stub-runner'
    testImplementation 'org.testcontainers:junit-jupiter'
    testImplementation 'org.testcontainers:consul'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

test {
    useJUnitPlatform()
}

// Docker build configuration
task buildDockerImage(type: Exec) {
    dependsOn build
    commandLine 'docker', 'build', '-t', 'ndvn/api-gateway:latest', '.'
}

// Application packaging
jar {
    enabled = false
}

bootJar {
    archiveFileName = 'api-gateway.jar'
}
