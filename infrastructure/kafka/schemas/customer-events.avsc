{"type": "record", "name": "CustomerEvent", "namespace": "com.nttdata.ndvn.customer.events", "doc": "Schema for customer management events", "fields": [{"name": "eventId", "type": "string", "doc": "Unique identifier for the event"}, {"name": "eventType", "type": {"type": "enum", "name": "CustomerEventType", "symbols": ["CustomerCreated", "CustomerUpdated", "CustomerDeactivated", "CustomerActivated", "CustomerAddressAdded", "CustomerAddressUpdated", "CustomerAddressRemoved", "CustomerClassificationChanged"]}, "doc": "Type of customer event"}, {"name": "eventVersion", "type": "string", "default": "1.0", "doc": "Version of the event schema"}, {"name": "timestamp", "type": {"type": "long", "logicalType": "timestamp-millis"}, "doc": "Timestamp when the event occurred"}, {"name": "source", "type": "string", "default": "customer-management-service", "doc": "Source service that generated the event"}, {"name": "correlationId", "type": ["null", "string"], "default": null, "doc": "Correlation ID for request tracing"}, {"name": "causationId", "type": ["null", "string"], "default": null, "doc": "ID of the event that caused this event"}, {"name": "data", "type": {"type": "record", "name": "CustomerEventData", "fields": [{"name": "customerId", "type": "string", "doc": "Unique identifier of the customer"}, {"name": "customerNumber", "type": "string", "doc": "Business identifier for the customer"}, {"name": "userId", "type": ["null", "string"], "default": null, "doc": "Associated user ID if customer is also a system user"}, {"name": "companyName", "type": ["null", "string"], "default": null, "doc": "Company name for business customers"}, {"name": "firstName", "type": ["null", "string"], "default": null, "doc": "First name for individual customers"}, {"name": "lastName", "type": ["null", "string"], "default": null, "doc": "Last name for individual customers"}, {"name": "email", "type": "string", "doc": "Primary email address"}, {"name": "phone", "type": ["null", "string"], "default": null, "doc": "Primary phone number"}, {"name": "status", "type": {"type": "enum", "name": "CustomerStatus", "symbols": ["ACTIVE", "INACTIVE", "SUSPENDED", "ARCHIVED"]}, "default": "ACTIVE", "doc": "Current status of the customer"}, {"name": "classification", "type": ["null", "string"], "default": null, "doc": "Customer classification or segment"}, {"name": "addresses", "type": {"type": "array", "items": {"type": "record", "name": "Address", "fields": [{"name": "addressId", "type": "string", "doc": "Unique identifier for the address"}, {"name": "type", "type": {"type": "enum", "name": "AddressType", "symbols": ["BILLING", "SHIPPING", "MAILING", "OTHER"]}, "doc": "Type of address"}, {"name": "street", "type": "string", "doc": "Street address"}, {"name": "city", "type": "string", "doc": "City"}, {"name": "state", "type": ["null", "string"], "default": null, "doc": "State or province"}, {"name": "postalCode", "type": "string", "doc": "Postal or ZIP code"}, {"name": "country", "type": "string", "doc": "Country code (ISO 3166-1 alpha-2)"}, {"name": "isPrimary", "type": "boolean", "default": false, "doc": "Whether this is the primary address for this type"}]}}, "default": [], "doc": "List of customer addresses"}, {"name": "preferences", "type": ["null", {"type": "map", "values": "string"}], "default": null, "doc": "Customer preferences and settings"}, {"name": "createdAt", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null, "doc": "Timestamp when the customer was created"}, {"name": "updatedAt", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null, "doc": "Timestamp when the customer was last updated"}]}, "doc": "Event payload data"}, {"name": "metadata", "type": ["null", {"type": "map", "values": "string"}], "default": null, "doc": "Additional metadata for the event"}]}