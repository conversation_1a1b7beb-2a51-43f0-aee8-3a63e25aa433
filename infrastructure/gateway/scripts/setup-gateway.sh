#!/bin/bash

# NDVN SCS Gateway Setup Script
# This script sets up the API Gateway and Service Discovery infrastructure

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
GATEWAY_DIR="$(dirname "$SCRIPT_DIR")"
INFRASTRUCTURE_DIR="$(dirname "$GATEWAY_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    log_info "Checking Docker Compose availability..."
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    log_success "Docker Compose is available"
}

# Check if network exists
check_network() {
    log_info "Checking Docker network..."
    if ! docker network ls | grep -q "ndvn-scs-network"; then
        log_info "Creating Docker network: ndvn-scs-network"
        docker network create ndvn-scs-network
    fi
    log_success "Docker network is ready"
}

# Build API Gateway image
build_gateway() {
    log_info "Building API Gateway Docker image..."
    
    cd "$GATEWAY_DIR/gateway-service"
    
    # Build the Spring Boot application
    if command -v ./gradlew > /dev/null 2>&1; then
        ./gradlew clean build -x test
    elif command -v gradle > /dev/null 2>&1; then
        gradle clean build -x test
    else
        log_error "Gradle not found. Please install Gradle or use the Gradle wrapper."
        exit 1
    fi
    
    # Build Docker image
    docker build -t ndvn/api-gateway:latest .
    
    log_success "API Gateway image built successfully"
}

# Generate SSL certificates for development
generate_ssl_certs() {
    log_info "Generating SSL certificates for development..."
    
    local ssl_dir="$GATEWAY_DIR/haproxy/ssl"
    mkdir -p "$ssl_dir"
    
    if [ ! -f "$ssl_dir/ndvn.pem" ]; then
        # Generate self-signed certificate for development
        openssl req -x509 -newkey rsa:4096 -keyout "$ssl_dir/ndvn.key" -out "$ssl_dir/ndvn.crt" \
            -days 365 -nodes -subj "/C=VN/ST=HCM/L=HoChiMinh/O=NTTDATA/OU=NDVN/CN=localhost"
        
        # Combine certificate and key for HAProxy
        cat "$ssl_dir/ndvn.crt" "$ssl_dir/ndvn.key" > "$ssl_dir/ndvn.pem"
        
        log_success "SSL certificates generated"
    else
        log_info "SSL certificates already exist"
    fi
}

# Start Gateway infrastructure
start_gateway() {
    log_info "Starting Gateway infrastructure..."
    
    cd "$GATEWAY_DIR"
    
    # Start services
    docker-compose -f docker-compose-gateway.yml up -d
    
    log_success "Gateway infrastructure started"
}

# Wait for Consul to be ready
wait_for_consul() {
    log_info "Waiting for Consul to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8500/v1/status/leader > /dev/null 2>&1; then
            log_success "Consul is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Consul not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Consul failed to start within expected time"
    return 1
}

# Wait for API Gateway to be ready
wait_for_gateway() {
    log_info "Waiting for API Gateway to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8080/actuator/health > /dev/null 2>&1; then
            log_success "API Gateway is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: API Gateway not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "API Gateway failed to start within expected time"
    return 1
}

# Register sample services in Consul
register_sample_services() {
    log_info "Registering sample services in Consul..."
    
    # Register User Management Service
    curl -X PUT http://localhost:8500/v1/agent/service/register \
        -d '{
            "ID": "user-management-service-1",
            "Name": "user-management-service",
            "Tags": ["user", "management", "scs"],
            "Address": "user-management-service",
            "Port": 8081,
            "Check": {
                "HTTP": "http://user-management-service:8081/actuator/health",
                "Interval": "30s",
                "Timeout": "10s"
            }
        }'
    
    # Register Customer Management Service
    curl -X PUT http://localhost:8500/v1/agent/service/register \
        -d '{
            "ID": "customer-management-service-1",
            "Name": "customer-management-service",
            "Tags": ["customer", "management", "scs"],
            "Address": "customer-management-service",
            "Port": 8082,
            "Check": {
                "HTTP": "http://customer-management-service:8082/actuator/health",
                "Interval": "30s",
                "Timeout": "10s"
            }
        }'
    
    # Register Product Catalog Service
    curl -X PUT http://localhost:8500/v1/agent/service/register \
        -d '{
            "ID": "product-catalog-service-1",
            "Name": "product-catalog-service",
            "Tags": ["product", "catalog", "scs"],
            "Address": "product-catalog-service",
            "Port": 8083,
            "Check": {
                "HTTP": "http://product-catalog-service:8083/actuator/health",
                "Interval": "30s",
                "Timeout": "10s"
            }
        }'
    
    # Register Order Management Service
    curl -X PUT http://localhost:8500/v1/agent/service/register \
        -d '{
            "ID": "order-management-service-1",
            "Name": "order-management-service",
            "Tags": ["order", "management", "scs"],
            "Address": "order-management-service",
            "Port": 8084,
            "Check": {
                "HTTP": "http://order-management-service:8084/actuator/health",
                "Interval": "30s",
                "Timeout": "10s"
            }
        }'
    
    # Register Notification Service
    curl -X PUT http://localhost:8500/v1/agent/service/register \
        -d '{
            "ID": "notification-service-1",
            "Name": "notification-service",
            "Tags": ["notification", "scs"],
            "Address": "notification-service",
            "Port": 8085,
            "Check": {
                "HTTP": "http://notification-service:8085/actuator/health",
                "Interval": "30s",
                "Timeout": "10s"
            }
        }'
    
    log_success "Sample services registered in Consul"
}

# Verify Gateway setup
verify_setup() {
    log_info "Verifying Gateway setup..."
    
    # Check Consul services
    log_info "Consul services:"
    curl -s http://localhost:8500/v1/agent/services | jq .
    
    # Check Gateway routes
    log_info "Gateway routes:"
    curl -s http://localhost:8080/actuator/gateway/routes | jq .
    
    log_success "Gateway setup verification completed"
}

# Show status
show_status() {
    log_info "Gateway Infrastructure Status:"
    echo ""
    docker-compose -f "$GATEWAY_DIR/docker-compose-gateway.yml" ps
    echo ""
    log_info "Access URLs:"
    echo "  - API Gateway: http://localhost:8080"
    echo "  - API Gateway (HTTPS): https://localhost:443"
    echo "  - Consul UI: http://localhost:8500"
    echo "  - HAProxy Stats: http://localhost:8404/stats (admin/ndvn-admin-2024)"
    echo "  - Service Dashboard: http://localhost:8090"
    echo ""
}

# Stop Gateway infrastructure
stop_gateway() {
    log_info "Stopping Gateway infrastructure..."
    cd "$GATEWAY_DIR"
    docker-compose -f docker-compose-gateway.yml down
    log_success "Gateway infrastructure stopped"
}

# Clean up Gateway infrastructure
cleanup_gateway() {
    log_warning "This will remove all Gateway data and containers. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up Gateway infrastructure..."
        cd "$GATEWAY_DIR"
        docker-compose -f docker-compose-gateway.yml down -v --remove-orphans
        docker rmi ndvn/api-gateway:latest 2>/dev/null || true
        log_success "Gateway infrastructure cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        start)
            check_docker
            check_docker_compose
            check_network
            build_gateway
            generate_ssl_certs
            start_gateway
            wait_for_consul
            wait_for_gateway
            register_sample_services
            verify_setup
            show_status
            ;;
        stop)
            stop_gateway
            ;;
        restart)
            stop_gateway
            sleep 5
            main start
            ;;
        build)
            build_gateway
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup_gateway
            ;;
        verify)
            verify_setup
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|build|status|cleanup|verify}"
            echo ""
            echo "Commands:"
            echo "  start    - Start Gateway infrastructure"
            echo "  stop     - Stop Gateway infrastructure"
            echo "  restart  - Restart Gateway infrastructure"
            echo "  build    - Build API Gateway image"
            echo "  status   - Show infrastructure status"
            echo "  cleanup  - Remove all Gateway data and containers"
            echo "  verify   - Verify Gateway setup"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
