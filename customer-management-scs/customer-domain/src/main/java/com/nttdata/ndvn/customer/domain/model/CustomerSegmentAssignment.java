package com.nttdata.ndvn.customer.domain.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * Customer segment assignment entity representing the relationship between customers and segments.
 */
@Entity
@Table(name = "customer_segment_assignments", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"customer_id", "segment_id"}),
       indexes = {
           @Index(name = "idx_customer_segment_assignment_customer", columnList = "customer_id"),
           @Index(name = "idx_customer_segment_assignment_segment", columnList = "segment_id"),
           @Index(name = "idx_customer_segment_assignment_assigned_at", columnList = "assignedAt")
       })
public class CustomerSegmentAssignment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "customer_id", nullable = false)
    private Customer customer;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "segment_id", nullable = false)
    private CustomerSegment segment;
    
    @Column(name = "assigned_at", nullable = false)
    private LocalDateTime assignedAt;
    
    @Column(name = "assigned_by", length = 100)
    private String assignedBy;
    
    // Constructors
    protected CustomerSegmentAssignment() {
        // JPA constructor
    }
    
    private CustomerSegmentAssignment(Builder builder) {
        this.customer = builder.customer;
        this.segment = builder.segment;
        this.assignedBy = builder.assignedBy;
        this.assignedAt = LocalDateTime.now();
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    // Builder pattern
    public static class Builder {
        private Customer customer;
        private CustomerSegment segment;
        private String assignedBy;
        
        public Builder customer(Customer customer) {
            this.customer = customer;
            return this;
        }
        
        public Builder segment(CustomerSegment segment) {
            this.segment = segment;
            return this;
        }
        
        public Builder assignedBy(String assignedBy) {
            this.assignedBy = assignedBy;
            return this;
        }
        
        public CustomerSegmentAssignment build() {
            Objects.requireNonNull(customer, "Customer is required");
            Objects.requireNonNull(segment, "Segment is required");
            return new CustomerSegmentAssignment(this);
        }
    }
    
    // Getters and setters
    public UUID getId() { return id; }
    public Customer getCustomer() { return customer; }
    public void setCustomer(Customer customer) { this.customer = customer; }
    public CustomerSegment getSegment() { return segment; }
    public void setSegment(CustomerSegment segment) { this.segment = segment; }
    public LocalDateTime getAssignedAt() { return assignedAt; }
    public String getAssignedBy() { return assignedBy; }
    
    @PrePersist
    protected void onCreate() {
        assignedAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerSegmentAssignment that = (CustomerSegmentAssignment) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "CustomerSegmentAssignment{" +
                "id=" + id +
                ", assignedAt=" + assignedAt +
                ", assignedBy='" + assignedBy + '\'' +
                '}';
    }
}
