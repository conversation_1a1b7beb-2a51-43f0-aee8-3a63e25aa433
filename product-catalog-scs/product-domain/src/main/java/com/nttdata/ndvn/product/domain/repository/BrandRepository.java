package com.nttdata.ndvn.product.domain.repository;

import com.nttdata.ndvn.product.domain.model.Brand;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Brand entity.
 * 
 * This interface defines the contract for brand data access operations.
 */
public interface BrandRepository {
    
    /**
     * Save a brand entity.
     */
    Brand save(Brand brand);
    
    /**
     * Find brand by ID.
     */
    Optional<Brand> findById(UUID id);
    
    /**
     * Find brand by slug.
     */
    Optional<Brand> findBySlug(String slug);
    
    /**
     * Find brand by name.
     */
    Optional<Brand> findByName(String name);
    
    /**
     * Find all brands with pagination.
     */
    Page<Brand> findAll(Pageable pageable);
    
    /**
     * Find active brands.
     */
    Page<Brand> findByActive(boolean active, Pageable pageable);
    
    /**
     * Find brands by name containing (case-insensitive).
     */
    Page<Brand> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find brands by description containing (case-insensitive).
     */
    Page<Brand> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find brands created after a specific date.
     */
    Page<Brand> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find brands updated after a specific date.
     */
    Page<Brand> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find brands ordered by sort order.
     */
    List<Brand> findAllOrderBySortOrderAsc();
    
    /**
     * Find brands ordered by name.
     */
    Page<Brand> findAllOrderByNameAsc(Pageable pageable);
    
    /**
     * Find brands ordered by creation date.
     */
    Page<Brand> findAllOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * Search brands by name or description.
     */
    Page<Brand> searchBrands(String searchTerm, Pageable pageable);
    
    /**
     * Find brands with logos.
     */
    Page<Brand> findByLogoUrlIsNotNull(Pageable pageable);
    
    /**
     * Find brands without logos.
     */
    Page<Brand> findByLogoUrlIsNull(Pageable pageable);
    
    /**
     * Find brands with websites.
     */
    Page<Brand> findByWebsiteUrlIsNotNull(Pageable pageable);
    
    /**
     * Find brands without websites.
     */
    Page<Brand> findByWebsiteUrlIsNull(Pageable pageable);
    
    /**
     * Check if slug exists.
     */
    boolean existsBySlug(String slug);
    
    /**
     * Check if name exists.
     */
    boolean existsByName(String name);
    
    /**
     * Check if brand exists by ID.
     */
    boolean existsById(UUID id);
    
    /**
     * Count active brands.
     */
    long countByActive(boolean active);
    
    /**
     * Count brands with products.
     */
    long countBrandsWithProducts();
    
    /**
     * Find brands by multiple IDs.
     */
    List<Brand> findAllById(Iterable<UUID> ids);
    
    /**
     * Find popular brands (with most products).
     */
    List<Brand> findPopularBrands(int limit);
    
    /**
     * Delete brand by ID.
     */
    void deleteById(UUID id);
    
    /**
     * Delete brand entity.
     */
    void delete(Brand brand);
    
    /**
     * Save all brands.
     */
    List<Brand> saveAll(Iterable<Brand> brands);
    
    /**
     * Get brands count.
     */
    long count();
}
