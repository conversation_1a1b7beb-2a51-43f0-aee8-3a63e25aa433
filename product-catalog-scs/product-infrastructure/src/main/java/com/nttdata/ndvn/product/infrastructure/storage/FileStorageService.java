package com.nttdata.ndvn.product.infrastructure.storage;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.UUID;

/**
 * File storage service for handling product images and documents.
 * 
 * This service provides file upload, storage, and retrieval capabilities
 * for product-related files.
 */
@Service
public class FileStorageService {
    
    private final Path fileStorageLocation;
    private final String baseUrl;
    
    public FileStorageService(@Value("${app.file.upload-dir:./uploads}") String uploadDir,
                             @Value("${app.file.base-url:http://localhost:8080/files}") String baseUrl) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        this.baseUrl = baseUrl;
        
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }
    
    /**
     * Stores a file and returns the file URL.
     */
    public String storeFile(MultipartFile file, String category) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("Cannot store empty file");
        }
        
        String originalFileName = file.getOriginalFilename();
        if (originalFileName == null) {
            throw new IllegalArgumentException("File must have a name");
        }
        
        // Validate file type
        validateFileType(originalFileName);
        
        // Generate unique filename
        String fileExtension = getFileExtension(originalFileName);
        String fileName = UUID.randomUUID().toString() + "." + fileExtension;
        
        try {
            // Create category directory if it doesn't exist
            Path categoryPath = this.fileStorageLocation.resolve(category);
            Files.createDirectories(categoryPath);
            
            // Copy file to the target location
            Path targetLocation = categoryPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
            
            // Return the file URL
            return baseUrl + "/" + category + "/" + fileName;
            
        } catch (IOException ex) {
            throw new RuntimeException("Could not store file " + fileName + ". Please try again!", ex);
        }
    }
    
    /**
     * Stores a product image.
     */
    public String storeProductImage(MultipartFile file) {
        return storeFile(file, "products/images");
    }
    
    /**
     * Stores a product thumbnail.
     */
    public String storeProductThumbnail(MultipartFile file) {
        return storeFile(file, "products/thumbnails");
    }
    
    /**
     * Stores a category image.
     */
    public String storeCategoryImage(MultipartFile file) {
        return storeFile(file, "categories/images");
    }
    
    /**
     * Stores a brand logo.
     */
    public String storeBrandLogo(MultipartFile file) {
        return storeFile(file, "brands/logos");
    }
    
    /**
     * Stores a product document.
     */
    public String storeProductDocument(MultipartFile file) {
        return storeFile(file, "products/documents");
    }
    
    /**
     * Deletes a file by URL.
     */
    public void deleteFile(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(baseUrl)) {
            return;
        }
        
        try {
            String relativePath = fileUrl.substring(baseUrl.length() + 1);
            Path filePath = this.fileStorageLocation.resolve(relativePath);
            Files.deleteIfExists(filePath);
        } catch (IOException ex) {
            throw new RuntimeException("Could not delete file: " + fileUrl, ex);
        }
    }
    
    /**
     * Checks if a file exists.
     */
    public boolean fileExists(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(baseUrl)) {
            return false;
        }
        
        try {
            String relativePath = fileUrl.substring(baseUrl.length() + 1);
            Path filePath = this.fileStorageLocation.resolve(relativePath);
            return Files.exists(filePath);
        } catch (Exception ex) {
            return false;
        }
    }
    
    /**
     * Gets file size in bytes.
     */
    public long getFileSize(String fileUrl) {
        if (fileUrl == null || !fileUrl.startsWith(baseUrl)) {
            return 0;
        }
        
        try {
            String relativePath = fileUrl.substring(baseUrl.length() + 1);
            Path filePath = this.fileStorageLocation.resolve(relativePath);
            return Files.size(filePath);
        } catch (IOException ex) {
            return 0;
        }
    }
    
    /**
     * Validates file type based on extension.
     */
    private void validateFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        
        // Allowed image extensions
        String[] allowedImageExtensions = {"jpg", "jpeg", "png", "gif", "webp", "svg"};
        
        // Allowed document extensions
        String[] allowedDocExtensions = {"pdf", "doc", "docx", "txt", "csv", "xlsx", "xls"};
        
        boolean isValidImage = false;
        boolean isValidDocument = false;
        
        for (String ext : allowedImageExtensions) {
            if (ext.equals(extension)) {
                isValidImage = true;
                break;
            }
        }
        
        for (String ext : allowedDocExtensions) {
            if (ext.equals(extension)) {
                isValidDocument = true;
                break;
            }
        }
        
        if (!isValidImage && !isValidDocument) {
            throw new IllegalArgumentException("File type not supported: " + extension);
        }
    }
    
    /**
     * Extracts file extension from filename.
     */
    private String getFileExtension(String fileName) {
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex == -1) {
            throw new IllegalArgumentException("File must have an extension");
        }
        return fileName.substring(lastDotIndex + 1);
    }
    
    /**
     * Generates a thumbnail URL from an image URL.
     */
    public String generateThumbnailUrl(String imageUrl) {
        if (imageUrl == null || !imageUrl.startsWith(baseUrl)) {
            return null;
        }
        
        // Replace /images/ with /thumbnails/ in the URL
        return imageUrl.replace("/images/", "/thumbnails/");
    }
    
    /**
     * Gets the MIME type of a file.
     */
    public String getContentType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        
        return switch (extension) {
            case "jpg", "jpeg" -> "image/jpeg";
            case "png" -> "image/png";
            case "gif" -> "image/gif";
            case "webp" -> "image/webp";
            case "svg" -> "image/svg+xml";
            case "pdf" -> "application/pdf";
            case "doc" -> "application/msword";
            case "docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document";
            case "txt" -> "text/plain";
            case "csv" -> "text/csv";
            case "xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
            case "xls" -> "application/vnd.ms-excel";
            default -> "application/octet-stream";
        };
    }
    
    /**
     * Validates file size.
     */
    public void validateFileSize(MultipartFile file, long maxSizeInBytes) {
        if (file.getSize() > maxSizeInBytes) {
            throw new IllegalArgumentException("File size exceeds maximum allowed size: " + maxSizeInBytes + " bytes");
        }
    }
    
    /**
     * Validates image dimensions (requires image processing library).
     */
    public void validateImageDimensions(MultipartFile file, int maxWidth, int maxHeight) {
        // This would require an image processing library like ImageIO or BufferedImage
        // For now, we'll just validate the file size as a proxy
        validateFileSize(file, 10 * 1024 * 1024); // 10MB max
    }
}
