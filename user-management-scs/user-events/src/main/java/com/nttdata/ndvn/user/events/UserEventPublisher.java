package com.nttdata.ndvn.user.events;

import com.nttdata.ndvn.user.domain.model.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Publisher for user-related domain events.
 * 
 * This component publishes events to Kafka topics when significant user domain
 * events occur, enabling other bounded contexts to react to user changes.
 */
@Component
public class UserEventPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(UserEventPublisher.class);
    
    // Kafka topic names
    public static final String USER_CREATED_TOPIC = "user.events.created";
    public static final String USER_UPDATED_TOPIC = "user.events.updated";
    public static final String USER_DELETED_TOPIC = "user.events.deleted";
    public static final String USER_ENABLED_TOPIC = "user.events.enabled";
    public static final String USER_DISABLED_TOPIC = "user.events.disabled";
    public static final String USER_EMAIL_VERIFIED_TOPIC = "user.events.email-verified";
    public static final String USER_PASSWORD_CHANGED_TOPIC = "user.events.password-changed";
    public static final String USER_ROLE_ASSIGNED_TOPIC = "user.events.role-assigned";
    public static final String USER_ROLE_REMOVED_TOPIC = "user.events.role-removed";
    public static final String USER_LOGIN_SUCCESS_TOPIC = "user.events.login-success";
    public static final String USER_LOGIN_FAILED_TOPIC = "user.events.login-failed";
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public UserEventPublisher(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }
    
    /**
     * Publishes a user created event.
     * 
     * @param user the created user
     */
    public void publishUserCreated(User user) {
        UserCreatedEvent event = UserCreatedEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .enabled(user.isEnabled())
                .emailVerified(user.isEmailVerified())
                .createdAt(user.getCreatedAt())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_CREATED_TOPIC, user.getId().toString(), event);
        logger.info("Published user created event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user updated event.
     * 
     * @param user the updated user
     */
    public void publishUserUpdated(User user) {
        UserUpdatedEvent event = UserUpdatedEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .enabled(user.isEnabled())
                .emailVerified(user.isEmailVerified())
                .updatedAt(user.getUpdatedAt())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_UPDATED_TOPIC, user.getId().toString(), event);
        logger.info("Published user updated event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user deleted event.
     * 
     * @param userId the deleted user ID
     * @param username the deleted user's username
     */
    public void publishUserDeleted(UUID userId, String username) {
        UserDeletedEvent event = UserDeletedEvent.builder()
                .userId(userId)
                .username(username)
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_DELETED_TOPIC, userId.toString(), event);
        logger.info("Published user deleted event for user ID: {}", userId);
    }
    
    /**
     * Publishes a user enabled event.
     * 
     * @param user the enabled user
     */
    public void publishUserEnabled(User user) {
        UserEnabledEvent event = UserEnabledEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_ENABLED_TOPIC, user.getId().toString(), event);
        logger.info("Published user enabled event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user disabled event.
     * 
     * @param user the disabled user
     */
    public void publishUserDisabled(User user) {
        UserDisabledEvent event = UserDisabledEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_DISABLED_TOPIC, user.getId().toString(), event);
        logger.info("Published user disabled event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user email verified event.
     * 
     * @param user the user whose email was verified
     */
    public void publishUserEmailVerified(User user) {
        UserEmailVerifiedEvent event = UserEmailVerifiedEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_EMAIL_VERIFIED_TOPIC, user.getId().toString(), event);
        logger.info("Published user email verified event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user password changed event.
     * 
     * @param user the user whose password was changed
     */
    public void publishUserPasswordChanged(User user) {
        UserPasswordChangedEvent event = UserPasswordChangedEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .passwordChangedAt(user.getPasswordChangedAt())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_PASSWORD_CHANGED_TOPIC, user.getId().toString(), event);
        logger.info("Published user password changed event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user role assigned event.
     * 
     * @param user the user
     * @param roleName the assigned role name
     */
    public void publishUserRoleAssigned(User user, String roleName) {
        UserRoleAssignedEvent event = UserRoleAssignedEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .roleName(roleName)
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_ROLE_ASSIGNED_TOPIC, user.getId().toString(), event);
        logger.info("Published user role assigned event for user ID: {} and role: {}", user.getId(), roleName);
    }
    
    /**
     * Publishes a user role removed event.
     * 
     * @param user the user
     * @param roleName the removed role name
     */
    public void publishUserRoleRemoved(User user, String roleName) {
        UserRoleRemovedEvent event = UserRoleRemovedEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .roleName(roleName)
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_ROLE_REMOVED_TOPIC, user.getId().toString(), event);
        logger.info("Published user role removed event for user ID: {} and role: {}", user.getId(), roleName);
    }
    
    /**
     * Publishes a user login success event.
     * 
     * @param user the user who logged in
     * @param ipAddress the IP address
     * @param userAgent the user agent
     */
    public void publishUserLoginSuccess(User user, String ipAddress, String userAgent) {
        UserLoginSuccessEvent event = UserLoginSuccessEvent.builder()
                .userId(user.getId())
                .username(user.getUsername())
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .loginTime(user.getLastLoginAt())
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_LOGIN_SUCCESS_TOPIC, user.getId().toString(), event);
        logger.info("Published user login success event for user ID: {}", user.getId());
    }
    
    /**
     * Publishes a user login failed event.
     * 
     * @param username the username that failed to login
     * @param ipAddress the IP address
     * @param userAgent the user agent
     * @param reason the failure reason
     */
    public void publishUserLoginFailed(String username, String ipAddress, String userAgent, String reason) {
        UserLoginFailedEvent event = UserLoginFailedEvent.builder()
                .username(username)
                .ipAddress(ipAddress)
                .userAgent(userAgent)
                .reason(reason)
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .build();
        
        publishEvent(USER_LOGIN_FAILED_TOPIC, username, event);
        logger.info("Published user login failed event for username: {}", username);
    }
    
    /**
     * Publishes an event to a Kafka topic.
     * 
     * @param topic the topic name
     * @param key the message key
     * @param event the event object
     */
    private void publishEvent(String topic, String key, Object event) {
        try {
            kafkaTemplate.send(topic, key, event)
                    .whenComplete((result, ex) -> {
                        if (ex != null) {
                            logger.error("Failed to publish event to topic {}: {}", topic, ex.getMessage(), ex);
                        } else {
                            logger.debug("Successfully published event to topic {} with key {}", topic, key);
                        }
                    });
        } catch (Exception e) {
            logger.error("Error publishing event to topic {}: {}", topic, e.getMessage(), e);
        }
    }
}
