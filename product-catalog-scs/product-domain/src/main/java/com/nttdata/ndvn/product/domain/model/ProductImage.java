package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * ProductImage entity representing images associated with products.
 * 
 * This entity manages product images with support for different image types,
 * sizes, and ordering.
 */
@Entity
@Table(name = "product_images", indexes = {
    @Index(name = "idx_image_product", columnList = "productId"),
    @Index(name = "idx_image_type", columnList = "imageType"),
    @Index(name = "idx_image_primary", columnList = "isPrimary"),
    @Index(name = "idx_image_sort_order", columnList = "sortOrder")
})
@Getter
@Setter
@NoArgsConstructor
public class ProductImage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotNull(message = "Product is required")
    @Column(name = "product_id", nullable = false)
    private UUID productId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;
    
    @NotBlank(message = "Image URL is required")
    @Size(max = 1000, message = "Image URL must not exceed 1000 characters")
    @Column(name = "image_url", nullable = false, length = 1000)
    private String imageUrl;
    
    @Size(max = 1000, message = "Thumbnail URL must not exceed 1000 characters")
    @Column(name = "thumbnail_url", length = 1000)
    private String thumbnailUrl;
    
    @NotBlank(message = "Alt text is required")
    @Size(max = 255, message = "Alt text must not exceed 255 characters")
    @Column(name = "alt_text", nullable = false)
    private String altText;
    
    @Size(max = 500, message = "Caption must not exceed 500 characters")
    @Column(name = "caption", length = 500)
    private String caption;
    
    @NotNull(message = "Image type is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "image_type", nullable = false)
    private ImageType imageType = ImageType.GALLERY;
    
    @Column(name = "is_primary")
    private boolean primary = false;
    
    @Column(name = "is_active")
    private boolean active = true;
    
    @Min(value = 0, message = "Sort order must be non-negative")
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Min(value = 1, message = "Width must be positive")
    @Column(name = "width")
    private Integer width;
    
    @Min(value = 1, message = "Height must be positive")
    @Column(name = "height")
    private Integer height;
    
    @Min(value = 0, message = "File size must be non-negative")
    @Column(name = "file_size")
    private Long fileSize;
    
    @Size(max = 50, message = "File format must not exceed 50 characters")
    @Column(name = "file_format", length = 50)
    private String fileFormat;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Enumeration for image types.
     */
    public enum ImageType {
        GALLERY,      // Regular product gallery image
        THUMBNAIL,    // Product thumbnail
        HERO,         // Hero/banner image
        DETAIL,       // Detail/zoom image
        VARIANT,      // Variant-specific image
        LIFESTYLE,    // Lifestyle/context image
        TECHNICAL     // Technical diagram/specification
    }
    
    // Business methods
    
    /**
     * Updates the image information.
     */
    public void updateImageInfo(String imageUrl, String thumbnailUrl, String altText, String caption) {
        this.imageUrl = imageUrl;
        this.thumbnailUrl = thumbnailUrl;
        this.altText = altText;
        this.caption = caption;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the image metadata.
     */
    public void updateMetadata(Integer width, Integer height, Long fileSize, String fileFormat) {
        this.width = width;
        this.height = height;
        this.fileSize = fileSize;
        this.fileFormat = fileFormat;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Sets this image as the primary image.
     */
    public void setAsPrimary() {
        this.primary = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Removes the primary status from this image.
     */
    public void removePrimaryStatus() {
        this.primary = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Activates the image.
     */
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates the image.
     */
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the sort order.
     */
    public void updateSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the image type.
     */
    public void updateImageType(ImageType imageType) {
        this.imageType = imageType;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Gets the aspect ratio of the image.
     */
    public Double getAspectRatio() {
        if (width != null && height != null && height > 0) {
            return (double) width / height;
        }
        return null;
    }
    
    /**
     * Checks if the image is landscape orientation.
     */
    public boolean isLandscape() {
        Double aspectRatio = getAspectRatio();
        return aspectRatio != null && aspectRatio > 1.0;
    }
    
    /**
     * Checks if the image is portrait orientation.
     */
    public boolean isPortrait() {
        Double aspectRatio = getAspectRatio();
        return aspectRatio != null && aspectRatio < 1.0;
    }
    
    /**
     * Checks if the image is square.
     */
    public boolean isSquare() {
        Double aspectRatio = getAspectRatio();
        return aspectRatio != null && Math.abs(aspectRatio - 1.0) < 0.01;
    }
    
    // Builder pattern
    public static class Builder {
        private UUID productId;
        private String imageUrl;
        private String thumbnailUrl;
        private String altText;
        private String caption;
        private ImageType imageType = ImageType.GALLERY;
        private boolean primary = false;
        private boolean active = true;
        private Integer sortOrder = 0;
        
        public Builder productId(UUID productId) {
            this.productId = productId;
            return this;
        }
        
        public Builder imageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
            return this;
        }
        
        public Builder thumbnailUrl(String thumbnailUrl) {
            this.thumbnailUrl = thumbnailUrl;
            return this;
        }
        
        public Builder altText(String altText) {
            this.altText = altText;
            return this;
        }
        
        public Builder caption(String caption) {
            this.caption = caption;
            return this;
        }
        
        public Builder imageType(ImageType imageType) {
            this.imageType = imageType;
            return this;
        }
        
        public Builder primary(boolean primary) {
            this.primary = primary;
            return this;
        }
        
        public Builder active(boolean active) {
            this.active = active;
            return this;
        }
        
        public Builder sortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
            return this;
        }
        
        public ProductImage build() {
            ProductImage image = new ProductImage();
            image.productId = this.productId;
            image.imageUrl = this.imageUrl;
            image.thumbnailUrl = this.thumbnailUrl;
            image.altText = this.altText;
            image.caption = this.caption;
            image.imageType = this.imageType;
            image.primary = this.primary;
            image.active = this.active;
            image.sortOrder = this.sortOrder;
            return image;
        }
    }
}
