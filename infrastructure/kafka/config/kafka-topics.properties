# Kafka Topics Configuration for NDVN SCS Platform
# This file defines the topic configurations for all SCS services

# Default topic configuration
default.partitions=3
default.replication.factor=1
default.retention.ms=604800000
default.segment.ms=86400000
default.cleanup.policy=delete

# User Management Service Topics
user.events.partitions=3
user.events.replication.factor=1
user.events.retention.ms=2592000000
user.events.cleanup.policy=delete
user.events.compression.type=snappy

user.commands.partitions=3
user.commands.replication.factor=1
user.commands.retention.ms=86400000
user.commands.cleanup.policy=delete

user.dlq.partitions=1
user.dlq.replication.factor=1
user.dlq.retention.ms=2592000000
user.dlq.cleanup.policy=delete

# Customer Management Service Topics
customer.events.partitions=3
customer.events.replication.factor=1
customer.events.retention.ms=2592000000
customer.events.cleanup.policy=delete
customer.events.compression.type=snappy

customer.commands.partitions=3
customer.commands.replication.factor=1
customer.commands.retention.ms=86400000
customer.commands.cleanup.policy=delete

customer.dlq.partitions=1
customer.dlq.replication.factor=1
customer.dlq.retention.ms=2592000000
customer.dlq.cleanup.policy=delete

# Product Catalog Service Topics
catalog.events.partitions=3
catalog.events.replication.factor=1
catalog.events.retention.ms=2592000000
catalog.events.cleanup.policy=delete
catalog.events.compression.type=snappy

catalog.commands.partitions=3
catalog.commands.replication.factor=1
catalog.commands.retention.ms=86400000
catalog.commands.cleanup.policy=delete

catalog.dlq.partitions=1
catalog.dlq.replication.factor=1
catalog.dlq.retention.ms=2592000000
catalog.dlq.cleanup.policy=delete

inventory.events.partitions=3
inventory.events.replication.factor=1
inventory.events.retention.ms=1209600000
inventory.events.cleanup.policy=delete
inventory.events.compression.type=snappy

# Order Management Service Topics
order.events.partitions=3
order.events.replication.factor=1
order.events.retention.ms=7776000000
order.events.cleanup.policy=delete
order.events.compression.type=snappy

order.commands.partitions=3
order.commands.replication.factor=1
order.commands.retention.ms=86400000
order.commands.cleanup.policy=delete

order.dlq.partitions=1
order.dlq.replication.factor=1
order.dlq.retention.ms=2592000000
order.dlq.cleanup.policy=delete

# Notification Service Topics
notification.events.partitions=3
notification.events.replication.factor=1
notification.events.retention.ms=2592000000
notification.events.cleanup.policy=delete
notification.events.compression.type=snappy

notification.commands.partitions=3
notification.commands.replication.factor=1
notification.commands.retention.ms=86400000
notification.commands.cleanup.policy=delete

notification.dlq.partitions=1
notification.dlq.replication.factor=1
notification.dlq.retention.ms=2592000000
notification.dlq.cleanup.policy=delete

# System Topics
system.audit.partitions=1
system.audit.replication.factor=1
system.audit.retention.ms=31536000000
system.audit.cleanup.policy=delete
system.audit.compression.type=gzip

system.metrics.partitions=1
system.metrics.replication.factor=1
system.metrics.retention.ms=604800000
system.metrics.cleanup.policy=delete
system.metrics.compression.type=snappy

# Consumer Group Configuration
consumer.group.session.timeout.ms=30000
consumer.group.heartbeat.interval.ms=3000
consumer.group.max.poll.interval.ms=300000
consumer.group.max.poll.records=500
consumer.group.auto.offset.reset=earliest
consumer.group.enable.auto.commit=false

# Producer Configuration
producer.acks=1
producer.retries=3
producer.batch.size=16384
producer.linger.ms=5
producer.buffer.memory=33554432
producer.compression.type=snappy
producer.max.in.flight.requests.per.connection=5
producer.enable.idempotence=true
