package com.nttdata.ndvn.customer.domain.model;

/**
 * Enumeration representing the type of address.
 */
public enum AddressType {
    /**
     * Billing address for invoices and payments.
     */
    BILLING,
    
    /**
     * Shipping address for deliveries.
     */
    SHIPPING,
    
    /**
     * Mailing address for correspondence.
     */
    MAILING,
    
    /**
     * Other type of address.
     */
    OTHER
}
