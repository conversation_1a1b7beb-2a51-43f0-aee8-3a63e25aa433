dependencies {
    // Domain module dependency
    implementation project(':ndvn-terasoluna-base-domain')
    
    // Terasoluna dependencies for data access
    implementation 'org.terasoluna.gfw:terasoluna-gfw-common-dependencies'
    
    // Choose one of the following data access technologies:
    
    // For MyBatis (uncomment if using MyBatis)
    // implementation 'org.terasoluna.gfw:terasoluna-gfw-mybatis3-dependencies'
    
    // For JPA (uncomment if using JPA)
    implementation 'org.terasoluna.gfw:terasoluna-gfw-jpa-dependencies'
    
    // Database connection pool
    implementation 'org.apache.commons:commons-dbcp2'
    
    // Database drivers (add as needed)
    implementation 'com.h2database:h2' // For H2 database
    // implementation 'org.postgresql:postgresql' // For PostgreSQL
    // implementation 'com.mysql:mysql-connector-j' // For MySQL
}
