package com.nttdata.ndvn.user.domain.service;

import com.nttdata.ndvn.user.domain.model.Role;
import com.nttdata.ndvn.user.domain.model.User;
import com.nttdata.ndvn.user.domain.repository.RoleRepository;
import com.nttdata.ndvn.user.domain.repository.UserRepository;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Domain service for User aggregate business logic.
 * 
 * This service encapsulates complex business rules and operations that involve
 * multiple entities or require coordination between different parts of the domain.
 */
@Service
public class UserDomainService {
    
    private final UserRepository userRepository;
    private final RoleRepository roleRepository;
    private final PasswordEncoder passwordEncoder;
    
    public UserDomainService(UserRepository userRepository, 
                           RoleRepository roleRepository,
                           PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.passwordEncoder = passwordEncoder;
    }
    
    /**
     * Creates a new user with the specified details and default role.
     * 
     * @param username the username
     * @param email the email address
     * @param rawPassword the raw password
     * @return the created user
     * @throws IllegalArgumentException if username or email already exists
     */
    public User createUser(String username, String email, String rawPassword) {
        validateUserCreation(username, email);
        
        String encodedPassword = passwordEncoder.encode(rawPassword);
        User user = new User(username, email, encodedPassword);
        
        // Assign default USER role
        Role userRole = roleRepository.findByName("USER")
            .orElseThrow(() -> new IllegalStateException("Default USER role not found"));
        user.assignRole(userRole);
        
        return userRepository.save(user);
    }
    
    /**
     * Creates a new user with specified roles.
     * 
     * @param username the username
     * @param email the email address
     * @param rawPassword the raw password
     * @param roleNames the names of roles to assign
     * @return the created user
     * @throws IllegalArgumentException if username or email already exists or roles not found
     */
    public User createUserWithRoles(String username, String email, String rawPassword, Set<String> roleNames) {
        validateUserCreation(username, email);
        
        String encodedPassword = passwordEncoder.encode(rawPassword);
        User user = new User(username, email, encodedPassword);
        
        // Assign specified roles
        for (String roleName : roleNames) {
            Role role = roleRepository.findByName(roleName)
                .orElseThrow(() -> new IllegalArgumentException("Role not found: " + roleName));
            user.assignRole(role);
        }
        
        return userRepository.save(user);
    }
    
    /**
     * Changes a user's password after validating the current password.
     * 
     * @param user the user
     * @param currentPassword the current password
     * @param newPassword the new password
     * @throws IllegalArgumentException if current password is incorrect
     */
    public void changePassword(User user, String currentPassword, String newPassword) {
        if (!passwordEncoder.matches(currentPassword, user.getPasswordHash())) {
            throw new IllegalArgumentException("Current password is incorrect");
        }
        
        String encodedNewPassword = passwordEncoder.encode(newPassword);
        user.changePassword(encodedNewPassword);
        userRepository.save(user);
    }
    
    /**
     * Resets a user's password (admin operation).
     * 
     * @param user the user
     * @param newPassword the new password
     */
    public void resetPassword(User user, String newPassword) {
        String encodedPassword = passwordEncoder.encode(newPassword);
        user.changePassword(encodedPassword);
        userRepository.save(user);
    }
    
    /**
     * Authenticates a user with username/email and password.
     * 
     * @param usernameOrEmail the username or email
     * @param password the password
     * @return the authenticated user
     * @throws IllegalArgumentException if authentication fails
     */
    public User authenticate(String usernameOrEmail, String password) {
        User user = findUserByUsernameOrEmail(usernameOrEmail);
        
        if (!user.isEnabled()) {
            user.recordFailedLogin();
            userRepository.save(user);
            throw new IllegalArgumentException("User account is disabled");
        }
        
        if (!user.isAccountNonLocked()) {
            throw new IllegalArgumentException("User account is locked");
        }
        
        if (!passwordEncoder.matches(password, user.getPasswordHash())) {
            user.recordFailedLogin();
            userRepository.save(user);
            throw new IllegalArgumentException("Invalid credentials");
        }
        
        user.recordSuccessfulLogin();
        userRepository.save(user);
        
        return user;
    }
    
    /**
     * Assigns a role to a user.
     * 
     * @param user the user
     * @param roleName the role name
     * @throws IllegalArgumentException if role not found
     */
    public void assignRole(User user, String roleName) {
        Role role = roleRepository.findByName(roleName)
            .orElseThrow(() -> new IllegalArgumentException("Role not found: " + roleName));
        
        user.assignRole(role);
        userRepository.save(user);
    }
    
    /**
     * Removes a role from a user.
     * 
     * @param user the user
     * @param roleName the role name
     */
    public void removeRole(User user, String roleName) {
        Role role = roleRepository.findByName(roleName).orElse(null);
        if (role != null) {
            user.removeRole(role);
            userRepository.save(user);
        }
    }
    
    /**
     * Locks a user account.
     * 
     * @param user the user to lock
     */
    public void lockUser(User user) {
        user.disable();
        userRepository.save(user);
    }
    
    /**
     * Unlocks a user account.
     * 
     * @param user the user to unlock
     */
    public void unlockUser(User user) {
        user.unlockAccount();
        user.enable();
        userRepository.save(user);
    }
    
    /**
     * Verifies a user's email address.
     * 
     * @param user the user
     */
    public void verifyEmail(User user) {
        user.verifyEmail();
        userRepository.save(user);
    }
    
    /**
     * Checks if a username is available.
     * 
     * @param username the username to check
     * @return true if available, false otherwise
     */
    public boolean isUsernameAvailable(String username) {
        return !userRepository.existsByUsername(username);
    }
    
    /**
     * Checks if an email is available.
     * 
     * @param email the email to check
     * @return true if available, false otherwise
     */
    public boolean isEmailAvailable(String email) {
        return !userRepository.existsByEmail(email);
    }
    
    /**
     * Finds inactive users (haven't logged in for specified days).
     * 
     * @param days the number of days
     * @return count of inactive users
     */
    public long countInactiveUsers(int days) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(days);
        return userRepository.countByCreatedAtAfter(cutoffDate);
    }
    
    /**
     * Validates user creation requirements.
     * 
     * @param username the username
     * @param email the email
     * @throws IllegalArgumentException if validation fails
     */
    private void validateUserCreation(String username, String email) {
        if (userRepository.existsByUsername(username)) {
            throw new IllegalArgumentException("Username already exists: " + username);
        }
        
        if (userRepository.existsByEmail(email)) {
            throw new IllegalArgumentException("Email already exists: " + email);
        }
    }
    
    /**
     * Finds a user by username or email.
     * 
     * @param usernameOrEmail the username or email
     * @return the user
     * @throws IllegalArgumentException if user not found
     */
    private User findUserByUsernameOrEmail(String usernameOrEmail) {
        // Try to find by username first
        return userRepository.findByUsername(usernameOrEmail)
            .orElseGet(() -> userRepository.findByEmail(usernameOrEmail)
                .orElseThrow(() -> new IllegalArgumentException("User not found: " + usernameOrEmail)));
    }
}
