package com.nttdata.ndvn.product.application.dto;

import jakarta.validation.constraints.*;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Data Transfer Object for Inventory.
 */
public class InventoryDto {
    
    private UUID id;
    
    @NotNull(message = "Product is required")
    private UUID productId;
    
    private UUID variantId;
    
    @NotBlank(message = "Location code is required")
    @Size(max = 50, message = "Location code must not exceed 50 characters")
    private String locationCode;
    
    @NotBlank(message = "Location name is required")
    @Size(max = 255, message = "Location name must not exceed 255 characters")
    private String locationName;
    
    @NotNull(message = "Total quantity is required")
    @Min(value = 0, message = "Total quantity must be non-negative")
    private Integer totalQuantity;
    
    @NotNull(message = "Available quantity is required")
    @Min(value = 0, message = "Available quantity must be non-negative")
    private Integer availableQuantity;
    
    @Min(value = 0, message = "Reserved quantity must be non-negative")
    private Integer reservedQuantity;
    
    @Min(value = 0, message = "Damaged quantity must be non-negative")
    private Integer damagedQuantity;
    
    @Min(value = 0, message = "Minimum stock level must be non-negative")
    private Integer minimumStockLevel;
    
    @Min(value = 0, message = "Maximum stock level must be non-negative")
    private Integer maximumStockLevel;
    
    @Min(value = 0, message = "Reorder point must be non-negative")
    private Integer reorderPoint;
    
    @Min(value = 0, message = "Reorder quantity must be non-negative")
    private Integer reorderQuantity;
    
    private boolean trackInventory;
    private boolean allowBackorder;
    private boolean active;
    private LocalDateTime lastStockCheck;
    private String notes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Computed fields
    private boolean inStock;
    private boolean lowStock;
    private boolean outOfStock;
    private boolean needsReorder;
    private Integer committedQuantity;
    private Double stockUtilization;
    
    // Constructors
    public InventoryDto() {}
    
    // Getters and setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }
    
    public UUID getProductId() { return productId; }
    public void setProductId(UUID productId) { this.productId = productId; }
    
    public UUID getVariantId() { return variantId; }
    public void setVariantId(UUID variantId) { this.variantId = variantId; }
    
    public String getLocationCode() { return locationCode; }
    public void setLocationCode(String locationCode) { this.locationCode = locationCode; }
    
    public String getLocationName() { return locationName; }
    public void setLocationName(String locationName) { this.locationName = locationName; }
    
    public Integer getTotalQuantity() { return totalQuantity; }
    public void setTotalQuantity(Integer totalQuantity) { this.totalQuantity = totalQuantity; }
    
    public Integer getAvailableQuantity() { return availableQuantity; }
    public void setAvailableQuantity(Integer availableQuantity) { this.availableQuantity = availableQuantity; }
    
    public Integer getReservedQuantity() { return reservedQuantity; }
    public void setReservedQuantity(Integer reservedQuantity) { this.reservedQuantity = reservedQuantity; }
    
    public Integer getDamagedQuantity() { return damagedQuantity; }
    public void setDamagedQuantity(Integer damagedQuantity) { this.damagedQuantity = damagedQuantity; }
    
    public Integer getMinimumStockLevel() { return minimumStockLevel; }
    public void setMinimumStockLevel(Integer minimumStockLevel) { this.minimumStockLevel = minimumStockLevel; }
    
    public Integer getMaximumStockLevel() { return maximumStockLevel; }
    public void setMaximumStockLevel(Integer maximumStockLevel) { this.maximumStockLevel = maximumStockLevel; }
    
    public Integer getReorderPoint() { return reorderPoint; }
    public void setReorderPoint(Integer reorderPoint) { this.reorderPoint = reorderPoint; }
    
    public Integer getReorderQuantity() { return reorderQuantity; }
    public void setReorderQuantity(Integer reorderQuantity) { this.reorderQuantity = reorderQuantity; }
    
    public boolean isTrackInventory() { return trackInventory; }
    public void setTrackInventory(boolean trackInventory) { this.trackInventory = trackInventory; }
    
    public boolean isAllowBackorder() { return allowBackorder; }
    public void setAllowBackorder(boolean allowBackorder) { this.allowBackorder = allowBackorder; }
    
    public boolean isActive() { return active; }
    public void setActive(boolean active) { this.active = active; }
    
    public LocalDateTime getLastStockCheck() { return lastStockCheck; }
    public void setLastStockCheck(LocalDateTime lastStockCheck) { this.lastStockCheck = lastStockCheck; }
    
    public String getNotes() { return notes; }
    public void setNotes(String notes) { this.notes = notes; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public boolean isInStock() { return inStock; }
    public void setInStock(boolean inStock) { this.inStock = inStock; }
    
    public boolean isLowStock() { return lowStock; }
    public void setLowStock(boolean lowStock) { this.lowStock = lowStock; }
    
    public boolean isOutOfStock() { return outOfStock; }
    public void setOutOfStock(boolean outOfStock) { this.outOfStock = outOfStock; }
    
    public boolean isNeedsReorder() { return needsReorder; }
    public void setNeedsReorder(boolean needsReorder) { this.needsReorder = needsReorder; }
    
    public Integer getCommittedQuantity() { return committedQuantity; }
    public void setCommittedQuantity(Integer committedQuantity) { this.committedQuantity = committedQuantity; }
    
    public Double getStockUtilization() { return stockUtilization; }
    public void setStockUtilization(Double stockUtilization) { this.stockUtilization = stockUtilization; }
}
