# Integration Patterns and Communication Design

## Overview

This document defines the integration patterns, event schemas, API contracts, and communication protocols for the NDVN Terasoluna Base SCS architecture. The design emphasizes loose coupling, eventual consistency, and resilient communication between services.

## Communication Patterns

### 1. Event-Driven Communication (Primary)

#### Message Broker: Apache Kafka
- **Cluster Setup:** 3-node Kafka cluster for high availability
- **Topics:** Domain-specific topics with multiple partitions
- **Retention:** 7 days for events, 30 days for audit events
- **Serialization:** JSON with Avro schema evolution support

#### Event Categories

**Domain Events:** Business events that represent state changes
- Published after successful database transactions
- Immutable and append-only
- Include complete event data to minimize downstream API calls

**Integration Events:** Cross-service coordination events
- Used for workflow orchestration
- May include correlation IDs for tracking
- Support retry and dead letter queue patterns

**System Events:** Technical events for monitoring and operations
- Service health and performance metrics
- Error and exception notifications
- Audit and compliance events

### 2. Synchronous API Communication (Secondary)

#### REST API Standards
- **Protocol:** HTTP/HTTPS with TLS 1.3
- **Format:** JSON with OpenAPI 3.0 specifications
- **Authentication:** JWT Bearer tokens
- **Versioning:** URL path versioning (e.g., `/api/v1/`)
- **Error Handling:** RFC 7807 Problem Details format

#### API Gateway Pattern
- **Gateway:** Spring Cloud Gateway
- **Routing:** Path-based routing to services
- **Cross-Cutting:** Authentication, rate limiting, CORS
- **Aggregation:** Limited aggregation for UI convenience

## Event Schemas and Contracts

### User Management Events

#### UserCreatedEvent
```json
{
  "eventId": "uuid",
  "eventType": "UserCreated",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "user-management-service",
  "correlationId": "uuid",
  "data": {
    "userId": "uuid",
    "username": "string",
    "email": "string",
    "roles": ["string"],
    "enabled": true,
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

#### UserUpdatedEvent
```json
{
  "eventId": "uuid",
  "eventType": "UserUpdated",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "user-management-service",
  "correlationId": "uuid",
  "data": {
    "userId": "uuid",
    "username": "string",
    "email": "string",
    "roles": ["string"],
    "enabled": true,
    "updatedAt": "2024-01-15T10:30:00Z",
    "changes": {
      "email": {
        "oldValue": "<EMAIL>",
        "newValue": "<EMAIL>"
      }
    }
  }
}
```

### Customer Management Events

#### CustomerCreatedEvent
```json
{
  "eventId": "uuid",
  "eventType": "CustomerCreated",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "customer-management-service",
  "correlationId": "uuid",
  "data": {
    "customerId": "uuid",
    "customerNumber": "string",
    "userId": "uuid",
    "companyName": "string",
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "phone": "string",
    "status": "ACTIVE",
    "addresses": [
      {
        "type": "BILLING",
        "street": "string",
        "city": "string",
        "state": "string",
        "postalCode": "string",
        "country": "string",
        "isPrimary": true
      }
    ],
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

### Product Catalog Events

#### ProductCreatedEvent
```json
{
  "eventId": "uuid",
  "eventType": "ProductCreated",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "product-catalog-service",
  "correlationId": "uuid",
  "data": {
    "productId": "uuid",
    "sku": "string",
    "name": "string",
    "description": "string",
    "categoryId": "uuid",
    "categoryName": "string",
    "price": 99.99,
    "currency": "USD",
    "status": "ACTIVE",
    "inventory": {
      "available": 100,
      "reserved": 0
    },
    "createdAt": "2024-01-15T10:30:00Z"
  }
}
```

#### InventoryUpdatedEvent
```json
{
  "eventId": "uuid",
  "eventType": "InventoryUpdated",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "product-catalog-service",
  "correlationId": "uuid",
  "data": {
    "productId": "uuid",
    "sku": "string",
    "previousQuantity": 100,
    "newQuantity": 95,
    "changeReason": "SALE",
    "reservationId": "uuid",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Order Management Events

#### OrderPlacedEvent
```json
{
  "eventId": "uuid",
  "eventType": "OrderPlaced",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "order-management-service",
  "correlationId": "uuid",
  "data": {
    "orderId": "uuid",
    "orderNumber": "string",
    "customerId": "uuid",
    "customerEmail": "string",
    "orderDate": "2024-01-15T10:30:00Z",
    "status": "PLACED",
    "totalAmount": 299.99,
    "currency": "USD",
    "items": [
      {
        "productId": "uuid",
        "sku": "string",
        "productName": "string",
        "quantity": 2,
        "unitPrice": 149.99,
        "totalPrice": 299.98
      }
    ],
    "shippingAddress": {
      "street": "string",
      "city": "string",
      "state": "string",
      "postalCode": "string",
      "country": "string"
    }
  }
}
```

### Notification Events

#### NotificationSentEvent
```json
{
  "eventId": "uuid",
  "eventType": "NotificationSent",
  "eventVersion": "1.0",
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "notification-service",
  "correlationId": "uuid",
  "data": {
    "notificationId": "uuid",
    "recipientId": "uuid",
    "recipientEmail": "string",
    "channel": "EMAIL",
    "templateId": "uuid",
    "templateName": "string",
    "subject": "string",
    "status": "SENT",
    "sentAt": "2024-01-15T10:30:00Z",
    "relatedEntityType": "ORDER",
    "relatedEntityId": "uuid"
  }
}
```

## API Contracts

### User Management API

#### Authentication Endpoints
```yaml
/api/v1/auth/login:
  post:
    summary: Authenticate user
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              username:
                type: string
              password:
                type: string
    responses:
      200:
        description: Authentication successful
        content:
          application/json:
            schema:
              type: object
              properties:
                accessToken:
                  type: string
                refreshToken:
                  type: string
                expiresIn:
                  type: integer
                user:
                  $ref: '#/components/schemas/User'

/api/v1/auth/refresh:
  post:
    summary: Refresh access token
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              refreshToken:
                type: string
```

#### User Management Endpoints
```yaml
/api/v1/users/{userId}:
  get:
    summary: Get user by ID
    parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: string
          format: uuid
    responses:
      200:
        description: User found
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
      404:
        description: User not found

/api/v1/users/{userId}/roles:
  get:
    summary: Get user roles
    responses:
      200:
        description: User roles
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/Role'
```

### Customer Management API

```yaml
/api/v1/customers:
  get:
    summary: Search customers
    parameters:
      - name: query
        in: query
        schema:
          type: string
      - name: page
        in: query
        schema:
          type: integer
          default: 0
      - name: size
        in: query
        schema:
          type: integer
          default: 20
    responses:
      200:
        description: Customer search results
        content:
          application/json:
            schema:
              type: object
              properties:
                content:
                  type: array
                  items:
                    $ref: '#/components/schemas/Customer'
                totalElements:
                  type: integer
                totalPages:
                  type: integer

  post:
    summary: Create customer
    requestBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateCustomerRequest'
    responses:
      201:
        description: Customer created
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Customer'
```

### Product Catalog API

```yaml
/api/v1/products:
  get:
    summary: Search products
    parameters:
      - name: category
        in: query
        schema:
          type: string
      - name: query
        in: query
        schema:
          type: string
      - name: page
        in: query
        schema:
          type: integer
          default: 0
    responses:
      200:
        description: Product search results

/api/v1/products/{productId}/inventory:
  get:
    summary: Get product inventory
    responses:
      200:
        description: Inventory information
        content:
          application/json:
            schema:
              type: object
              properties:
                productId:
                  type: string
                available:
                  type: integer
                reserved:
                  type: integer
                lastUpdated:
                  type: string
                  format: date-time

  post:
    summary: Reserve inventory
    requestBody:
      required: true
      content:
        application/json:
          schema:
            type: object
            properties:
              quantity:
                type: integer
              reservationId:
                type: string
    responses:
      200:
        description: Inventory reserved
```

## Integration Patterns Implementation

### Event Publishing Pattern

#### Spring Boot Event Publisher
```java
@Component
public class CustomerEventPublisher {
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    private final ObjectMapper objectMapper;
    
    public void publishCustomerCreated(Customer customer) {
        CustomerCreatedEvent event = CustomerCreatedEvent.builder()
            .eventId(UUID.randomUUID().toString())
            .eventType("CustomerCreated")
            .eventVersion("1.0")
            .timestamp(Instant.now())
            .source("customer-management-service")
            .correlationId(MDC.get("correlationId"))
            .data(mapToEventData(customer))
            .build();
            
        kafkaTemplate.send("customer.events", customer.getId().toString(), event);
    }
}
```

### Event Consumption Pattern

#### Spring Kafka Listener
```java
@Component
public class OrderEventConsumer {
    
    private final NotificationApplicationService notificationService;
    
    @KafkaListener(topics = "order.events", groupId = "notification-service")
    public void handleOrderEvent(
        @Payload OrderPlacedEvent event,
        @Header Map<String, Object> headers) {
        
        try {
            MDC.put("correlationId", event.getCorrelationId());
            
            switch (event.getEventType()) {
                case "OrderPlaced":
                    notificationService.sendOrderConfirmation(event.getData());
                    break;
                case "OrderShipped":
                    notificationService.sendShippingNotification(event.getData());
                    break;
            }
        } catch (Exception e) {
            // Handle error and potentially send to DLQ
            log.error("Error processing order event", e);
            throw e;
        } finally {
            MDC.clear();
        }
    }
}
```

### Circuit Breaker Pattern

#### Resilience4j Implementation
```java
@Component
public class CustomerServiceClient {
    
    private final WebClient webClient;
    private final CircuitBreaker circuitBreaker;
    
    @CircuitBreaker(name = "customer-service", fallbackMethod = "getCustomerFallback")
    @Retry(name = "customer-service")
    @TimeLimiter(name = "customer-service")
    public CompletableFuture<Customer> getCustomer(String customerId) {
        return webClient.get()
            .uri("/api/v1/customers/{id}", customerId)
            .retrieve()
            .bodyToMono(Customer.class)
            .toFuture();
    }
    
    public CompletableFuture<Customer> getCustomerFallback(String customerId, Exception ex) {
        // Return cached data or default response
        return CompletableFuture.completedFuture(Customer.builder()
            .id(customerId)
            .name("Customer information temporarily unavailable")
            .build());
    }
}
```

## Error Handling and Resilience

### Dead Letter Queue Pattern
- Failed events are sent to DLQ topics
- Manual review and reprocessing capabilities
- Alerting on DLQ message accumulation

### Saga Pattern for Distributed Transactions
- Choreography-based sagas using events
- Compensation events for rollback scenarios
- Saga state tracking and monitoring

### API Error Handling
- Consistent error response format (RFC 7807)
- Correlation IDs for request tracing
- Structured error logging

This integration design ensures reliable, scalable communication between SCS services while maintaining loose coupling and system resilience.
