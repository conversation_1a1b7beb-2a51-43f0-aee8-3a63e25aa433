# NDVN SCS Platform Infrastructure

This directory contains the complete infrastructure setup for the NDVN Self-Contained Systems (SCS) platform, providing all the necessary components for developing, testing, and running the distributed architecture.

## 🏗️ Architecture Overview

The infrastructure is organized into several key components:

```
infrastructure/
├── kafka/              # Message Broker (Apache Kafka)
├── gateway/            # API Gateway & Service Discovery (Spring Cloud Gateway + Consul)
├── auth/               # Authentication & Authorization (Keycloak + OAuth 2.0)
├── monitoring/         # Observability Stack (Prometheus, Grafana, ELK, Jaeger)
├── development/        # Development Environment (Databases, Tools, Utilities)
└── scripts/            # Master orchestration scripts
```

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+ with at least 4GB RAM allocated
- Docker Compose 2.0+
- Available ports: 80, 443, 3000, 5432-5436, 6379, 8080-8090, 9000-9093, 9200, 16686

### Start Everything
```bash
# Start the complete infrastructure
./infrastructure/scripts/start-all.sh

# Check status of all services
./infrastructure/scripts/start-all.sh status

# Stop all services
./infrastructure/scripts/start-all.sh stop
```

### Individual Component Setup
```bash
# Message Broker
./infrastructure/kafka/scripts/setup-kafka.sh start

# API Gateway & Service Discovery
./infrastructure/gateway/scripts/setup-gateway.sh start

# Authentication
./infrastructure/auth/scripts/setup-auth.sh start

# Monitoring & Observability
./infrastructure/monitoring/scripts/setup-monitoring.sh start

# Development Environment
./infrastructure/development/scripts/setup-dev-env.sh start
```

## 📊 Service Access Points

### Core Infrastructure
| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **API Gateway** | http://localhost:8080 | - | Central entry point for all SCS services |
| **Consul UI** | http://localhost:8500 | - | Service discovery and configuration |
| **Kafka UI** | http://localhost:8080 | - | Message broker management |
| **Keycloak Admin** | http://localhost:8090/admin | admin/admin-password | Authentication server |

### Monitoring & Observability
| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **Grafana** | http://localhost:3000 | admin/admin123 | Metrics dashboards |
| **Prometheus** | http://localhost:9090 | - | Metrics collection |
| **Kibana** | http://localhost:5601 | - | Log analysis and visualization |
| **Jaeger** | http://localhost:16686 | - | Distributed tracing |
| **AlertManager** | http://localhost:9093 | - | Alert management |

### Development Tools
| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **Development Dashboard** | http://localhost:8084 | - | Central development portal |
| **Adminer** | http://localhost:8082 | - | Database management |
| **Redis Commander** | http://localhost:8083 | - | Redis management |
| **MailHog** | http://localhost:8025 | - | Email testing |
| **MinIO Console** | http://localhost:9001 | minioadmin/minioadmin123 | Object storage |

### Database Connections
| Database | Host | Port | Username | Password | Database Name |
|----------|------|------|----------|----------|---------------|
| **User Management** | localhost | 5432 | user_service | password | user_management |
| **Customer Management** | localhost | 5433 | customer_service | password | customer_management |
| **Product Catalog** | localhost | 5434 | catalog_service | password | product_catalog |
| **Order Management** | localhost | 5435 | order_service | password | order_management |
| **Notification** | localhost | 5436 | notification_service | password | notification_service |

## 🛠️ Development Workflow

### Access Development Tools
```bash
# Enter the development container
docker exec -it ndvn-dev-tools bash

# Connect to databases
db-connect user      # User Management DB
db-connect customer  # Customer Management DB
db-connect catalog   # Product Catalog DB
db-connect order     # Order Management DB

# Kafka operations
kafka-list-topics
kafka-create-topic my-topic 3 1  # topic-name partitions replication-factor
```

### Test Authentication Flow
```bash
# Get access token
curl -X POST "http://localhost:8090/realms/ndvn/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=user" \
  -d "password=user123" \
  -d "grant_type=password" \
  -d "client_id=api-gateway" \
  -d "client_secret=api-gateway-secret"

# Test API Gateway with token
curl -H "Authorization: Bearer <token>" http://localhost:8080/api/v1/users/me
```

### Monitor Services
```bash
# View logs
docker-compose -f infrastructure/monitoring/docker-compose-monitoring.yml logs -f

# Check service health
curl http://localhost:8080/actuator/health  # API Gateway
curl http://localhost:8090/health/ready     # Keycloak
curl http://localhost:9090/-/healthy        # Prometheus
```

## 🔧 Configuration

### Environment Variables
Key environment variables can be customized in each component's docker-compose file:

```yaml
# Example: Kafka configuration
KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'false'
KAFKA_LOG_RETENTION_HOURS: 168
KAFKA_NUM_PARTITIONS: 3

# Example: Keycloak configuration
KEYCLOAK_ADMIN: admin
KEYCLOAK_ADMIN_PASSWORD: admin-password
KC_HOSTNAME: localhost
```

### Custom Configuration Files
- **Kafka**: `kafka/config/kafka-topics.properties`
- **API Gateway**: `gateway/gateway-service/src/main/resources/application.yml`
- **Prometheus**: `monitoring/prometheus/config/prometheus.yml`
- **Grafana**: `monitoring/grafana/config/grafana.ini`

## 📈 Monitoring and Alerting

### Pre-configured Dashboards
- **SCS Platform Overview**: Service health, request rates, error rates
- **Infrastructure Metrics**: CPU, memory, disk usage
- **Application Metrics**: JVM metrics, database connections
- **Business Metrics**: Order processing, user registrations

### Alert Rules
- Service availability alerts
- High error rate alerts
- Resource utilization alerts
- Business process alerts

### Log Aggregation
- Centralized logging with ELK stack
- Structured JSON logging
- Log correlation with trace IDs
- Real-time log streaming

## 🔒 Security

### Authentication & Authorization
- OAuth 2.0 with Keycloak
- JWT token-based authentication
- Role-based access control (RBAC)
- Service-to-service authentication

### Network Security
- Internal Docker network isolation
- TLS termination at load balancer
- API rate limiting
- CORS configuration

### Secrets Management
- Environment-based secret injection
- Encrypted communication between services
- Database credential rotation support

## 🚨 Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check port usage
   netstat -tulpn | grep -E ':(8080|8090|9090|5432)'
   
   # Stop conflicting services
   sudo systemctl stop apache2  # If using port 80
   ```

2. **Memory Issues**
   ```bash
   # Check Docker memory allocation
   docker system info | grep -i memory
   
   # Increase Docker memory limit to 4GB+
   ```

3. **Service Startup Failures**
   ```bash
   # Check service logs
   docker-compose logs service-name
   
   # Restart specific service
   docker-compose restart service-name
   ```

4. **Database Connection Issues**
   ```bash
   # Test database connectivity
   docker exec ndvn-user-db pg_isready -h localhost -p 5432
   
   # Reset database
   docker-compose down -v
   docker-compose up -d
   ```

### Health Checks
```bash
# Comprehensive health check
./infrastructure/scripts/start-all.sh status

# Individual service health
curl http://localhost:8080/actuator/health
curl http://localhost:8090/health/ready
curl http://localhost:9090/-/healthy
```

### Log Analysis
```bash
# View aggregated logs in Kibana
open http://localhost:5601

# Search logs by service
# In Kibana: service_name:"user-management-service"

# View real-time logs
docker-compose logs -f --tail=100
```

## 🔄 Maintenance

### Backup Procedures
```bash
# Backup databases
docker exec ndvn-user-db pg_dump -U user_service user_management > backup_user_db.sql

# Backup Kafka topics
kafka-console-consumer --bootstrap-server localhost:9092 --topic user.events --from-beginning > backup_user_events.json
```

### Updates and Upgrades
```bash
# Update infrastructure images
docker-compose pull
docker-compose up -d

# Rebuild custom images
./infrastructure/gateway/scripts/setup-gateway.sh build
./infrastructure/auth/scripts/setup-auth.sh build
```

### Cleanup
```bash
# Clean up everything (WARNING: Destructive)
./infrastructure/scripts/start-all.sh cleanup

# Clean up specific component
./infrastructure/kafka/scripts/setup-kafka.sh cleanup
```

## 📚 Next Steps

1. **Phase 3: Domain Decomposition**
   - Implement User Management SCS
   - Implement Customer Management SCS
   - Implement Product Catalog SCS
   - Implement Order Management SCS
   - Implement Notification SCS

2. **Phase 4: Integration Testing**
   - End-to-end workflow testing
   - Performance testing
   - Security testing
   - Chaos engineering

3. **Phase 5: Production Deployment**
   - Kubernetes deployment
   - CI/CD pipeline setup
   - Production monitoring
   - Disaster recovery

## 🤝 Contributing

When adding new infrastructure components:

1. Follow the established directory structure
2. Include setup scripts with consistent interface
3. Add health checks and monitoring
4. Update this README with access points
5. Test the complete setup process

## 📞 Support

For issues with the infrastructure setup:

1. Check the troubleshooting section above
2. Review service logs for error details
3. Verify system requirements are met
4. Consult individual component READMEs

The infrastructure is designed to be self-contained and reproducible across different environments while providing a solid foundation for the SCS platform development.
