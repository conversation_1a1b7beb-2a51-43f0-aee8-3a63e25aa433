# Test environment configuration
spring:
  # Database configuration for testing
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # H2 Console disabled for tests
  h2:
    console:
      enabled: false
  
  # JPA configuration for testing
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: false

# Test server configuration
server:
  port: 0  # Random port for tests

# Logging configuration for tests
logging:
  level:
    com.nttdata.ndvn: INFO
    org.springframework: WARN
    org.hibernate.SQL: WARN
    root: WARN
