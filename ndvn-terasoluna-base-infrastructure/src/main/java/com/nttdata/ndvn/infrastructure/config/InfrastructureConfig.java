package com.nttdata.ndvn.infrastructure.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import com.nttdata.ndvn.domain.config.DomainConfig;

/**
 * Infrastructure layer configuration class.
 * This configuration class is responsible for setting up infrastructure-specific beans
 * such as data access configurations, external service integrations, etc.
 */
@Configuration
@ComponentScan(basePackages = {
    "com.nttdata.ndvn.infrastructure.repository"
})
@Import(DomainConfig.class)
public class InfrastructureConfig {
    
    // Infrastructure-specific bean configurations can be added here
    // For example: DataSource, TransactionManager, etc.
}
