version: '3.8'

services:
  # PostgreSQL database for Keycloak
  keycloak-db:
    image: postgres:15
    hostname: keycloak-db
    container_name: ndvn-keycloak-db
    environment:
      POSTGRES_DB: keycloak
      POSTGRES_USER: keycloak
      POSTGRES_PASSWORD: keycloak-password
      POSTGRES_ROOT_PASSWORD: root-password
    volumes:
      - keycloak-db-data:/var/lib/postgresql/data
      - ./keycloak/init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5437:5432"
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U keycloak -d keycloak"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Keycloak OAuth 2.0 Authorization Server
  keycloak:
    image: quay.io/keycloak/keycloak:23.0
    hostname: keycloak
    container_name: ndvn-keycloak
    environment:
      # Database configuration
      KC_DB: postgres
      KC_DB_URL: *******************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloak-password
      
      # Keycloak admin user
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin-password
      
      # Keycloak configuration
      KC_HOSTNAME: localhost
      KC_HOSTNAME_PORT: 8090
      KC_HOSTNAME_STRICT: false
      KC_HOSTNAME_STRICT_HTTPS: false
      KC_HTTP_ENABLED: true
      KC_HEALTH_ENABLED: true
      KC_METRICS_ENABLED: true
      
      # Proxy configuration
      KC_PROXY: edge
      
      # Logging
      KC_LOG_LEVEL: INFO
      KC_LOG_CONSOLE_COLOR: true
      
      # Features
      KC_FEATURES: token-exchange,admin-fine-grained-authz
    command: start-dev
    ports:
      - "8090:8080"
      - "8091:9000"  # Management port
    depends_on:
      keycloak-db:
        condition: service_healthy
    volumes:
      - ./keycloak/themes:/opt/keycloak/themes
      - ./keycloak/providers:/opt/keycloak/providers
      - ./keycloak/conf:/opt/keycloak/conf
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080/health/ready || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Redis for session storage and caching
  redis-auth:
    image: redis:7-alpine
    hostname: redis-auth
    container_name: ndvn-redis-auth
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --requirepass redis-password
    volumes:
      - redis-auth-data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Keycloak Configuration Service
  keycloak-config:
    image: quay.io/keycloak/keycloak:23.0
    container_name: ndvn-keycloak-config
    environment:
      KC_DB: postgres
      KC_DB_URL: *******************************************
      KC_DB_USERNAME: keycloak
      KC_DB_PASSWORD: keycloak-password
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin-password
    depends_on:
      keycloak:
        condition: service_healthy
    volumes:
      - ./keycloak/config:/opt/keycloak/config
      - ./keycloak/scripts:/opt/keycloak/scripts
    command: >
      bash -c "
        echo 'Waiting for Keycloak to be ready...'
        sleep 30
        echo 'Configuring Keycloak...'
        /opt/keycloak/scripts/configure-keycloak.sh
        echo 'Keycloak configuration completed'
      "
    networks:
      - ndvn-network

  # OAuth 2.0 Token Introspection Service
  token-service:
    image: ndvn/token-service:latest
    hostname: token-service
    container_name: ndvn-token-service
    build:
      context: ./token-service
      dockerfile: Dockerfile
    ports:
      - "8092:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      KEYCLOAK_URL: http://keycloak:8080
      KEYCLOAK_REALM: ndvn
      KEYCLOAK_CLIENT_ID: token-service
      KEYCLOAK_CLIENT_SECRET: token-service-secret
      REDIS_HOST: redis-auth
      REDIS_PORT: 6379
      REDIS_PASSWORD: redis-password
    depends_on:
      keycloak:
        condition: service_healthy
      redis-auth:
        condition: service_healthy
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  keycloak-db-data:
    driver: local
  redis-auth-data:
    driver: local

networks:
  ndvn-network:
    external: true
    name: ndvn-scs-network
