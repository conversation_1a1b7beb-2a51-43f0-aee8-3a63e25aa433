plugins {
    id 'java-library'
}

description = 'Product Catalog Infrastructure Layer - Data access and external integrations'

dependencies {
    api project(':product-domain')
    
    // Data access
    api 'org.springframework.boot:spring-boot-starter-data-jpa'
    api 'org.springframework.boot:spring-boot-starter-data-redis'
    api 'org.springframework.boot:spring-boot-starter-data-elasticsearch'
    api 'org.postgresql:postgresql'
    
    // Caching
    api 'org.springframework.boot:spring-boot-starter-cache'
    
    // External integrations
    api 'org.springframework.cloud:spring-cloud-starter-openfeign'
    
    // File storage
    api 'org.springframework.boot:spring-boot-starter-web'
    
    // Testing
    testImplementation 'org.testcontainers:postgresql'
    testImplementation 'org.testcontainers:elasticsearch'
    testImplementation 'com.h2database:h2'
}
