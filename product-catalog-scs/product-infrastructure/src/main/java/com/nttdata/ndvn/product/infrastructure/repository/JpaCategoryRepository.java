package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA repository interface for Category entity.
 */
@Repository
public interface JpaCategoryRepository extends JpaRepository<Category, UUID> {
    
    /**
     * Find category by slug.
     */
    Optional<Category> findBySlug(String slug);
    
    /**
     * Find active categories.
     */
    Page<Category> findByActive(boolean active, Pageable pageable);
    
    /**
     * Find root categories (no parent).
     */
    @Query("SELECT c FROM Category c WHERE c.parentId IS NULL ORDER BY c.sortOrder ASC")
    List<Category> findRootCategories();
    
    /**
     * Find categories by parent ID.
     */
    List<Category> findByParentId(UUID parentId);
    
    /**
     * Find categories by parent ID with pagination.
     */
    Page<Category> findByParentId(UUID parentId, Pageable pageable);
    
    /**
     * Find categories by level.
     */
    List<Category> findByLevel(Integer level);
    
    /**
     * Find categories by level with pagination.
     */
    Page<Category> findByLevel(Integer level, Pageable pageable);
    
    /**
     * Find categories by name containing (case-insensitive).
     */
    Page<Category> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find categories by description containing (case-insensitive).
     */
    Page<Category> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find categories created after a specific date.
     */
    Page<Category> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find categories updated after a specific date.
     */
    Page<Category> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find categories ordered by sort order.
     */
    List<Category> findAllByOrderBySortOrderAsc();
    
    /**
     * Find categories by parent ordered by sort order.
     */
    List<Category> findByParentIdOrderBySortOrderAsc(UUID parentId);
    
    /**
     * Find categories ordered by name.
     */
    Page<Category> findAllByOrderByNameAsc(Pageable pageable);
    
    /**
     * Find categories ordered by creation date.
     */
    Page<Category> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * Search categories by name or description.
     */
    @Query("SELECT c FROM Category c WHERE " +
           "LOWER(c.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Category> searchCategories(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Find leaf categories (no children).
     */
    @Query("SELECT c FROM Category c WHERE c.id NOT IN " +
           "(SELECT DISTINCT c2.parentId FROM Category c2 WHERE c2.parentId IS NOT NULL)")
    List<Category> findLeafCategories();
    
    /**
     * Find categories with children.
     */
    @Query("SELECT DISTINCT c FROM Category c WHERE c.id IN " +
           "(SELECT c2.parentId FROM Category c2 WHERE c2.parentId IS NOT NULL)")
    List<Category> findCategoriesWithChildren();
    
    /**
     * Find categories by path containing.
     */
    Page<Category> findByPathContaining(String pathSegment, Pageable pageable);
    
    /**
     * Check if slug exists.
     */
    boolean existsBySlug(String slug);
    
    /**
     * Check if category has children.
     */
    @Query("SELECT COUNT(c) > 0 FROM Category c WHERE c.parentId = :categoryId")
    boolean hasChildren(@Param("categoryId") UUID categoryId);
    
    /**
     * Check if category has products.
     */
    @Query("SELECT COUNT(p) > 0 FROM Product p WHERE p.categoryId = :categoryId")
    boolean hasProducts(@Param("categoryId") UUID categoryId);
    
    /**
     * Count categories by parent.
     */
    long countByParentId(UUID parentId);
    
    /**
     * Count active categories.
     */
    long countByActive(boolean active);
    
    /**
     * Count categories by level.
     */
    long countByLevel(Integer level);
    
    /**
     * Count root categories.
     */
    @Query("SELECT COUNT(c) FROM Category c WHERE c.parentId IS NULL")
    long countRootCategories();
    
    /**
     * Count leaf categories.
     */
    @Query("SELECT COUNT(c) FROM Category c WHERE c.id NOT IN " +
           "(SELECT DISTINCT c2.parentId FROM Category c2 WHERE c2.parentId IS NOT NULL)")
    long countLeafCategories();
    
    /**
     * Find all descendants of a category.
     */
    @Query("SELECT c FROM Category c WHERE c.path LIKE CONCAT(:path, '/%')")
    List<Category> findDescendants(@Param("path") String path);
    
    /**
     * Find categories by multiple parent IDs.
     */
    List<Category> findByParentIdIn(List<UUID> parentIds);
    
    /**
     * Find categories by multiple levels.
     */
    List<Category> findByLevelIn(List<Integer> levels);
    
    /**
     * Find categories with images.
     */
    Page<Category> findByImageUrlIsNotNull(Pageable pageable);
    
    /**
     * Find categories without images.
     */
    Page<Category> findByImageUrlIsNull(Pageable pageable);
    
    /**
     * Find categories with icons.
     */
    Page<Category> findByIconIsNotNull(Pageable pageable);
    
    /**
     * Find categories without icons.
     */
    Page<Category> findByIconIsNull(Pageable pageable);
    
    /**
     * Find categories by meta keywords containing.
     */
    Page<Category> findByMetaKeywordsContaining(String keywords, Pageable pageable);
    
    /**
     * Find categories for sitemap (active categories with paths).
     */
    @Query("SELECT c FROM Category c WHERE c.active = true AND c.path IS NOT NULL ORDER BY c.path")
    List<Category> findCategoriesForSitemap();
    
    /**
     * Find categories for navigation menu.
     */
    @Query("SELECT c FROM Category c WHERE c.active = true AND c.level <= :maxLevel ORDER BY c.level, c.sortOrder")
    List<Category> findCategoriesForNavigation(@Param("maxLevel") Integer maxLevel);
    
    /**
     * Find popular categories (with most products).
     */
    @Query(value = "SELECT c.* FROM categories c " +
                   "LEFT JOIN products p ON c.id = p.category_id " +
                   "WHERE c.active = true " +
                   "GROUP BY c.id " +
                   "ORDER BY COUNT(p.id) DESC " +
                   "LIMIT :limit", 
           nativeQuery = true)
    List<Category> findPopularCategories(@Param("limit") int limit);
}
