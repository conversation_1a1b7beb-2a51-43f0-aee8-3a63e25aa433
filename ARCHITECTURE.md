# Architecture Documentation

## Overview

This document describes the architecture and design decisions for the NDVN Terasoluna Base project, a multi-module Gradle application built with Terasoluna 5.10.0.RELEASE and Spring Boot 3.4.1.

## Architectural Principles

### 1. Clean Architecture
The project follows Clean Architecture principles with clear separation of concerns:
- **Domain Layer**: Core business logic, independent of external concerns
- **Application Layer**: Use cases and application services
- **Infrastructure Layer**: Data access and external integrations
- **Web Layer**: REST API and web-specific concerns

### 2. Dependency Rule
Dependencies point inward toward the domain:
```
Web → Application → Infrastructure → Domain
```

### 3. Modularity
The project is structured as a multi-module Gradle application:
```
ndvn-terasoluna-base/
├── ndvn-terasoluna-base-domain/
├── ndvn-terasoluna-base-infrastructure/
├── ndvn-terasoluna-base-application/
└── ndvn-terasoluna-base-web/
```

## Module Details

### Domain Module (`ndvn-terasoluna-base-domain`)

**Purpose**: Contains the core business logic and domain model.

**Key Components**:
- **Entities**: Core business objects with identity
- **Value Objects**: Immutable objects without identity
- **Domain Services**: Business logic that doesn't belong to a single entity
- **Repository Interfaces**: Contracts for data access

**Dependencies**:
- Terasoluna GFW Common
- Bean Validation (Jakarta)
- No dependencies on other modules

**Configuration**:
- `DomainConfig.java`: Component scanning for domain services

### Infrastructure Module (`ndvn-terasoluna-base-infrastructure`)

**Purpose**: Implements data access and external service integrations.

**Key Components**:
- **Repository Implementations**: JPA/MyBatis implementations
- **Data Source Configuration**: Database connection setup
- **External Service Clients**: Third-party API integrations

**Dependencies**:
- Domain module
- Terasoluna GFW JPA dependencies
- Database drivers (H2, PostgreSQL)
- Connection pooling (Commons DBCP2)

**Configuration**:
- `InfrastructureConfig.java`: Infrastructure component scanning
- `DataSourceConfig.java`: Database configuration

### Application Module (`ndvn-terasoluna-base-application`)

**Purpose**: Orchestrates business operations and implements use cases.

**Key Components**:
- **Application Services**: Use case implementations
- **DTOs**: Data transfer objects for layer communication
- **Mappers**: Object mapping between layers (MapStruct)

**Dependencies**:
- Domain module
- Infrastructure module
- MapStruct for object mapping
- Spring Transaction management

**Configuration**:
- `ApplicationConfig.java`: Application service configuration
- Transaction management enabled

### Web Module (`ndvn-terasoluna-base-web`)

**Purpose**: Provides REST API endpoints and web-specific configuration.

**Key Components**:
- **REST Controllers**: HTTP endpoint implementations
- **Exception Handlers**: Global exception handling
- **Security Configuration**: Authentication and authorization
- **Main Application Class**: Spring Boot entry point

**Dependencies**:
- Application module
- Spring Boot Web Starter
- Terasoluna GFW Web dependencies
- Jackson for JSON processing

**Configuration**:
- `WebConfig.java`: Web layer configuration
- `Application.java`: Main Spring Boot application

## Technology Stack Integration

### Terasoluna Framework
- **Version**: 5.10.0.RELEASE
- **Purpose**: Enterprise-grade framework with proven patterns
- **Benefits**: 
  - Standardized project structure
  - Common libraries for enterprise features
  - Spring Boot compatibility

### Spring Boot Integration
- **Version**: 3.4.1
- **Integration Strategy**:
  - Use Spring Boot's dependency management
  - Leverage auto-configuration where appropriate
  - Maintain Terasoluna's architectural principles
  - Override auto-configuration when needed

### Database Strategy
- **Development**: H2 in-memory database
- **Production**: PostgreSQL (configurable)
- **ORM**: JPA with Hibernate
- **Migration**: Ready for Flyway/Liquibase integration

## Configuration Strategy

### Profile-Based Configuration
- **development**: Local development with H2
- **test**: Testing with in-memory database
- **production**: Production-ready configuration

### Property Externalization
Following 12-factor app principles:
- Environment variables for sensitive data
- Profile-specific property files
- Sensible defaults for development

### Java Configuration Classes
```java
@Configuration
@ComponentScan(basePackages = "...")
@Import(OtherConfig.class)
public class ModuleConfig {
    // Bean definitions
}
```

## Security Considerations

### Framework Security
- Spring Security integration ready
- Terasoluna security extensions available
- CSRF protection enabled by default

### Data Protection
- Input validation with Bean Validation
- SQL injection prevention with JPA/MyBatis
- XSS protection in web layer

## Testing Strategy

### Unit Testing
- JUnit 5 for all modules
- Mockito for mocking dependencies
- Spring Boot Test for integration tests

### Test Configuration
- Separate test profile
- In-memory database for tests
- Mock external dependencies

## Deployment Considerations

### Build Artifacts
- Executable JAR from web module
- All dependencies included
- Profile-specific configurations

### Environment Requirements
- Java 21+
- Database (PostgreSQL for production)
- Application server (embedded Tomcat)

## Extension Points

### Adding New Features
1. **Domain**: Add entities and business logic
2. **Infrastructure**: Implement data access
3. **Application**: Create use case services
4. **Web**: Expose REST endpoints

### Adding New Modules
- Follow the same structure pattern
- Update `settings.gradle`
- Add appropriate dependencies

### Database Changes
- Add migration scripts
- Update entity mappings
- Test with different profiles

## Best Practices

### Code Organization
- Package by feature, not by layer
- Keep interfaces in domain layer
- Implement in infrastructure layer

### Dependency Management
- Use Terasoluna's dependency management
- Override versions only when necessary
- Document version changes

### Configuration Management
- Use type-safe configuration
- Validate configuration on startup
- Provide meaningful error messages

## Monitoring and Observability

### Health Checks
- Spring Boot Actuator endpoints
- Custom health indicators
- Database connectivity checks

### Logging
- Structured logging with Logback
- Profile-specific log levels
- Centralized log configuration

### Metrics
- Spring Boot Actuator metrics
- Custom business metrics
- Performance monitoring ready
