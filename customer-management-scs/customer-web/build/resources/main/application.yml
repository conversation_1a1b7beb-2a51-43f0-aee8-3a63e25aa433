spring:
  application:
    name: customer-management-service
  
  profiles:
    active: local
  
  datasource:
    url: ****************************************************
    username: customer_service
    password: customer_password
    driver-class-name: org.postgresql.Driver
    hikari:
      pool-name: CustomerManagementPool
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 25
        order_inserts: true
        order_updates: true
        connection:
          provider_disables_autocommit: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  
  data:
    redis:
      host: localhost
      port: 6379
      database: 2
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  cache:
    type: redis
    redis:
      time-to-live: 1800000
      cache-null-values: false
  
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
      acks: all
      retries: 3
      properties:
        enable.idempotence: true
        max.in.flight.requests.per.connection: 1
    consumer:
      group-id: customer-management-service
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      auto-offset-reset: earliest
      properties:
        spring.json.trusted.packages: "com.nttdata.ndvn.customer.events"
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8090/realms/ndvn
          jwk-set-uri: http://localhost:8090/realms/ndvn/protocol/openid-connect/certs
  
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        service-name: ${spring.application.name}
        health-check-path: /actuator/health
        health-check-interval: 10s
        instance-id: ${spring.application.name}:${server.port}
        prefer-ip-address: true
        tags:
          - customer
          - scs
          - microservice
      config:
        enabled: true
        format: yaml
        data-key: configuration

server:
  port: 8082
  servlet:
    context-path: /

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  tracing:
    sampling:
      probability: 1.0

logging:
  level:
    com.nttdata.ndvn.customer: INFO
    org.springframework.security: DEBUG
    org.springframework.kafka: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"

# OpenAPI Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operationsSorter: method
  info:
    title: Customer Management API
    description: Customer Management Self-Contained System API
    version: 1.0.0
    contact:
      name: NDVN Development Team
      email: <EMAIL>

---
spring:
  config:
    activate:
      on-profile: docker
  
  datasource:
    url: ***************************************************
  
  data:
    redis:
      host: redis
  
  kafka:
    bootstrap-servers: kafka:9092
  
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://keycloak:8080/realms/ndvn
          jwk-set-uri: http://keycloak:8080/realms/ndvn/protocol/openid-connect/certs
  
  cloud:
    consul:
      host: consul

---
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  
  flyway:
    enabled: false
  
  data:
    redis:
      host: localhost
      port: 6370
  
  kafka:
    bootstrap-servers: localhost:9093
