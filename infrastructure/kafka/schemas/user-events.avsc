{"type": "record", "name": "UserEvent", "namespace": "com.nttdata.ndvn.user.events", "doc": "Schema for user management events", "fields": [{"name": "eventId", "type": "string", "doc": "Unique identifier for the event"}, {"name": "eventType", "type": {"type": "enum", "name": "UserEventType", "symbols": ["UserCreated", "UserUpdated", "UserDeactivated", "UserActivated", "UserPasswordChanged", "UserRoleAssigned", "UserRoleRevoked"]}, "doc": "Type of user event"}, {"name": "eventVersion", "type": "string", "default": "1.0", "doc": "Version of the event schema"}, {"name": "timestamp", "type": {"type": "long", "logicalType": "timestamp-millis"}, "doc": "Timestamp when the event occurred"}, {"name": "source", "type": "string", "default": "user-management-service", "doc": "Source service that generated the event"}, {"name": "correlationId", "type": ["null", "string"], "default": null, "doc": "Correlation ID for request tracing"}, {"name": "causationId", "type": ["null", "string"], "default": null, "doc": "ID of the event that caused this event"}, {"name": "data", "type": {"type": "record", "name": "UserEventData", "fields": [{"name": "userId", "type": "string", "doc": "Unique identifier of the user"}, {"name": "username", "type": "string", "doc": "Username of the user"}, {"name": "email", "type": "string", "doc": "Email address of the user"}, {"name": "enabled", "type": "boolean", "default": true, "doc": "Whether the user account is enabled"}, {"name": "roles", "type": {"type": "array", "items": "string"}, "default": [], "doc": "List of roles assigned to the user"}, {"name": "createdAt", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null, "doc": "Timestamp when the user was created"}, {"name": "updatedAt", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null, "doc": "Timestamp when the user was last updated"}, {"name": "changes", "type": ["null", {"type": "map", "values": {"type": "record", "name": "FieldChange", "fields": [{"name": "oldValue", "type": ["null", "string"], "default": null}, {"name": "newValue", "type": ["null", "string"], "default": null}]}}], "default": null, "doc": "Map of field changes for update events"}]}, "doc": "Event payload data"}, {"name": "metadata", "type": ["null", {"type": "map", "values": "string"}], "default": null, "doc": "Additional metadata for the event"}]}