-- Customer Management SCS Database Schema
-- Version 1.0 - Initial schema creation

-- Create customers table
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_number VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID,
    customer_type VARCHAR(20) NOT NULL DEFAULT 'INDIVIDUAL',
    company_name <PERSON><PERSON><PERSON><PERSON>(255),
    first_name <PERSON><PERSON><PERSON><PERSON>(100),
    last_name <PERSON><PERSON><PERSON><PERSON>(100),
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    tax_id VARCHAR(50),
    status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE',
    classification VARCHAR(50) DEFAULT 'STANDARD',
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT chk_customer_type CHECK (customer_type IN ('INDIVIDUAL', 'BUSINESS')),
    CONSTRAINT chk_customer_status CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'ARCHIVED')),
    CONSTRAINT chk_credit_limit CHECK (credit_limit >= 0)
);

-- Create customer_addresses table
CREATE TABLE customer_addresses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    address_type VARCHAR(20) NOT NULL DEFAULT 'BILLING',
    street_address TEXT NOT NULL,
    address_line_2 TEXT,
    city VARCHAR(100) NOT NULL,
    state VARCHAR(50),
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(50) NOT NULL DEFAULT 'VN',
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_customer_addresses_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    CONSTRAINT chk_address_type CHECK (address_type IN ('BILLING', 'SHIPPING', 'MAILING', 'OTHER'))
);

-- Create customer_notes table
CREATE TABLE customer_notes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    note_type VARCHAR(50) DEFAULT 'GENERAL',
    subject VARCHAR(255),
    content TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT TRUE,
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT fk_customer_notes_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- Create customer_segments table
CREATE TABLE customer_segments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    criteria JSONB,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create customer_segment_assignments table
CREATE TABLE customer_segment_assignments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID NOT NULL,
    segment_id UUID NOT NULL,
    assigned_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(100),
    
    CONSTRAINT fk_customer_segment_assignments_customer FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    CONSTRAINT fk_customer_segment_assignments_segment FOREIGN KEY (segment_id) REFERENCES customer_segments(id) ON DELETE CASCADE,
    CONSTRAINT uk_customer_segment_assignment UNIQUE (customer_id, segment_id)
);

-- Create indexes for performance optimization

-- Customer indexes
CREATE INDEX idx_customer_number ON customers(customer_number);
CREATE INDEX idx_customer_email ON customers(email);
CREATE INDEX idx_customer_user_id ON customers(user_id);
CREATE INDEX idx_customer_status ON customers(status);
CREATE INDEX idx_customer_classification ON customers(classification);
CREATE INDEX idx_customer_created_at ON customers(created_at);
CREATE INDEX idx_customer_type ON customers(customer_type);
CREATE INDEX idx_customer_company_name ON customers(company_name) WHERE company_name IS NOT NULL;
CREATE INDEX idx_customer_name ON customers(first_name, last_name) WHERE first_name IS NOT NULL AND last_name IS NOT NULL;

-- Customer address indexes
CREATE INDEX idx_customer_address_customer_id ON customer_addresses(customer_id);
CREATE INDEX idx_customer_address_type ON customer_addresses(address_type);
CREATE INDEX idx_customer_address_primary ON customer_addresses(is_primary);
CREATE INDEX idx_customer_address_active ON customer_addresses(is_active);
CREATE INDEX idx_customer_address_city ON customer_addresses(city);
CREATE INDEX idx_customer_address_country ON customer_addresses(country);

-- Customer note indexes
CREATE INDEX idx_customer_note_customer_id ON customer_notes(customer_id);
CREATE INDEX idx_customer_note_type ON customer_notes(note_type);
CREATE INDEX idx_customer_note_created_at ON customer_notes(created_at);
CREATE INDEX idx_customer_note_created_by ON customer_notes(created_by);

-- Customer segment indexes
CREATE INDEX idx_customer_segment_name ON customer_segments(name);
CREATE INDEX idx_customer_segment_active ON customer_segments(is_active);

-- Customer segment assignment indexes
CREATE INDEX idx_customer_segment_assignment_customer ON customer_segment_assignments(customer_id);
CREATE INDEX idx_customer_segment_assignment_segment ON customer_segment_assignments(segment_id);
CREATE INDEX idx_customer_segment_assignment_assigned_at ON customer_segment_assignments(assigned_at);

-- Create trigger function for updating updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_customers_updated_at 
    BEFORE UPDATE ON customers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_addresses_updated_at 
    BEFORE UPDATE ON customer_addresses 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_segments_updated_at 
    BEFORE UPDATE ON customer_segments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE customers IS 'Main customer entity table storing customer profile information';
COMMENT ON TABLE customer_addresses IS 'Customer address information with support for multiple addresses per customer';
COMMENT ON TABLE customer_notes IS 'Customer notes and comments for customer service and internal use';
COMMENT ON TABLE customer_segments IS 'Customer segmentation definitions for marketing and business analysis';
COMMENT ON TABLE customer_segment_assignments IS 'Assignment of customers to specific segments';

COMMENT ON COLUMN customers.customer_number IS 'Unique customer identifier for external reference';
COMMENT ON COLUMN customers.user_id IS 'Reference to user management system (loose coupling)';
COMMENT ON COLUMN customers.customer_type IS 'Type of customer: INDIVIDUAL or BUSINESS';
COMMENT ON COLUMN customers.classification IS 'Customer classification for business rules (STANDARD, PREMIUM, etc.)';
COMMENT ON COLUMN customers.credit_limit IS 'Customer credit limit in system currency';

COMMENT ON COLUMN customer_addresses.is_primary IS 'Indicates if this is the primary address for the customer';
COMMENT ON COLUMN customer_addresses.is_active IS 'Indicates if this address is currently active';

COMMENT ON COLUMN customer_notes.is_internal IS 'Indicates if this note is for internal use only';

COMMENT ON COLUMN customer_segments.criteria IS 'JSON criteria for automatic segment assignment';
COMMENT ON COLUMN customer_segments.is_active IS 'Indicates if this segment is currently active for assignment';
