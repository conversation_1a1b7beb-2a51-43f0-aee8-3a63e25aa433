package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Brand;
import com.nttdata.ndvn.product.domain.repository.BrandRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of BrandRepository using JPA.
 */
@Repository
public class BrandRepositoryImpl implements BrandRepository {
    
    private final JpaBrandRepository jpaBrandRepository;
    
    public BrandRepositoryImpl(JpaBrandRepository jpaBrandRepository) {
        this.jpaBrandRepository = jpaBrandRepository;
    }
    
    @Override
    public Brand save(Brand brand) {
        return jpaBrandRepository.save(brand);
    }
    
    @Override
    public Optional<Brand> findById(UUID id) {
        return jpaBrandRepository.findById(id);
    }
    
    @Override
    public Optional<Brand> findBySlug(String slug) {
        return jpaBrandRepository.findBySlug(slug);
    }
    
    @Override
    public Optional<Brand> findByName(String name) {
        return jpaBrandRepository.findByName(name);
    }
    
    @Override
    public Page<Brand> findAll(Pageable pageable) {
        return jpaBrandRepository.findAll(pageable);
    }
    
    @Override
    public Page<Brand> findByActive(boolean active, Pageable pageable) {
        return jpaBrandRepository.findByActive(active, pageable);
    }
    
    @Override
    public Page<Brand> findByNameContainingIgnoreCase(String name, Pageable pageable) {
        return jpaBrandRepository.findByNameContainingIgnoreCase(name, pageable);
    }
    
    @Override
    public Page<Brand> findByDescriptionContainingIgnoreCase(String description, Pageable pageable) {
        return jpaBrandRepository.findByDescriptionContainingIgnoreCase(description, pageable);
    }
    
    @Override
    public Page<Brand> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable) {
        return jpaBrandRepository.findByCreatedAtAfter(createdAfter, pageable);
    }
    
    @Override
    public Page<Brand> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable) {
        return jpaBrandRepository.findByUpdatedAtAfter(updatedAfter, pageable);
    }
    
    @Override
    public List<Brand> findAllOrderBySortOrderAsc() {
        return jpaBrandRepository.findAllByOrderBySortOrderAsc();
    }
    
    @Override
    public Page<Brand> findAllOrderByNameAsc(Pageable pageable) {
        return jpaBrandRepository.findAllByOrderByNameAsc(pageable);
    }
    
    @Override
    public Page<Brand> findAllOrderByCreatedAtDesc(Pageable pageable) {
        return jpaBrandRepository.findAllByOrderByCreatedAtDesc(pageable);
    }
    
    @Override
    public Page<Brand> searchBrands(String searchTerm, Pageable pageable) {
        return jpaBrandRepository.searchBrands(searchTerm, pageable);
    }
    
    @Override
    public Page<Brand> findByLogoUrlIsNotNull(Pageable pageable) {
        return jpaBrandRepository.findByLogoUrlIsNotNull(pageable);
    }
    
    @Override
    public Page<Brand> findByLogoUrlIsNull(Pageable pageable) {
        return jpaBrandRepository.findByLogoUrlIsNull(pageable);
    }
    
    @Override
    public Page<Brand> findByWebsiteUrlIsNotNull(Pageable pageable) {
        return jpaBrandRepository.findByWebsiteUrlIsNotNull(pageable);
    }
    
    @Override
    public Page<Brand> findByWebsiteUrlIsNull(Pageable pageable) {
        return jpaBrandRepository.findByWebsiteUrlIsNull(pageable);
    }
    
    @Override
    public boolean existsBySlug(String slug) {
        return jpaBrandRepository.existsBySlug(slug);
    }
    
    @Override
    public boolean existsByName(String name) {
        return jpaBrandRepository.existsByName(name);
    }
    
    @Override
    public boolean existsById(UUID id) {
        return jpaBrandRepository.existsById(id);
    }
    
    @Override
    public long countByActive(boolean active) {
        return jpaBrandRepository.countByActive(active);
    }
    
    @Override
    public long countBrandsWithProducts() {
        return jpaBrandRepository.countBrandsWithProducts();
    }
    
    @Override
    public List<Brand> findAllById(Iterable<UUID> ids) {
        return jpaBrandRepository.findAllById(ids);
    }
    
    @Override
    public List<Brand> findPopularBrands(int limit) {
        return jpaBrandRepository.findPopularBrands(limit);
    }
    
    @Override
    public void deleteById(UUID id) {
        jpaBrandRepository.deleteById(id);
    }
    
    @Override
    public void delete(Brand brand) {
        jpaBrandRepository.delete(brand);
    }
    
    @Override
    public List<Brand> saveAll(Iterable<Brand> brands) {
        return jpaBrandRepository.saveAll(brands);
    }
    
    @Override
    public long count() {
        return jpaBrandRepository.count();
    }
}
