# SCS Implementation Guide: NDVN Terasoluna Base Project

## Quick Start Guide

This guide provides step-by-step instructions for implementing the Self-Contained Systems (SCS) architecture transformation of the NDVN Terasoluna Base Project.

## Prerequisites

### Development Environment
- **Java 21+** with Jakarta EE 10
- **Gradle 8.5+** for build management
- **Docker & Docker Compose** for local development
- **PostgreSQL 15+** for databases
- **Apache Kafka 3.6+** for event streaming
- **IDE:** IntelliJ IDEA or Eclipse with Spring Tools

### Infrastructure Requirements
- **Kubernetes cluster** or Docker Swarm for deployment
- **Container registry** (Docker Hub, AWS ECR, etc.)
- **CI/CD pipeline** (Jenkins, GitLab CI, GitHub Actions)
- **Monitoring stack** (Prometheus, Grafana, ELK)

## Phase 1: Project Setup and Infrastructure

### Step 1: Create SCS Project Structure

```bash
# Create root directory for all SCS services
mkdir ndvn-scs-platform
cd ndvn-scs-platform

# Create individual SCS projects
mkdir user-management-scs
mkdir customer-management-scs
mkdir product-catalog-scs
mkdir order-management-scs
mkdir notification-scs
mkdir shared-infrastructure
```

### Step 2: Set Up Shared Infrastructure

#### Docker Compose for Local Development
Create `shared-infrastructure/docker-compose.yml`:

```yaml
version: '3.8'
services:
  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1

  # PostgreSQL for each service
  user-db:
    image: postgres:15
    environment:
      POSTGRES_DB: user_management
      POSTGRES_USER: user_service
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"

  customer-db:
    image: postgres:15
    environment:
      POSTGRES_DB: customer_management
      POSTGRES_USER: customer_service
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"

  catalog-db:
    image: postgres:15
    environment:
      POSTGRES_DB: product_catalog
      POSTGRES_USER: catalog_service
      POSTGRES_PASSWORD: password
    ports:
      - "5434:5432"

  order-db:
    image: postgres:15
    environment:
      POSTGRES_DB: order_management
      POSTGRES_USER: order_service
      POSTGRES_PASSWORD: password
    ports:
      - "5435:5432"

  notification-db:
    image: postgres:15
    environment:
      POSTGRES_DB: notification_service
      POSTGRES_USER: notification_service
      POSTGRES_PASSWORD: password
    ports:
      - "5436:5432"

  # Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  # API Gateway
  api-gateway:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
```

### Step 3: Create User Management SCS

#### Project Structure
```bash
cd user-management-scs

# Create Gradle multi-module structure
mkdir -p user-domain/src/main/java/com/nttdata/ndvn/user/domain
mkdir -p user-infrastructure/src/main/java/com/nttdata/ndvn/user/infrastructure
mkdir -p user-application/src/main/java/com/nttdata/ndvn/user/application
mkdir -p user-events/src/main/java/com/nttdata/ndvn/user/events
mkdir -p user-web/src/main/java/com/nttdata/ndvn/user/web
```

#### Root build.gradle
```gradle
plugins {
    id 'java'
    id 'org.springframework.boot' version '3.4.1' apply false
    id 'io.spring.dependency-management' version '1.1.7' apply false
}

allprojects {
    group = 'com.nttdata.ndvn.user'
    version = '1.0.0-SNAPSHOT'
    
    repositories {
        mavenCentral()
    }
}

subprojects {
    apply plugin: 'java'
    apply plugin: 'io.spring.dependency-management'
    
    java {
        sourceCompatibility = JavaVersion.VERSION_21
        targetCompatibility = JavaVersion.VERSION_21
    }
    
    dependencyManagement {
        imports {
            mavenBom 'org.terasoluna.gfw:terasoluna-gfw-parent:5.10.0.RELEASE'
            mavenBom 'org.springframework.boot:spring-boot-dependencies:3.4.1'
        }
    }
    
    dependencies {
        implementation 'org.springframework:spring-context'
        implementation 'org.springframework:spring-core'
        implementation 'jakarta.annotation:jakarta.annotation-api'
        implementation 'org.slf4j:slf4j-api'
        implementation 'ch.qos.logback:logback-classic'
        
        testImplementation 'org.springframework.boot:spring-boot-starter-test'
        testImplementation 'org.junit.jupiter:junit-jupiter'
        testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
    }
    
    test {
        useJUnitPlatform()
    }
}
```

#### settings.gradle
```gradle
rootProject.name = 'user-management-scs'

include 'user-domain'
include 'user-infrastructure'
include 'user-application'
include 'user-events'
include 'user-web'
```

### Step 4: Implement Domain Layer

#### User Entity (user-domain/src/main/java/com/nttdata/ndvn/user/domain/model/User.java)
```java
package com.nttdata.ndvn.user.domain.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "users")
public class User {
    
    @Id
    private UUID id;
    
    @Column(unique = true, nullable = false)
    private String username;
    
    @Column(unique = true, nullable = false)
    private String email;
    
    @Column(name = "password_hash", nullable = false)
    private String passwordHash;
    
    private boolean enabled = true;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
        name = "user_roles",
        joinColumns = @JoinColumn(name = "user_id"),
        inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles;
    
    // Constructors, getters, setters, equals, hashCode
    
    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
}
```

#### Repository Interface (user-domain/src/main/java/com/nttdata/ndvn/user/domain/repository/UserRepository.java)
```java
package com.nttdata.ndvn.user.domain.repository;

import com.nttdata.ndvn.user.domain.model.User;
import java.util.Optional;
import java.util.UUID;

public interface UserRepository {
    Optional<User> findById(UUID id);
    Optional<User> findByUsername(String username);
    Optional<User> findByEmail(String email);
    User save(User user);
    void delete(User user);
    boolean existsByUsername(String username);
    boolean existsByEmail(String email);
}
```

### Step 5: Implement Infrastructure Layer

#### JPA Repository Implementation
```java
package com.nttdata.ndvn.user.infrastructure.repository;

import com.nttdata.ndvn.user.domain.model.User;
import com.nttdata.ndvn.user.domain.repository.UserRepository;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

@Repository
public interface JpaUserRepository extends JpaRepository<User, UUID>, UserRepository {
    // Spring Data JPA will implement the methods automatically
}
```

#### Database Configuration
```java
package com.nttdata.ndvn.user.infrastructure.config;

import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Configuration
@EnableJpaRepositories(basePackages = "com.nttdata.ndvn.user.infrastructure.repository")
@EntityScan(basePackages = "com.nttdata.ndvn.user.domain.model")
@EnableTransactionManagement
public class DatabaseConfig {
}
```

### Step 6: Implement Application Layer

#### Application Service
```java
package com.nttdata.ndvn.user.application.service;

import com.nttdata.ndvn.user.application.dto.CreateUserRequest;
import com.nttdata.ndvn.user.application.dto.UserDto;
import com.nttdata.ndvn.user.domain.model.User;
import com.nttdata.ndvn.user.domain.repository.UserRepository;
import com.nttdata.ndvn.user.events.publisher.UserEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@Transactional
public class UserApplicationService {
    
    private final UserRepository userRepository;
    private final UserEventPublisher eventPublisher;
    private final UserMapper userMapper;
    
    public UserApplicationService(UserRepository userRepository, 
                                UserEventPublisher eventPublisher,
                                UserMapper userMapper) {
        this.userRepository = userRepository;
        this.eventPublisher = eventPublisher;
        this.userMapper = userMapper;
    }
    
    public UserDto createUser(CreateUserRequest request) {
        // Validate request
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new IllegalArgumentException("Username already exists");
        }
        
        if (userRepository.existsByEmail(request.getEmail())) {
            throw new IllegalArgumentException("Email already exists");
        }
        
        // Create user
        User user = User.builder()
            .username(request.getUsername())
            .email(request.getEmail())
            .passwordHash(encodePassword(request.getPassword()))
            .enabled(true)
            .build();
        
        User savedUser = userRepository.save(user);
        
        // Publish event
        eventPublisher.publishUserCreated(savedUser);
        
        return userMapper.toDto(savedUser);
    }
    
    @Transactional(readOnly = true)
    public UserDto getUser(UUID userId) {
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new IllegalArgumentException("User not found"));
        
        return userMapper.toDto(user);
    }
}
```

### Step 7: Implement Event Layer

#### Event Publisher
```java
package com.nttdata.ndvn.user.events.publisher;

import com.nttdata.ndvn.user.domain.model.User;
import com.nttdata.ndvn.user.events.model.UserCreatedEvent;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

@Component
public class UserEventPublisher {
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public UserEventPublisher(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }
    
    public void publishUserCreated(User user) {
        UserCreatedEvent event = UserCreatedEvent.builder()
            .eventId(UUID.randomUUID().toString())
            .eventType("UserCreated")
            .eventVersion("1.0")
            .timestamp(Instant.now())
            .source("user-management-service")
            .data(UserCreatedEvent.Data.builder()
                .userId(user.getId().toString())
                .username(user.getUsername())
                .email(user.getEmail())
                .enabled(user.isEnabled())
                .createdAt(user.getCreatedAt())
                .build())
            .build();
            
        kafkaTemplate.send("user.events", user.getId().toString(), event);
    }
}
```

### Step 8: Implement Web Layer

#### REST Controller
```java
package com.nttdata.ndvn.user.web.controller;

import com.nttdata.ndvn.user.application.dto.CreateUserRequest;
import com.nttdata.ndvn.user.application.dto.UserDto;
import com.nttdata.ndvn.user.application.service.UserApplicationService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/users")
public class UserController {
    
    private final UserApplicationService userApplicationService;
    
    public UserController(UserApplicationService userApplicationService) {
        this.userApplicationService = userApplicationService;
    }
    
    @PostMapping
    public ResponseEntity<UserDto> createUser(@Valid @RequestBody CreateUserRequest request) {
        UserDto user = userApplicationService.createUser(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(user);
    }
    
    @GetMapping("/{userId}")
    public ResponseEntity<UserDto> getUser(@PathVariable UUID userId) {
        UserDto user = userApplicationService.getUser(userId);
        return ResponseEntity.ok(user);
    }
}
```

#### Main Application Class
```java
package com.nttdata.ndvn.user.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = "com.nttdata.ndvn.user")
public class UserManagementApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(UserManagementApplication.class, args);
    }
}
```

## Next Steps

### Phase 2: Implement Remaining Services
1. **Customer Management SCS** - Follow the same pattern as User Management
2. **Product Catalog SCS** - Add search capabilities with Elasticsearch
3. **Order Management SCS** - Implement complex business workflows
4. **Notification SCS** - Add email/SMS integration

### Phase 3: Integration and Testing
1. **Set up integration tests** between services
2. **Implement contract testing** with Pact
3. **Add end-to-end testing** with TestContainers
4. **Performance testing** with load simulation

### Phase 4: Production Deployment
1. **Kubernetes deployment** manifests
2. **CI/CD pipeline** configuration
3. **Monitoring and alerting** setup
4. **Data migration** execution

## Best Practices

### Code Organization
- Keep domain logic pure and framework-agnostic
- Use dependency injection consistently
- Implement proper error handling and validation
- Follow consistent naming conventions

### Testing Strategy
- Unit tests for domain logic
- Integration tests for repository implementations
- Contract tests for API interactions
- End-to-end tests for business workflows

### Monitoring and Observability
- Add correlation IDs to all requests
- Implement structured logging
- Use distributed tracing
- Monitor business metrics and SLAs

This implementation guide provides a solid foundation for building the SCS architecture while maintaining the proven patterns from the original Terasoluna framework.
