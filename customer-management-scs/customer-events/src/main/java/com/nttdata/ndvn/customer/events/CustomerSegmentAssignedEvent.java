package com.nttdata.ndvn.customer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.UUID;

public class CustomerSegmentAssignedEvent {
    private UUID eventId;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    private UUID customerId;
    private String customerNumber;
    private String segmentName;
    private String assignedBy;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime assignedAt;

    public CustomerSegmentAssignedEvent() {}

    private CustomerSegmentAssignedEvent(Builder builder) {
        this.eventId = builder.eventId;
        this.eventTime = builder.eventTime;
        this.customerId = builder.customerId;
        this.customerNumber = builder.customerNumber;
        this.segmentName = builder.segmentName;
        this.assignedBy = builder.assignedBy;
        this.assignedAt = builder.assignedAt;
    }

    public static Builder builder() { return new Builder(); }

    public static class Builder {
        private UUID eventId;
        private LocalDateTime eventTime;
        private UUID customerId;
        private String customerNumber;
        private String segmentName;
        private String assignedBy;
        private LocalDateTime assignedAt;

        public Builder eventId(UUID eventId) { this.eventId = eventId; return this; }
        public Builder eventTime(LocalDateTime eventTime) { this.eventTime = eventTime; return this; }
        public Builder customerId(UUID customerId) { this.customerId = customerId; return this; }
        public Builder customerNumber(String customerNumber) { this.customerNumber = customerNumber; return this; }
        public Builder segmentName(String segmentName) { this.segmentName = segmentName; return this; }
        public Builder assignedBy(String assignedBy) { this.assignedBy = assignedBy; return this; }
        public Builder assignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; return this; }
        public CustomerSegmentAssignedEvent build() { return new CustomerSegmentAssignedEvent(this); }
    }

    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    public String getSegmentName() { return segmentName; }
    public void setSegmentName(String segmentName) { this.segmentName = segmentName; }
    public String getAssignedBy() { return assignedBy; }
    public void setAssignedBy(String assignedBy) { this.assignedBy = assignedBy; }
    public LocalDateTime getAssignedAt() { return assignedAt; }
    public void setAssignedAt(LocalDateTime assignedAt) { this.assignedAt = assignedAt; }
}
