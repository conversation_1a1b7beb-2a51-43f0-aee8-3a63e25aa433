package com.nttdata.ndvn.web;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

import com.nttdata.ndvn.application.config.ApplicationConfig;

/**
 * Main Spring Boot application class.
 * This class serves as the entry point for the Terasoluna-based backend application.
 */
@SpringBootApplication
@Import(ApplicationConfig.class)
public class Application {
    
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
