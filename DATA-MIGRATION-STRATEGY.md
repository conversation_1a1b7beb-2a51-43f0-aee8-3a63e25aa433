# Data Migration Strategy for SCS Architecture

## Overview

This document outlines the comprehensive data migration strategy for transforming the NDVN Terasoluna Base Project from a monolithic database structure to independent databases for each Self-Contained System (SCS). The strategy ensures data integrity, minimal downtime, and safe rollback capabilities.

## Current State Analysis

### Existing Database Structure
The current monolithic application uses a single database with the following characteristics:
- **Database:** H2 (development) / PostgreSQL (production)
- **Schema:** Single schema with all domain tables
- **Transactions:** ACID transactions across all domains
- **Relationships:** Foreign key constraints between domains
- **Data Volume:** Estimated based on typical enterprise applications

### Identified Data Domains
Based on the SCS design, data will be separated into the following domains:

1. **User Management Data**
   - Users, roles, permissions, sessions
   - Authentication and authorization data

2. **Customer Management Data**
   - Customer profiles, contacts, addresses
   - Customer preferences and classifications

3. **Product Catalog Data**
   - Products, categories, inventory
   - Pricing rules and promotions

4. **Order Management Data**
   - Orders, order items, fulfillment
   - Order status and tracking information

5. **Notification Data**
   - Notification templates and logs
   - Delivery tracking and preferences

## Migration Strategy Overview

### Migration Principles
1. **Zero-Downtime Migration:** Use blue-green deployment strategy
2. **Data Consistency:** Maintain referential integrity during migration
3. **Rollback Capability:** Ability to revert to monolithic structure
4. **Incremental Migration:** Migrate one domain at a time
5. **Data Validation:** Comprehensive validation at each step

### Migration Phases

#### Phase 1: Database Schema Analysis and Preparation
**Duration:** 1 week
**Objective:** Analyze current schema and prepare migration scripts

**Tasks:**
1. **Schema Discovery**
   - Document all existing tables and relationships
   - Identify foreign key dependencies between domains
   - Map data ownership to target SCS services
   - Analyze data volume and growth patterns

2. **Dependency Analysis**
   - Create dependency graph of table relationships
   - Identify circular dependencies that need resolution
   - Plan order of domain extraction based on dependencies
   - Document shared reference data

3. **Migration Script Development**
   - Create DDL scripts for each SCS database
   - Develop data extraction and transformation scripts
   - Create validation scripts for data integrity
   - Prepare rollback scripts for each migration step

#### Phase 2: Infrastructure Setup
**Duration:** 1 week
**Objective:** Set up target database infrastructure

**Tasks:**
1. **Database Provisioning**
   - Create separate PostgreSQL instances for each SCS
   - Configure connection pooling and performance settings
   - Set up database monitoring and alerting
   - Configure backup and recovery procedures

2. **Migration Environment Setup**
   - Set up staging environment mirroring production
   - Create data migration pipeline infrastructure
   - Set up data validation and testing tools
   - Configure logging and monitoring for migration process

#### Phase 3: User Management Data Migration
**Duration:** 1 week
**Objective:** Extract user authentication and authorization data

**Migration Steps:**
1. **Schema Creation**
   ```sql
   -- Create user management database schema
   CREATE DATABASE user_management;
   
   -- Users table
   CREATE TABLE users (
       id UUID PRIMARY KEY,
       username VARCHAR(50) UNIQUE NOT NULL,
       email VARCHAR(100) UNIQUE NOT NULL,
       password_hash VARCHAR(255) NOT NULL,
       enabled BOOLEAN DEFAULT true,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   -- Roles and permissions tables
   CREATE TABLE roles (
       id UUID PRIMARY KEY,
       name VARCHAR(50) UNIQUE NOT NULL,
       description VARCHAR(255)
   );
   
   CREATE TABLE user_roles (
       user_id UUID REFERENCES users(id),
       role_id UUID REFERENCES roles(id),
       PRIMARY KEY (user_id, role_id)
   );
   ```

2. **Data Migration**
   ```sql
   -- Extract user data from monolithic database
   INSERT INTO user_management.users 
   SELECT id, username, email, password_hash, enabled, created_at, updated_at
   FROM monolith.users;
   
   -- Extract role data
   INSERT INTO user_management.roles
   SELECT id, name, description
   FROM monolith.roles;
   
   -- Extract user-role relationships
   INSERT INTO user_management.user_roles
   SELECT user_id, role_id
   FROM monolith.user_roles;
   ```

3. **Validation**
   - Verify record counts match between source and target
   - Validate data integrity and constraints
   - Test authentication flows with migrated data
   - Performance testing of user management operations

#### Phase 4: Customer Management Data Migration
**Duration:** 1 week
**Objective:** Extract customer-related business data

**Migration Steps:**
1. **Schema Creation**
   ```sql
   CREATE DATABASE customer_management;
   
   CREATE TABLE customers (
       id UUID PRIMARY KEY,
       user_id UUID, -- Reference to user management (no FK)
       customer_number VARCHAR(50) UNIQUE NOT NULL,
       company_name VARCHAR(255),
       first_name VARCHAR(100),
       last_name VARCHAR(100),
       email VARCHAR(100),
       phone VARCHAR(20),
       status VARCHAR(20) DEFAULT 'ACTIVE',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   CREATE TABLE customer_addresses (
       id UUID PRIMARY KEY,
       customer_id UUID REFERENCES customers(id),
       address_type VARCHAR(20),
       street_address VARCHAR(255),
       city VARCHAR(100),
       state VARCHAR(50),
       postal_code VARCHAR(20),
       country VARCHAR(50),
       is_primary BOOLEAN DEFAULT false
   );
   ```

2. **Data Migration with Transformation**
   ```sql
   -- Migrate customer data with denormalization
   INSERT INTO customer_management.customers
   SELECT 
       c.id,
       c.user_id,
       c.customer_number,
       c.company_name,
       c.first_name,
       c.last_name,
       c.email,
       c.phone,
       c.status,
       c.created_at,
       c.updated_at
   FROM monolith.customers c;
   
   -- Migrate address data
   INSERT INTO customer_management.customer_addresses
   SELECT id, customer_id, address_type, street_address, 
          city, state, postal_code, country, is_primary
   FROM monolith.customer_addresses;
   ```

#### Phase 5: Product Catalog Data Migration
**Duration:** 1 week
**Objective:** Extract product and inventory data

**Migration Steps:**
1. **Schema Creation**
   ```sql
   CREATE DATABASE product_catalog;
   
   CREATE TABLE categories (
       id UUID PRIMARY KEY,
       name VARCHAR(100) NOT NULL,
       description TEXT,
       parent_id UUID REFERENCES categories(id),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   CREATE TABLE products (
       id UUID PRIMARY KEY,
       sku VARCHAR(50) UNIQUE NOT NULL,
       name VARCHAR(255) NOT NULL,
       description TEXT,
       category_id UUID REFERENCES categories(id),
       price DECIMAL(10,2),
       status VARCHAR(20) DEFAULT 'ACTIVE',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   CREATE TABLE inventory (
       id UUID PRIMARY KEY,
       product_id UUID REFERENCES products(id),
       quantity_available INTEGER DEFAULT 0,
       quantity_reserved INTEGER DEFAULT 0,
       reorder_level INTEGER DEFAULT 0,
       last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

2. **Data Migration**
   - Extract product hierarchy and categories
   - Migrate product master data
   - Transfer inventory levels and reservations
   - Validate product relationships and constraints

#### Phase 6: Order Management Data Migration
**Duration:** 1 week
**Objective:** Extract order processing data

**Migration Steps:**
1. **Schema Creation**
   ```sql
   CREATE DATABASE order_management;
   
   CREATE TABLE orders (
       id UUID PRIMARY KEY,
       order_number VARCHAR(50) UNIQUE NOT NULL,
       customer_id UUID NOT NULL, -- Reference to customer (no FK)
       customer_name VARCHAR(255), -- Denormalized for performance
       customer_email VARCHAR(100), -- Denormalized for performance
       order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       status VARCHAR(20) DEFAULT 'PENDING',
       total_amount DECIMAL(10,2),
       shipping_address JSONB,
       billing_address JSONB,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   CREATE TABLE order_items (
       id UUID PRIMARY KEY,
       order_id UUID REFERENCES orders(id),
       product_id UUID NOT NULL, -- Reference to product (no FK)
       product_name VARCHAR(255), -- Denormalized
       product_sku VARCHAR(50), -- Denormalized
       quantity INTEGER NOT NULL,
       unit_price DECIMAL(10,2),
       total_price DECIMAL(10,2)
   );
   ```

2. **Data Migration with Denormalization**
   - Extract order data with customer information
   - Denormalize product information in order items
   - Maintain order history and audit trails
   - Validate order totals and calculations

#### Phase 7: Notification Data Migration
**Duration:** 3 days
**Objective:** Extract notification and communication data

**Migration Steps:**
1. **Schema Creation**
   ```sql
   CREATE DATABASE notification_service;
   
   CREATE TABLE notification_templates (
       id UUID PRIMARY KEY,
       name VARCHAR(100) UNIQUE NOT NULL,
       subject VARCHAR(255),
       body TEXT,
       template_type VARCHAR(50),
       channel VARCHAR(20),
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   
   CREATE TABLE notifications (
       id UUID PRIMARY KEY,
       template_id UUID REFERENCES notification_templates(id),
       recipient_id UUID NOT NULL,
       recipient_email VARCHAR(100),
       subject VARCHAR(255),
       body TEXT,
       channel VARCHAR(20),
       status VARCHAR(20) DEFAULT 'PENDING',
       sent_at TIMESTAMP,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
   );
   ```

2. **Data Migration**
   - Extract notification templates
   - Migrate notification history
   - Set up delivery tracking data

## Data Consistency Strategy

### Referential Integrity Handling
1. **Remove Foreign Key Constraints**
   - Replace with application-level validation
   - Use eventual consistency patterns
   - Implement data validation in application services

2. **Data Denormalization**
   - Store frequently accessed data locally in each service
   - Use event-driven updates to maintain consistency
   - Accept eventual consistency for non-critical data

3. **Shared Reference Data**
   - Identify truly shared reference data (countries, currencies)
   - Create shared reference data service or replicate across services
   - Use events to synchronize reference data updates

### Migration Validation

#### Data Integrity Checks
```sql
-- Validate record counts
SELECT 'users' as table_name, 
       (SELECT COUNT(*) FROM monolith.users) as source_count,
       (SELECT COUNT(*) FROM user_management.users) as target_count;

-- Validate data consistency
SELECT u1.id, u1.username, u1.email
FROM monolith.users u1
LEFT JOIN user_management.users u2 ON u1.id = u2.id
WHERE u2.id IS NULL;
```

#### Business Logic Validation
- Test critical business workflows end-to-end
- Validate calculated fields and aggregations
- Verify audit trails and timestamps
- Test data access patterns and performance

### Rollback Strategy

#### Rollback Triggers
- Data validation failures
- Performance degradation
- Business logic errors
- Critical system failures

#### Rollback Process
1. **Stop Migration Process**
   - Halt data migration scripts
   - Stop new service deployments
   - Preserve current state for analysis

2. **Data Restoration**
   - Restore from pre-migration backups
   - Replay transaction logs if necessary
   - Validate restored data integrity

3. **Service Rollback**
   - Revert to monolithic application
   - Restore original database connections
   - Validate system functionality

## Migration Timeline

### Week 1: Preparation and Analysis
- Schema analysis and documentation
- Migration script development
- Infrastructure setup

### Week 2: User Management Migration
- User data extraction and migration
- Authentication service deployment
- Integration testing

### Week 3: Customer Management Migration
- Customer data migration
- Customer service deployment
- Cross-service integration testing

### Week 4: Product Catalog Migration
- Product and inventory data migration
- Catalog service deployment
- Order integration testing

### Week 5: Order Management Migration
- Order data migration with denormalization
- Order service deployment
- End-to-end workflow testing

### Week 6: Notification Migration and Final Testing
- Notification data migration
- Complete system integration testing
- Performance validation and optimization

## Risk Mitigation

### Technical Risks
1. **Data Loss Prevention**
   - Multiple backup points before each migration step
   - Transaction log preservation
   - Automated data validation scripts

2. **Performance Impact**
   - Migration during low-traffic periods
   - Gradual traffic shifting
   - Performance monitoring throughout process

3. **Integration Failures**
   - Comprehensive integration testing
   - Fallback to synchronous calls if events fail
   - Circuit breaker patterns for resilience

### Business Risks
1. **Service Disruption**
   - Blue-green deployment strategy
   - Feature flags for gradual rollout
   - 24/7 monitoring during migration

2. **Data Inconsistency**
   - Real-time data validation
   - Automated reconciliation processes
   - Manual verification procedures

This migration strategy ensures a safe, controlled transition from monolithic to SCS architecture while maintaining data integrity and system availability throughout the process.
