package com.nttdata.ndvn.customer.domain.model;

import jakarta.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Customer aggregate root representing a customer entity in the system.
 * 
 * This entity encapsulates all customer-related business logic and maintains
 * consistency within the customer bounded context. It supports both individual
 * and business customers with comprehensive profile management.
 */
@Entity
@Table(name = "customers", indexes = {
    @Index(name = "idx_customer_number", columnList = "customerNumber", unique = true),
    @Index(name = "idx_customer_email", columnList = "email"),
    @Index(name = "idx_customer_user_id", columnList = "userId"),
    @Index(name = "idx_customer_status", columnList = "status"),
    @Index(name = "idx_customer_classification", columnList = "classification"),
    @Index(name = "idx_customer_created_at", columnList = "createdAt")
})
public class Customer {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(name = "customer_number", unique = true, nullable = false, length = 50)
    private String customerNumber;
    
    @Column(name = "user_id")
    private UUID userId; // Reference to user management (loose coupling)
    
    @Enumerated(EnumType.STRING)
    @Column(name = "customer_type", nullable = false)
    private CustomerType customerType = CustomerType.INDIVIDUAL;
    
    @Column(name = "company_name")
    private String companyName;
    
    @Column(name = "first_name", length = 100)
    private String firstName;
    
    @Column(name = "last_name", length = 100)
    private String lastName;
    
    @Column(name = "email", nullable = false, length = 100)
    private String email;
    
    @Column(name = "phone", length = 20)
    private String phone;
    
    @Column(name = "date_of_birth")
    private LocalDate dateOfBirth;
    
    @Column(name = "tax_id", length = 50)
    private String taxId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CustomerStatus status = CustomerStatus.ACTIVE;
    
    @Column(name = "classification", length = 50)
    private String classification = "STANDARD";
    
    @Column(name = "credit_limit", precision = 15, scale = 2)
    private BigDecimal creditLimit = BigDecimal.ZERO;
    
    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CustomerAddress> addresses = new ArrayList<>();
    
    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CustomerNote> notes = new ArrayList<>();
    
    @OneToMany(mappedBy = "customer", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CustomerSegmentAssignment> segmentAssignments = new ArrayList<>();
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Constructors
    public Customer() {
        // Default constructor for JPA and MapStruct
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    private Customer(Builder builder) {
        this.customerNumber = builder.customerNumber;
        this.userId = builder.userId;
        this.customerType = builder.customerType;
        this.companyName = builder.companyName;
        this.firstName = builder.firstName;
        this.lastName = builder.lastName;
        this.email = builder.email;
        this.phone = builder.phone;
        this.dateOfBirth = builder.dateOfBirth;
        this.taxId = builder.taxId;
        this.status = builder.status;
        this.classification = builder.classification;
        this.creditLimit = builder.creditLimit;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    public static Builder builder() {
        return new Builder();
    }
    
    // Business methods
    
    public void updateProfile(String firstName, String lastName, String phone, LocalDate dateOfBirth) {
        this.firstName = firstName;
        this.lastName = lastName;
        this.phone = phone;
        this.dateOfBirth = dateOfBirth;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void updateBusinessProfile(String companyName, String taxId) {
        if (this.customerType != CustomerType.BUSINESS) {
            throw new IllegalStateException("Cannot update business profile for non-business customer");
        }
        this.companyName = companyName;
        this.taxId = taxId;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void changeStatus(CustomerStatus newStatus) {
        if (this.status == newStatus) {
            return;
        }
        this.status = newStatus;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void updateClassification(String classification) {
        this.classification = classification;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void updateCreditLimit(BigDecimal creditLimit) {
        if (creditLimit.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Credit limit cannot be negative");
        }
        this.creditLimit = creditLimit;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void addAddress(CustomerAddress address) {
        address.setCustomer(this);
        this.addresses.add(address);
        this.updatedAt = LocalDateTime.now();
    }
    
    public void addNote(CustomerNote note) {
        note.setCustomer(this);
        this.notes.add(note);
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDisplayName() {
        if (customerType == CustomerType.BUSINESS && companyName != null) {
            return companyName;
        }
        if (firstName != null && lastName != null) {
            return firstName + " " + lastName;
        }
        if (firstName != null) {
            return firstName;
        }
        return email;
    }
    
    public boolean isActive() {
        return status == CustomerStatus.ACTIVE;
    }
    
    public boolean isBusiness() {
        return customerType == CustomerType.BUSINESS;
    }
    
    // Builder pattern
    public static class Builder {
        private String customerNumber;
        private UUID userId;
        private CustomerType customerType = CustomerType.INDIVIDUAL;
        private String companyName;
        private String firstName;
        private String lastName;
        private String email;
        private String phone;
        private LocalDate dateOfBirth;
        private String taxId;
        private CustomerStatus status = CustomerStatus.ACTIVE;
        private String classification = "STANDARD";
        private BigDecimal creditLimit = BigDecimal.ZERO;
        
        public Builder customerNumber(String customerNumber) {
            this.customerNumber = customerNumber;
            return this;
        }
        
        public Builder userId(UUID userId) {
            this.userId = userId;
            return this;
        }
        
        public Builder customerType(CustomerType customerType) {
            this.customerType = customerType;
            return this;
        }
        
        public Builder companyName(String companyName) {
            this.companyName = companyName;
            return this;
        }
        
        public Builder firstName(String firstName) {
            this.firstName = firstName;
            return this;
        }
        
        public Builder lastName(String lastName) {
            this.lastName = lastName;
            return this;
        }
        
        public Builder email(String email) {
            this.email = email;
            return this;
        }
        
        public Builder phone(String phone) {
            this.phone = phone;
            return this;
        }
        
        public Builder dateOfBirth(LocalDate dateOfBirth) {
            this.dateOfBirth = dateOfBirth;
            return this;
        }
        
        public Builder taxId(String taxId) {
            this.taxId = taxId;
            return this;
        }
        
        public Builder status(CustomerStatus status) {
            this.status = status;
            return this;
        }
        
        public Builder classification(String classification) {
            this.classification = classification;
            return this;
        }
        
        public Builder creditLimit(BigDecimal creditLimit) {
            this.creditLimit = creditLimit;
            return this;
        }
        
        public Customer build() {
            Objects.requireNonNull(customerNumber, "Customer number is required");
            Objects.requireNonNull(email, "Email is required");
            return new Customer(this);
        }
    }
    
    // Getters and setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }

    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }

    public UUID getUserId() { return userId; }
    public void setUserId(UUID userId) { this.userId = userId; }

    public CustomerType getCustomerType() { return customerType; }
    public void setCustomerType(CustomerType customerType) { this.customerType = customerType; }

    public String getCompanyName() { return companyName; }
    public void setCompanyName(String companyName) { this.companyName = companyName; }

    public String getFirstName() { return firstName; }
    public void setFirstName(String firstName) { this.firstName = firstName; }

    public String getLastName() { return lastName; }
    public void setLastName(String lastName) { this.lastName = lastName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }
    public void setPhone(String phone) { this.phone = phone; }

    public LocalDate getDateOfBirth() { return dateOfBirth; }
    public void setDateOfBirth(LocalDate dateOfBirth) { this.dateOfBirth = dateOfBirth; }

    public String getTaxId() { return taxId; }
    public void setTaxId(String taxId) { this.taxId = taxId; }

    public CustomerStatus getStatus() { return status; }
    public void setStatus(CustomerStatus status) { this.status = status; }

    public String getClassification() { return classification; }
    public void setClassification(String classification) { this.classification = classification; }

    public BigDecimal getCreditLimit() { return creditLimit; }
    public void setCreditLimit(BigDecimal creditLimit) { this.creditLimit = creditLimit; }

    public List<CustomerAddress> getAddresses() { return addresses; }
    public void setAddresses(List<CustomerAddress> addresses) { this.addresses = addresses; }

    public List<CustomerNote> getNotes() { return notes; }
    public void setNotes(List<CustomerNote> notes) { this.notes = notes; }

    public List<CustomerSegmentAssignment> getSegmentAssignments() { return segmentAssignments; }
    public void setSegmentAssignments(List<CustomerSegmentAssignment> segmentAssignments) { this.segmentAssignments = segmentAssignments; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Customer customer = (Customer) o;
        return Objects.equals(id, customer.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Customer{" +
                "id=" + id +
                ", customerNumber='" + customerNumber + '\'' +
                ", customerType=" + customerType +
                ", email='" + email + '\'' +
                ", status=" + status +
                '}';
    }
}
