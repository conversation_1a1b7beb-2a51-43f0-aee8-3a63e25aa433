package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.ProductDto;
import com.nttdata.ndvn.product.domain.model.Product;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for Product entity and DTO.
 * 
 * This mapper handles the conversion between Product domain entities
 * and ProductDto data transfer objects.
 */
@Mapper(componentModel = "spring", 
        uses = {ProductVariantMapper.class, ProductAttributeMapper.class, ProductImageMapper.class, InventoryMapper.class},
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductMapper {
    
    /**
     * Maps Product entity to ProductDto.
     */
    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "categoryPath", ignore = true)
    @Mapping(target = "brandName", ignore = true)
    @Mapping(target = "effectivePrice", source = "effectivePrice")
    @Mapping(target = "onSale", source = "onSale")
    @Mapping(target = "available", source = "available")
    @Mapping(target = "inStock", ignore = true)
    @Mapping(target = "totalStock", ignore = true)
    @Mapping(target = "availableStock", ignore = true)
    @Mapping(target = "discountPercentage", ignore = true)
    @Mapping(target = "savingsAmount", ignore = true)
    ProductDto toDto(Product product);
    
    /**
     * Maps ProductDto to Product entity.
     */
    @Mapping(target = "variants", ignore = true)
    @Mapping(target = "attributes", ignore = true)
    @Mapping(target = "images", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Product toEntity(ProductDto productDto);
    
    /**
     * Maps list of Product entities to list of ProductDto.
     */
    List<ProductDto> toDtoList(List<Product> products);
    
    /**
     * Maps list of ProductDto to list of Product entities.
     */
    List<Product> toEntityList(List<ProductDto> productDtos);
    
    /**
     * Updates existing Product entity with data from ProductDto.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "variants", ignore = true)
    @Mapping(target = "attributes", ignore = true)
    @Mapping(target = "images", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(ProductDto productDto, @MappingTarget Product product);
    
    /**
     * Maps Product entity to ProductDto with additional computed fields.
     */
    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "categoryPath", ignore = true)
    @Mapping(target = "brandName", ignore = true)
    @Mapping(target = "effectivePrice", source = "effectivePrice")
    @Mapping(target = "onSale", source = "onSale")
    @Mapping(target = "available", source = "available")
    @Mapping(target = "inStock", ignore = true)
    @Mapping(target = "totalStock", ignore = true)
    @Mapping(target = "availableStock", ignore = true)
    @Mapping(target = "discountPercentage", ignore = true)
    @Mapping(target = "savingsAmount", ignore = true)
    ProductDto toDtoWithDetails(Product product);
    
    /**
     * Maps Product entity to simplified ProductDto (without collections).
     */
    @Mapping(target = "categoryName", ignore = true)
    @Mapping(target = "categoryPath", ignore = true)
    @Mapping(target = "brandName", ignore = true)
    @Mapping(target = "effectivePrice", source = "effectivePrice")
    @Mapping(target = "onSale", source = "onSale")
    @Mapping(target = "available", source = "available")
    @Mapping(target = "variants", ignore = true)
    @Mapping(target = "attributes", ignore = true)
    @Mapping(target = "images", ignore = true)
    @Mapping(target = "inventory", ignore = true)
    @Mapping(target = "inStock", ignore = true)
    @Mapping(target = "totalStock", ignore = true)
    @Mapping(target = "availableStock", ignore = true)
    @Mapping(target = "discountPercentage", ignore = true)
    @Mapping(target = "savingsAmount", ignore = true)
    ProductDto toSimpleDto(Product product);
    
    /**
     * Custom mapping for creating a new Product from ProductDto.
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "variants", ignore = true)
    @Mapping(target = "attributes", ignore = true)
    @Mapping(target = "images", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    Product toNewEntity(ProductDto productDto);
}
