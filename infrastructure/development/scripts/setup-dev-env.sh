#!/bin/bash

# NDVN SCS Development Environment Setup Script
# This script sets up the complete development environment for the SCS platform

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DEV_DIR="$(dirname "$SCRIPT_DIR")"
INFRASTRUCTURE_DIR="$(dirname "$DEV_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    log_info "Checking Docker Compose availability..."
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    log_success "Docker Compose is available"
}

# Check if network exists
check_network() {
    log_info "Checking Docker network..."
    if ! docker network ls | grep -q "ndvn-scs-network"; then
        log_info "Creating Docker network: ndvn-scs-network"
        docker network create ndvn-scs-network
    fi
    log_success "Docker network is ready"
}

# Create required directories
create_directories() {
    log_info "Creating required directories..."
    
    mkdir -p "$DEV_DIR/databases/init-scripts"
    mkdir -p "$DEV_DIR/nginx"
    mkdir -p "$DEV_DIR/nginx/html"
    mkdir -p "$DEV_DIR/dev-tools"
    
    log_success "Directories created"
}

# Generate configuration files
generate_configs() {
    log_info "Generating configuration files..."
    
    # Nginx configuration
    cat > "$DEV_DIR/nginx/nginx.conf" << 'EOF'
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log;
    
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    
    # API Gateway proxy
    upstream api_gateway {
        server api-gateway:8080;
    }
    
    # Keycloak proxy
    upstream keycloak {
        server keycloak:8080;
    }
    
    server {
        listen 80;
        server_name localhost;
        
        # Serve static files
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # Proxy API requests to API Gateway
        location /api/ {
            proxy_pass http://api_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Proxy auth requests to Keycloak
        location /auth/ {
            proxy_pass http://keycloak;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
EOF

    # Create a simple index.html
    cat > "$DEV_DIR/nginx/html/index.html" << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NDVN SCS Platform - Development Environment</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        h1 { color: #333; text-align: center; }
        .services { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px; }
        .service { padding: 20px; border: 1px solid #ddd; border-radius: 5px; background: #f9f9f9; }
        .service h3 { margin-top: 0; color: #0066cc; }
        .service a { color: #0066cc; text-decoration: none; }
        .service a:hover { text-decoration: underline; }
        .status { display: inline-block; padding: 2px 8px; border-radius: 3px; font-size: 12px; }
        .status.running { background: #d4edda; color: #155724; }
        .status.stopped { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>NDVN SCS Platform - Development Environment</h1>
        <p>Welcome to the NDVN Self-Contained Systems development environment. Below are the available services and tools.</p>
        
        <div class="services">
            <div class="service">
                <h3>Infrastructure Services</h3>
                <ul>
                    <li><a href="http://localhost:8500" target="_blank">Consul</a> - Service Discovery</li>
                    <li><a href="http://localhost:8080" target="_blank">Kafka UI</a> - Message Broker Management</li>
                    <li><a href="http://localhost:8090/admin" target="_blank">Keycloak</a> - Authentication Server</li>
                    <li><a href="http://localhost:6379" target="_blank">Redis</a> - Caching & Session Store</li>
                </ul>
            </div>
            
            <div class="service">
                <h3>Monitoring & Observability</h3>
                <ul>
                    <li><a href="http://localhost:3000" target="_blank">Grafana</a> - Metrics Dashboard</li>
                    <li><a href="http://localhost:9090" target="_blank">Prometheus</a> - Metrics Collection</li>
                    <li><a href="http://localhost:5601" target="_blank">Kibana</a> - Log Analysis</li>
                    <li><a href="http://localhost:16686" target="_blank">Jaeger</a> - Distributed Tracing</li>
                </ul>
            </div>
            
            <div class="service">
                <h3>Development Tools</h3>
                <ul>
                    <li><a href="http://localhost:8082" target="_blank">Adminer</a> - Database Management</li>
                    <li><a href="http://localhost:8083" target="_blank">Redis Commander</a> - Redis Management</li>
                    <li><a href="http://localhost:8025" target="_blank">MailHog</a> - Email Testing</li>
                    <li><a href="http://localhost:9001" target="_blank">MinIO Console</a> - Object Storage</li>
                </ul>
            </div>
            
            <div class="service">
                <h3>SCS Services</h3>
                <ul>
                    <li><a href="http://localhost:8081/actuator/health" target="_blank">User Management</a> - User & Auth Service</li>
                    <li><a href="http://localhost:8082/actuator/health" target="_blank">Customer Management</a> - Customer Service</li>
                    <li><a href="http://localhost:8083/actuator/health" target="_blank">Product Catalog</a> - Product Service</li>
                    <li><a href="http://localhost:8084/actuator/health" target="_blank">Order Management</a> - Order Service</li>
                    <li><a href="http://localhost:8085/actuator/health" target="_blank">Notification</a> - Notification Service</li>
                </ul>
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e9ecef; border-radius: 5px;">
            <h3>Quick Start Commands</h3>
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 3px; overflow-x: auto;">
# Start all infrastructure services
./infrastructure/scripts/start-all.sh

# Connect to development tools container
docker exec -it ndvn-dev-tools bash

# Connect to databases
db-connect user      # User Management DB
db-connect customer  # Customer Management DB
db-connect catalog   # Product Catalog DB
db-connect order     # Order Management DB

# Kafka operations
kafka-list-topics
kafka-create-topic test-topic
            </pre>
        </div>
    </div>
</body>
</html>
EOF

    log_success "Configuration files generated"
}

# Build development tools image
build_dev_tools() {
    log_info "Building development tools Docker image..."
    
    cd "$DEV_DIR/dev-tools"
    docker build -t ndvn/dev-tools:latest .
    
    log_success "Development tools image built successfully"
}

# Start development environment
start_dev_env() {
    log_info "Starting development environment..."
    
    cd "$DEV_DIR"
    
    # Start services
    docker-compose -f docker-compose-dev.yml up -d
    
    log_success "Development environment started"
}

# Wait for databases to be ready
wait_for_databases() {
    log_info "Waiting for databases to be ready..."
    
    local databases=("user-db:5432" "customer-db:5432" "catalog-db:5432" "order-db:5432" "notification-db:5432")
    local max_attempts=30
    
    for db in "${databases[@]}"; do
        local host=$(echo $db | cut -d: -f1)
        local port=$(echo $db | cut -d: -f2)
        local attempt=1
        
        log_info "Waiting for $host..."
        while [ $attempt -le $max_attempts ]; do
            if docker exec ndvn-$host pg_isready -h localhost -p $port > /dev/null 2>&1; then
                log_success "$host is ready"
                break
            fi
            log_info "Attempt $attempt/$max_attempts: $host not ready yet, waiting 5 seconds..."
            sleep 5
            ((attempt++))
        done
        
        if [ $attempt -gt $max_attempts ]; then
            log_error "$host failed to start within expected time"
            return 1
        fi
    done
}

# Show status
show_status() {
    log_info "Development Environment Status:"
    echo ""
    docker-compose -f "$DEV_DIR/docker-compose-dev.yml" ps
    echo ""
    log_info "Access URLs:"
    echo "  - Development Dashboard: http://localhost:8084"
    echo "  - Database Management: http://localhost:8082"
    echo "  - Redis Management: http://localhost:8083"
    echo "  - Email Testing: http://localhost:8025"
    echo "  - Object Storage: http://localhost:9001 (minioadmin/minioadmin123)"
    echo ""
    log_info "Database Connections:"
    echo "  - User DB: localhost:5432 (user_service/password)"
    echo "  - Customer DB: localhost:5433 (customer_service/password)"
    echo "  - Catalog DB: localhost:5434 (catalog_service/password)"
    echo "  - Order DB: localhost:5435 (order_service/password)"
    echo "  - Notification DB: localhost:5436 (notification_service/password)"
    echo ""
    log_info "Development Tools:"
    echo "  - Access dev container: docker exec -it ndvn-dev-tools bash"
    echo "  - Redis: localhost:6379 (password: redis-password)"
    echo ""
}

# Stop development environment
stop_dev_env() {
    log_info "Stopping development environment..."
    cd "$DEV_DIR"
    docker-compose -f docker-compose-dev.yml down
    log_success "Development environment stopped"
}

# Clean up development environment
cleanup_dev_env() {
    log_warning "This will remove all development data and containers. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up development environment..."
        cd "$DEV_DIR"
        docker-compose -f docker-compose-dev.yml down -v --remove-orphans
        docker rmi ndvn/dev-tools:latest 2>/dev/null || true
        log_success "Development environment cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        start)
            check_docker
            check_docker_compose
            check_network
            create_directories
            generate_configs
            build_dev_tools
            start_dev_env
            wait_for_databases
            show_status
            ;;
        stop)
            stop_dev_env
            ;;
        restart)
            stop_dev_env
            sleep 5
            main start
            ;;
        build)
            build_dev_tools
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup_dev_env
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|build|status|cleanup}"
            echo ""
            echo "Commands:"
            echo "  start    - Start development environment"
            echo "  stop     - Stop development environment"
            echo "  restart  - Restart development environment"
            echo "  build    - Build development tools image"
            echo "  status   - Show environment status"
            echo "  cleanup  - Remove all development data and containers"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
