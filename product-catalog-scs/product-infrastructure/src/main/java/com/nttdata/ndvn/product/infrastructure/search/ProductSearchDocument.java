package com.nttdata.ndvn.product.infrastructure.search;

import com.nttdata.ndvn.product.domain.model.Product;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Elasticsearch document for product search.
 * 
 * This document represents a product in the search index with optimized
 * fields for search and filtering operations.
 */
@Document(indexName = "products")
public class ProductSearchDocument {
    
    @Id
    private String id;
    
    @Field(type = FieldType.Keyword)
    private String sku;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String name;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String description;
    
    @Field(type = FieldType.Text, analyzer = "standard")
    private String longDescription;
    
    @Field(type = FieldType.Keyword)
    private String categoryId;
    
    @Field(type = FieldType.Text)
    private String categoryName;
    
    @Field(type = FieldType.Text)
    private String categoryPath;
    
    @Field(type = FieldType.Keyword)
    private String brandId;
    
    @Field(type = FieldType.Text)
    private String brandName;
    
    @Field(type = FieldType.Double)
    private BigDecimal basePrice;
    
    @Field(type = FieldType.Double)
    private BigDecimal salePrice;
    
    @Field(type = FieldType.Double)
    private BigDecimal effectivePrice;
    
    @Field(type = FieldType.Keyword)
    private ProductStatus status;
    
    @Field(type = FieldType.Boolean)
    private boolean featured;
    
    @Field(type = FieldType.Boolean)
    private boolean digital;
    
    @Field(type = FieldType.Boolean)
    private boolean requiresShipping;
    
    @Field(type = FieldType.Boolean)
    private boolean taxable;
    
    @Field(type = FieldType.Boolean)
    private boolean onSale;
    
    @Field(type = FieldType.Boolean)
    private boolean inStock;
    
    @Field(type = FieldType.Integer)
    private Integer availableQuantity;
    
    @Field(type = FieldType.Text)
    private List<String> attributes;
    
    @Field(type = FieldType.Text)
    private List<String> variants;
    
    @Field(type = FieldType.Text)
    private List<String> tags;
    
    @Field(type = FieldType.Text)
    private String metaKeywords;
    
    @Field(type = FieldType.Date)
    private LocalDateTime createdAt;
    
    @Field(type = FieldType.Date)
    private LocalDateTime updatedAt;
    
    @Field(type = FieldType.Integer)
    private Integer sortOrder;
    
    @Field(type = FieldType.Double)
    private Double weight;
    
    @Field(type = FieldType.Text)
    private String weightUnit;
    
    @Field(type = FieldType.Text)
    private List<String> imageUrls;
    
    @Field(type = FieldType.Text)
    private String primaryImageUrl;
    
    // Constructors
    public ProductSearchDocument() {}
    
    public ProductSearchDocument(Product product) {
        this.id = product.getId().toString();
        this.sku = product.getSku();
        this.name = product.getName();
        this.description = product.getDescription();
        this.longDescription = product.getLongDescription();
        this.categoryId = product.getCategoryId() != null ? product.getCategoryId().toString() : null;
        this.brandId = product.getBrandId() != null ? product.getBrandId().toString() : null;
        this.basePrice = product.getBasePrice();
        this.salePrice = product.getSalePrice();
        this.effectivePrice = product.getEffectivePrice();
        this.status = product.getStatus();
        this.featured = product.isFeatured();
        this.digital = product.isDigital();
        this.requiresShipping = product.isRequiresShipping();
        this.taxable = product.isTaxable();
        this.onSale = product.isOnSale();
        this.metaKeywords = product.getMetaKeywords();
        this.createdAt = product.getCreatedAt();
        this.updatedAt = product.getUpdatedAt();
        this.sortOrder = product.getSortOrder();
        this.weight = product.getWeight() != null ? product.getWeight().doubleValue() : null;
        this.weightUnit = product.getWeightUnit();
    }
    
    // Getters and setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getSku() {
        return sku;
    }
    
    public void setSku(String sku) {
        this.sku = sku;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getLongDescription() {
        return longDescription;
    }
    
    public void setLongDescription(String longDescription) {
        this.longDescription = longDescription;
    }
    
    public String getCategoryId() {
        return categoryId;
    }
    
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }
    
    public String getCategoryName() {
        return categoryName;
    }
    
    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    
    public String getCategoryPath() {
        return categoryPath;
    }
    
    public void setCategoryPath(String categoryPath) {
        this.categoryPath = categoryPath;
    }
    
    public String getBrandId() {
        return brandId;
    }
    
    public void setBrandId(String brandId) {
        this.brandId = brandId;
    }
    
    public String getBrandName() {
        return brandName;
    }
    
    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }
    
    public BigDecimal getBasePrice() {
        return basePrice;
    }
    
    public void setBasePrice(BigDecimal basePrice) {
        this.basePrice = basePrice;
    }
    
    public BigDecimal getSalePrice() {
        return salePrice;
    }
    
    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }
    
    public BigDecimal getEffectivePrice() {
        return effectivePrice;
    }
    
    public void setEffectivePrice(BigDecimal effectivePrice) {
        this.effectivePrice = effectivePrice;
    }
    
    public ProductStatus getStatus() {
        return status;
    }
    
    public void setStatus(ProductStatus status) {
        this.status = status;
    }
    
    public boolean isFeatured() {
        return featured;
    }
    
    public void setFeatured(boolean featured) {
        this.featured = featured;
    }
    
    public boolean isDigital() {
        return digital;
    }
    
    public void setDigital(boolean digital) {
        this.digital = digital;
    }
    
    public boolean isRequiresShipping() {
        return requiresShipping;
    }
    
    public void setRequiresShipping(boolean requiresShipping) {
        this.requiresShipping = requiresShipping;
    }
    
    public boolean isTaxable() {
        return taxable;
    }
    
    public void setTaxable(boolean taxable) {
        this.taxable = taxable;
    }
    
    public boolean isOnSale() {
        return onSale;
    }
    
    public void setOnSale(boolean onSale) {
        this.onSale = onSale;
    }
    
    public boolean isInStock() {
        return inStock;
    }
    
    public void setInStock(boolean inStock) {
        this.inStock = inStock;
    }
    
    public Integer getAvailableQuantity() {
        return availableQuantity;
    }
    
    public void setAvailableQuantity(Integer availableQuantity) {
        this.availableQuantity = availableQuantity;
    }
    
    public List<String> getAttributes() {
        return attributes;
    }
    
    public void setAttributes(List<String> attributes) {
        this.attributes = attributes;
    }
    
    public List<String> getVariants() {
        return variants;
    }
    
    public void setVariants(List<String> variants) {
        this.variants = variants;
    }
    
    public List<String> getTags() {
        return tags;
    }
    
    public void setTags(List<String> tags) {
        this.tags = tags;
    }
    
    public String getMetaKeywords() {
        return metaKeywords;
    }
    
    public void setMetaKeywords(String metaKeywords) {
        this.metaKeywords = metaKeywords;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Double getWeight() {
        return weight;
    }
    
    public void setWeight(Double weight) {
        this.weight = weight;
    }
    
    public String getWeightUnit() {
        return weightUnit;
    }
    
    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }
    
    public List<String> getImageUrls() {
        return imageUrls;
    }
    
    public void setImageUrls(List<String> imageUrls) {
        this.imageUrls = imageUrls;
    }
    
    public String getPrimaryImageUrl() {
        return primaryImageUrl;
    }
    
    public void setPrimaryImageUrl(String primaryImageUrl) {
        this.primaryImageUrl = primaryImageUrl;
    }
}
