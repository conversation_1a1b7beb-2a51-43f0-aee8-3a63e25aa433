package com.nttdata.ndvn.customer.domain.model;

/**
 * Enumeration representing the status of a customer.
 */
public enum CustomerStatus {
    /**
     * Customer is active and can perform transactions.
     */
    ACTIVE,
    
    /**
     * Customer is inactive but can be reactivated.
     */
    INACTIVE,
    
    /**
     * Customer is suspended due to policy violations or other issues.
     */
    SUSPENDED,
    
    /**
     * Customer is archived and cannot be reactivated.
     */
    ARCHIVED
}
