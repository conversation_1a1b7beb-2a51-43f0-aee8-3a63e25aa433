package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Inventory entity representing stock levels and inventory management for products.
 * 
 * This entity manages inventory tracking, stock levels, and availability
 * for both products and their variants.
 */
@Entity
@Table(name = "inventory", indexes = {
    @Index(name = "idx_inventory_product", columnList = "productId"),
    @Index(name = "idx_inventory_variant", columnList = "variantId"),
    @Index(name = "idx_inventory_location", columnList = "locationCode"),
    @Index(name = "idx_inventory_available", columnList = "availableQuantity")
})
@Getter
@Setter
@NoArgsConstructor
public class Inventory {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotNull(message = "Product is required")
    @Column(name = "product_id", nullable = false)
    private UUID productId;
    
    @Column(name = "variant_id")
    private UUID variantId;
    
    @NotBlank(message = "Location code is required")
    @Size(max = 50, message = "Location code must not exceed 50 characters")
    @Column(name = "location_code", nullable = false, length = 50)
    private String locationCode;
    
    @NotBlank(message = "Location name is required")
    @Size(max = 255, message = "Location name must not exceed 255 characters")
    @Column(name = "location_name", nullable = false)
    private String locationName;
    
    @NotNull(message = "Total quantity is required")
    @Min(value = 0, message = "Total quantity must be non-negative")
    @Column(name = "total_quantity", nullable = false)
    private Integer totalQuantity = 0;
    
    @NotNull(message = "Available quantity is required")
    @Min(value = 0, message = "Available quantity must be non-negative")
    @Column(name = "available_quantity", nullable = false)
    private Integer availableQuantity = 0;
    
    @Min(value = 0, message = "Reserved quantity must be non-negative")
    @Column(name = "reserved_quantity")
    private Integer reservedQuantity = 0;
    
    @Min(value = 0, message = "Damaged quantity must be non-negative")
    @Column(name = "damaged_quantity")
    private Integer damagedQuantity = 0;
    
    @Min(value = 0, message = "Minimum stock level must be non-negative")
    @Column(name = "minimum_stock_level")
    private Integer minimumStockLevel = 0;
    
    @Min(value = 0, message = "Maximum stock level must be non-negative")
    @Column(name = "maximum_stock_level")
    private Integer maximumStockLevel = 1000;
    
    @Min(value = 0, message = "Reorder point must be non-negative")
    @Column(name = "reorder_point")
    private Integer reorderPoint = 10;
    
    @Min(value = 0, message = "Reorder quantity must be non-negative")
    @Column(name = "reorder_quantity")
    private Integer reorderQuantity = 50;
    
    @Column(name = "track_inventory")
    private boolean trackInventory = true;
    
    @Column(name = "allow_backorder")
    private boolean allowBackorder = false;
    
    @Column(name = "is_active")
    private boolean active = true;
    
    @Column(name = "last_stock_check")
    private LocalDateTime lastStockCheck;
    
    @Size(max = 1000, message = "Notes must not exceed 1000 characters")
    @Column(name = "notes", length = 1000)
    private String notes;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Business methods
    
    /**
     * Updates the stock quantities.
     */
    public void updateStock(Integer totalQuantity, Integer availableQuantity, Integer reservedQuantity, Integer damagedQuantity) {
        if (totalQuantity < 0 || availableQuantity < 0 || reservedQuantity < 0 || damagedQuantity < 0) {
            throw new IllegalArgumentException("Quantities must be non-negative");
        }
        
        if (availableQuantity + reservedQuantity + damagedQuantity > totalQuantity) {
            throw new IllegalArgumentException("Sum of available, reserved, and damaged quantities cannot exceed total quantity");
        }
        
        this.totalQuantity = totalQuantity;
        this.availableQuantity = availableQuantity;
        this.reservedQuantity = reservedQuantity;
        this.damagedQuantity = damagedQuantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Reserves inventory for an order.
     */
    public void reserveStock(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity to reserve must be positive");
        }
        
        if (quantity > availableQuantity) {
            throw new IllegalStateException("Cannot reserve more than available quantity");
        }
        
        this.availableQuantity -= quantity;
        this.reservedQuantity += quantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Releases reserved inventory.
     */
    public void releaseReservedStock(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity to release must be positive");
        }
        
        if (quantity > reservedQuantity) {
            throw new IllegalStateException("Cannot release more than reserved quantity");
        }
        
        this.reservedQuantity -= quantity;
        this.availableQuantity += quantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Confirms sale and removes stock from inventory.
     */
    public void confirmSale(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity to confirm must be positive");
        }
        
        if (quantity > reservedQuantity) {
            throw new IllegalStateException("Cannot confirm more than reserved quantity");
        }
        
        this.reservedQuantity -= quantity;
        this.totalQuantity -= quantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Adds stock to inventory.
     */
    public void addStock(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity to add must be positive");
        }
        
        this.totalQuantity += quantity;
        this.availableQuantity += quantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Removes damaged stock from inventory.
     */
    public void removeDamagedStock(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity to remove must be positive");
        }
        
        if (quantity > damagedQuantity) {
            throw new IllegalStateException("Cannot remove more than damaged quantity");
        }
        
        this.damagedQuantity -= quantity;
        this.totalQuantity -= quantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Marks stock as damaged.
     */
    public void markAsDamaged(Integer quantity) {
        if (quantity <= 0) {
            throw new IllegalArgumentException("Quantity to mark as damaged must be positive");
        }
        
        if (quantity > availableQuantity) {
            throw new IllegalStateException("Cannot mark more than available quantity as damaged");
        }
        
        this.availableQuantity -= quantity;
        this.damagedQuantity += quantity;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates inventory settings.
     */
    public void updateSettings(Integer minimumStockLevel, Integer maximumStockLevel, 
                              Integer reorderPoint, Integer reorderQuantity,
                              boolean trackInventory, boolean allowBackorder) {
        this.minimumStockLevel = minimumStockLevel;
        this.maximumStockLevel = maximumStockLevel;
        this.reorderPoint = reorderPoint;
        this.reorderQuantity = reorderQuantity;
        this.trackInventory = trackInventory;
        this.allowBackorder = allowBackorder;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the last stock check timestamp.
     */
    public void updateLastStockCheck() {
        this.lastStockCheck = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Checks if the inventory is in stock.
     */
    public boolean isInStock() {
        return availableQuantity > 0;
    }
    
    /**
     * Checks if the inventory is low stock.
     */
    public boolean isLowStock() {
        return availableQuantity <= reorderPoint;
    }
    
    /**
     * Checks if the inventory is out of stock.
     */
    public boolean isOutOfStock() {
        return availableQuantity == 0;
    }
    
    /**
     * Checks if the inventory needs reordering.
     */
    public boolean needsReorder() {
        return isLowStock() && trackInventory;
    }
    
    /**
     * Gets the total committed quantity (reserved + damaged).
     */
    public Integer getCommittedQuantity() {
        return reservedQuantity + damagedQuantity;
    }
    
    /**
     * Gets the stock utilization percentage.
     */
    public Double getStockUtilization() {
        if (maximumStockLevel == 0) {
            return 0.0;
        }
        return (double) totalQuantity / maximumStockLevel * 100;
    }
    
    // Builder pattern
    public static class Builder {
        private UUID productId;
        private UUID variantId;
        private String locationCode;
        private String locationName;
        private Integer totalQuantity = 0;
        private Integer availableQuantity = 0;
        private Integer minimumStockLevel = 0;
        private Integer reorderPoint = 10;
        private Integer reorderQuantity = 50;
        private boolean trackInventory = true;
        private boolean allowBackorder = false;
        
        public Builder productId(UUID productId) {
            this.productId = productId;
            return this;
        }
        
        public Builder variantId(UUID variantId) {
            this.variantId = variantId;
            return this;
        }
        
        public Builder locationCode(String locationCode) {
            this.locationCode = locationCode;
            return this;
        }
        
        public Builder locationName(String locationName) {
            this.locationName = locationName;
            return this;
        }
        
        public Builder totalQuantity(Integer totalQuantity) {
            this.totalQuantity = totalQuantity;
            this.availableQuantity = totalQuantity; // Default available = total
            return this;
        }
        
        public Builder availableQuantity(Integer availableQuantity) {
            this.availableQuantity = availableQuantity;
            return this;
        }
        
        public Builder minimumStockLevel(Integer minimumStockLevel) {
            this.minimumStockLevel = minimumStockLevel;
            return this;
        }
        
        public Builder reorderPoint(Integer reorderPoint) {
            this.reorderPoint = reorderPoint;
            return this;
        }
        
        public Builder reorderQuantity(Integer reorderQuantity) {
            this.reorderQuantity = reorderQuantity;
            return this;
        }
        
        public Builder trackInventory(boolean trackInventory) {
            this.trackInventory = trackInventory;
            return this;
        }
        
        public Builder allowBackorder(boolean allowBackorder) {
            this.allowBackorder = allowBackorder;
            return this;
        }
        
        public Inventory build() {
            Inventory inventory = new Inventory();
            inventory.productId = this.productId;
            inventory.variantId = this.variantId;
            inventory.locationCode = this.locationCode;
            inventory.locationName = this.locationName;
            inventory.totalQuantity = this.totalQuantity;
            inventory.availableQuantity = this.availableQuantity;
            inventory.minimumStockLevel = this.minimumStockLevel;
            inventory.reorderPoint = this.reorderPoint;
            inventory.reorderQuantity = this.reorderQuantity;
            inventory.trackInventory = this.trackInventory;
            inventory.allowBackorder = this.allowBackorder;
            return inventory;
        }
    }
}
