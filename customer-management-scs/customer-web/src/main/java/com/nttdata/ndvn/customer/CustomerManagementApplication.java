package com.nttdata.ndvn.customer;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;

/**
 * Main application class for Customer Management SCS.
 * 
 * This Self-Contained System handles all customer-related operations
 * including customer lifecycle management, segmentation, and address management.
 */
@SpringBootApplication
@EnableDiscoveryClient
public class CustomerManagementApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(CustomerManagementApplication.class, args);
    }
}
