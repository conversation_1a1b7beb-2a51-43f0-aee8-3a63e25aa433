package com.nttdata.ndvn.product.domain.service;

import com.nttdata.ndvn.product.domain.model.Product;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import com.nttdata.ndvn.product.domain.repository.ProductRepository;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * Domain service for Product-related business logic.
 * 
 * This service encapsulates complex business rules and operations
 * that don't naturally fit within a single aggregate.
 */
@Service
public class ProductDomainService {
    
    private final ProductRepository productRepository;
    
    public ProductDomainService(ProductRepository productRepository) {
        this.productRepository = productRepository;
    }
    
    /**
     * Generates a unique SKU for a product.
     */
    public String generateSku(String prefix) {
        String basePrefix = prefix != null ? prefix.toUpperCase() : "PROD";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = String.valueOf((int) (Math.random() * 1000));
        
        String sku;
        do {
            sku = basePrefix + "-" + timestamp + "-" + suffix;
            suffix = String.valueOf((int) (Math.random() * 1000));
        } while (productRepository.existsBySku(sku));
        
        return sku;
    }
    
    /**
     * Validates product data for creation.
     */
    public void validateProductForCreation(Product product) {
        if (product.getSku() == null || product.getSku().trim().isEmpty()) {
            throw new IllegalArgumentException("SKU is required");
        }
        
        if (productRepository.existsBySku(product.getSku())) {
            throw new IllegalArgumentException("SKU already exists: " + product.getSku());
        }
        
        if (product.getName() == null || product.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Product name is required");
        }
        
        if (product.getCategoryId() == null) {
            throw new IllegalArgumentException("Category is required");
        }
        
        if (product.getBasePrice() == null || product.getBasePrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Base price must be greater than 0");
        }
        
        validatePricing(product);
    }
    
    /**
     * Validates product data for update.
     */
    public void validateProductForUpdate(Product product) {
        if (product.getId() == null) {
            throw new IllegalArgumentException("Product ID is required for update");
        }
        
        if (!productRepository.existsById(product.getId())) {
            throw new IllegalArgumentException("Product not found: " + product.getId());
        }
        
        if (product.getName() == null || product.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Product name is required");
        }
        
        if (product.getCategoryId() == null) {
            throw new IllegalArgumentException("Category is required");
        }
        
        if (product.getBasePrice() == null || product.getBasePrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Base price must be greater than 0");
        }
        
        validatePricing(product);
    }
    
    /**
     * Validates product pricing.
     */
    public void validatePricing(Product product) {
        if (product.getBasePrice() == null) {
            throw new IllegalArgumentException("Base price is required");
        }
        
        if (product.getBasePrice().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Base price must be greater than 0");
        }
        
        if (product.getSalePrice() != null) {
            if (product.getSalePrice().compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("Sale price must be non-negative");
            }
            
            if (product.getSalePrice().compareTo(product.getBasePrice()) > 0) {
                throw new IllegalArgumentException("Sale price cannot be greater than base price");
            }
        }
    }
    
    /**
     * Validates product status transition.
     */
    public void validateStatusTransition(Product product, ProductStatus newStatus) {
        ProductStatus currentStatus = product.getStatus();
        
        if (currentStatus == newStatus) {
            return; // No change needed
        }
        
        switch (currentStatus) {
            case DRAFT:
                // Draft can transition to any status
                break;
            case ACTIVE:
                if (newStatus == ProductStatus.DRAFT) {
                    throw new IllegalStateException("Cannot change active product back to draft");
                }
                break;
            case INACTIVE:
                if (newStatus == ProductStatus.DRAFT) {
                    throw new IllegalStateException("Cannot change inactive product back to draft");
                }
                break;
            case DISCONTINUED:
                if (newStatus == ProductStatus.DRAFT || newStatus == ProductStatus.ACTIVE) {
                    throw new IllegalStateException("Cannot reactivate discontinued product");
                }
                break;
            case ARCHIVED:
                throw new IllegalStateException("Cannot change status of archived product");
        }
    }
    
    /**
     * Checks if a product can be deleted.
     */
    public boolean canDeleteProduct(Product product) {
        // Business rule: Can only delete draft products or archived products
        return product.getStatus() == ProductStatus.DRAFT || 
               product.getStatus() == ProductStatus.ARCHIVED;
    }
    
    /**
     * Checks if a product can be activated.
     */
    public boolean canActivateProduct(Product product) {
        // Business rules for activation
        if (product.getStatus() == ProductStatus.ARCHIVED) {
            return false;
        }
        
        if (product.getName() == null || product.getName().trim().isEmpty()) {
            return false;
        }
        
        if (product.getBasePrice() == null || product.getBasePrice().compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        if (product.getCategoryId() == null) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Calculates discount percentage for a product.
     */
    public BigDecimal calculateDiscountPercentage(Product product) {
        if (product.getSalePrice() == null || !product.isOnSale()) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal discount = product.getBasePrice().subtract(product.getSalePrice());
        return discount.divide(product.getBasePrice(), 4, BigDecimal.ROUND_HALF_UP)
                      .multiply(new BigDecimal("100"));
    }
    
    /**
     * Calculates savings amount for a product.
     */
    public BigDecimal calculateSavingsAmount(Product product) {
        if (product.getSalePrice() == null || !product.isOnSale()) {
            return BigDecimal.ZERO;
        }
        
        return product.getBasePrice().subtract(product.getSalePrice());
    }
    
    /**
     * Determines if a product is considered new (created within last 30 days).
     */
    public boolean isNewProduct(Product product) {
        if (product.getCreatedAt() == null) {
            return false;
        }
        
        return product.getCreatedAt().isAfter(
            java.time.LocalDateTime.now().minusDays(30)
        );
    }
    
    /**
     * Determines if a product is recently updated (updated within last 7 days).
     */
    public boolean isRecentlyUpdated(Product product) {
        if (product.getUpdatedAt() == null) {
            return false;
        }
        
        return product.getUpdatedAt().isAfter(
            java.time.LocalDateTime.now().minusDays(7)
        );
    }
    
    /**
     * Validates product dimensions.
     */
    public void validateDimensions(Product product) {
        if (product.getLength() != null && product.getLength().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Length must be non-negative");
        }
        
        if (product.getWidth() != null && product.getWidth().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Width must be non-negative");
        }
        
        if (product.getHeight() != null && product.getHeight().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Height must be non-negative");
        }
        
        if (product.getWeight() != null && product.getWeight().compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Weight must be non-negative");
        }
    }
    
    /**
     * Calculates product volume.
     */
    public BigDecimal calculateVolume(Product product) {
        if (product.getLength() == null || product.getWidth() == null || product.getHeight() == null) {
            return null;
        }
        
        return product.getLength()
                     .multiply(product.getWidth())
                     .multiply(product.getHeight());
    }
    
    /**
     * Determines shipping category based on product properties.
     */
    public String determineShippingCategory(Product product) {
        if (product.isDigital()) {
            return "DIGITAL";
        }
        
        if (!product.isRequiresShipping()) {
            return "NO_SHIPPING";
        }
        
        BigDecimal volume = calculateVolume(product);
        BigDecimal weight = product.getWeight();
        
        if (volume != null && volume.compareTo(new BigDecimal("1000")) > 0) {
            return "OVERSIZED";
        }
        
        if (weight != null && weight.compareTo(new BigDecimal("20")) > 0) {
            return "HEAVY";
        }
        
        return "STANDARD";
    }
    
    /**
     * Validates SKU format.
     */
    public boolean isValidSkuFormat(String sku) {
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        
        // SKU should contain only alphanumeric characters, hyphens, and underscores
        return sku.matches("^[A-Za-z0-9_-]+$") && sku.length() <= 100;
    }
    
    /**
     * Generates product slug from name.
     */
    public String generateSlugFromName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "";
        }
        
        return name.toLowerCase()
                  .replaceAll("[^a-z0-9\\s-]", "")
                  .replaceAll("\\s+", "-")
                  .replaceAll("-+", "-")
                  .replaceAll("^-|-$", "");
    }
}
