#!/bin/bash

# NDVN SCS Monitoring Setup Script
# This script sets up the complete monitoring and observability stack

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
MONITORING_DIR="$(dirname "$SCRIPT_DIR")"
INFRASTRUCTURE_DIR="$(dirname "$MONITORING_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    log_info "Checking Docker Compose availability..."
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    log_success "Docker Compose is available"
}

# Check if network exists
check_network() {
    log_info "Checking Docker network..."
    if ! docker network ls | grep -q "ndvn-scs-network"; then
        log_info "Creating Docker network: ndvn-scs-network"
        docker network create ndvn-scs-network
    fi
    log_success "Docker network is ready"
}

# Set up system requirements
setup_system_requirements() {
    log_info "Setting up system requirements..."
    
    # Increase vm.max_map_count for Elasticsearch
    if [ "$(sysctl -n vm.max_map_count)" -lt 262144 ]; then
        log_info "Increasing vm.max_map_count for Elasticsearch..."
        sudo sysctl -w vm.max_map_count=262144
        echo "vm.max_map_count=262144" | sudo tee -a /etc/sysctl.conf
    fi
    
    log_success "System requirements configured"
}

# Create required directories
create_directories() {
    log_info "Creating required directories..."
    
    mkdir -p "$MONITORING_DIR/elasticsearch/config"
    mkdir -p "$MONITORING_DIR/logstash/config"
    mkdir -p "$MONITORING_DIR/logstash/pipeline"
    mkdir -p "$MONITORING_DIR/kibana/config"
    mkdir -p "$MONITORING_DIR/grafana/config"
    mkdir -p "$MONITORING_DIR/grafana/dashboards/scs-platform"
    mkdir -p "$MONITORING_DIR/grafana/dashboards/infrastructure"
    mkdir -p "$MONITORING_DIR/grafana/dashboards/applications"
    mkdir -p "$MONITORING_DIR/grafana/dashboards/business"
    mkdir -p "$MONITORING_DIR/alertmanager/config"
    mkdir -p "$MONITORING_DIR/filebeat/config"
    
    log_success "Directories created"
}

# Generate configuration files
generate_configs() {
    log_info "Generating configuration files..."
    
    # Elasticsearch configuration
    cat > "$MONITORING_DIR/elasticsearch/config/elasticsearch.yml" << EOF
cluster.name: ndvn-cluster
node.name: elasticsearch
network.host: 0.0.0.0
discovery.type: single-node
xpack.security.enabled: false
xpack.monitoring.collection.enabled: true
EOF

    # Logstash configuration
    cat > "$MONITORING_DIR/logstash/config/logstash.yml" << EOF
http.host: "0.0.0.0"
xpack.monitoring.elasticsearch.hosts: ["http://elasticsearch:9200"]
EOF

    # Logstash pipeline
    cat > "$MONITORING_DIR/logstash/pipeline/logstash.conf" << EOF
input {
  beats {
    port => 5044
  }
  tcp {
    port => 5000
    codec => json_lines
  }
}

filter {
  if [fields][service] {
    mutate {
      add_field => { "service_name" => "%{[fields][service]}" }
    }
  }
  
  if [message] =~ /^\{.*\}$/ {
    json {
      source => "message"
    }
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "logstash-%{+YYYY.MM.dd}"
  }
  stdout { codec => rubydebug }
}
EOF

    # Kibana configuration
    cat > "$MONITORING_DIR/kibana/config/kibana.yml" << EOF
server.name: kibana
server.host: 0.0.0.0
elasticsearch.hosts: ["http://elasticsearch:9200"]
monitoring.ui.container.elasticsearch.enabled: true
EOF

    # Grafana configuration
    cat > "$MONITORING_DIR/grafana/config/grafana.ini" << EOF
[server]
http_port = 3000
domain = localhost

[security]
admin_user = admin
admin_password = admin123

[users]
allow_sign_up = false

[auth.anonymous]
enabled = false

[dashboards]
default_home_dashboard_path = /var/lib/grafana/dashboards/scs-platform/overview.json
EOF

    # AlertManager configuration
    cat > "$MONITORING_DIR/alertmanager/config/alertmanager.yml" << EOF
global:
  smtp_smarthost: 'localhost:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'web.hook'

receivers:
- name: 'web.hook'
  webhook_configs:
  - url: 'http://localhost:5001/'
    send_resolved: true

inhibit_rules:
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'dev', 'instance']
EOF

    # Filebeat configuration
    cat > "$MONITORING_DIR/filebeat/config/filebeat.yml" << EOF
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
    - add_docker_metadata:
        host: "unix:///var/run/docker.sock"

output.logstash:
  hosts: ["logstash:5044"]

logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
EOF

    log_success "Configuration files generated"
}

# Start Monitoring infrastructure
start_monitoring() {
    log_info "Starting Monitoring infrastructure..."
    
    cd "$MONITORING_DIR"
    
    # Start services
    docker-compose -f docker-compose-monitoring.yml up -d
    
    log_success "Monitoring infrastructure started"
}

# Wait for services to be ready
wait_for_services() {
    log_info "Waiting for services to be ready..."
    
    # Wait for Elasticsearch
    log_info "Waiting for Elasticsearch..."
    local max_attempts=30
    local attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:9200/_cluster/health > /dev/null 2>&1; then
            log_success "Elasticsearch is ready"
            break
        fi
        log_info "Attempt $attempt/$max_attempts: Elasticsearch not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done

    # Wait for Kibana
    log_info "Waiting for Kibana..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:5601/api/status > /dev/null 2>&1; then
            log_success "Kibana is ready"
            break
        fi
        log_info "Attempt $attempt/$max_attempts: Kibana not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done

    # Wait for Prometheus
    log_info "Waiting for Prometheus..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:9090/-/healthy > /dev/null 2>&1; then
            log_success "Prometheus is ready"
            break
        fi
        log_info "Attempt $attempt/$max_attempts: Prometheus not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done

    # Wait for Grafana
    log_info "Waiting for Grafana..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:3000/api/health > /dev/null 2>&1; then
            log_success "Grafana is ready"
            break
        fi
        log_info "Attempt $attempt/$max_attempts: Grafana not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done

    # Wait for Jaeger
    log_info "Waiting for Jaeger..."
    attempt=1
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:16686/ > /dev/null 2>&1; then
            log_success "Jaeger is ready"
            break
        fi
        log_info "Attempt $attempt/$max_attempts: Jaeger not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
}

# Configure Kibana index patterns
configure_kibana() {
    log_info "Configuring Kibana index patterns..."
    
    # Create index pattern for logstash
    curl -X POST "http://localhost:5601/api/saved_objects/index-pattern/logstash-*" \
        -H "Content-Type: application/json" \
        -H "kbn-xsrf: true" \
        -d '{
            "attributes": {
                "title": "logstash-*",
                "timeFieldName": "@timestamp"
            }
        }' > /dev/null 2>&1
    
    log_success "Kibana configured"
}

# Show status
show_status() {
    log_info "Monitoring Infrastructure Status:"
    echo ""
    docker-compose -f "$MONITORING_DIR/docker-compose-monitoring.yml" ps
    echo ""
    log_info "Access URLs:"
    echo "  - Grafana: http://localhost:3000 (admin/admin123)"
    echo "  - Prometheus: http://localhost:9090"
    echo "  - Kibana: http://localhost:5601"
    echo "  - Jaeger: http://localhost:16686"
    echo "  - AlertManager: http://localhost:9093"
    echo "  - Elasticsearch: http://localhost:9200"
    echo "  - Node Exporter: http://localhost:9100"
    echo "  - cAdvisor: http://localhost:8081"
    echo ""
}

# Stop Monitoring infrastructure
stop_monitoring() {
    log_info "Stopping Monitoring infrastructure..."
    cd "$MONITORING_DIR"
    docker-compose -f docker-compose-monitoring.yml down
    log_success "Monitoring infrastructure stopped"
}

# Clean up Monitoring infrastructure
cleanup_monitoring() {
    log_warning "This will remove all Monitoring data and containers. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up Monitoring infrastructure..."
        cd "$MONITORING_DIR"
        docker-compose -f docker-compose-monitoring.yml down -v --remove-orphans
        log_success "Monitoring infrastructure cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        start)
            check_docker
            check_docker_compose
            check_network
            setup_system_requirements
            create_directories
            generate_configs
            start_monitoring
            wait_for_services
            configure_kibana
            show_status
            ;;
        stop)
            stop_monitoring
            ;;
        restart)
            stop_monitoring
            sleep 5
            main start
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup_monitoring
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|status|cleanup}"
            echo ""
            echo "Commands:"
            echo "  start    - Start Monitoring infrastructure"
            echo "  stop     - Stop Monitoring infrastructure"
            echo "  restart  - Restart Monitoring infrastructure"
            echo "  status   - Show infrastructure status"
            echo "  cleanup  - Remove all Monitoring data and containers"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
