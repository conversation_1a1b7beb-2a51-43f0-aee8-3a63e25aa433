package com.nttdata.ndvn.application.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.nttdata.ndvn.infrastructure.config.InfrastructureConfig;

/**
 * Application layer configuration class.
 * This configuration class is responsible for setting up application-specific beans
 * such as application services, DTOs, and transaction management.
 */
@Configuration
@ComponentScan(basePackages = {
    "com.nttdata.ndvn.application.service"
})
@Import(InfrastructureConfig.class)
@EnableTransactionManagement
public class ApplicationConfig {
    
    // Application-specific bean configurations can be added here
    // For example: application services, mappers, etc.
}
