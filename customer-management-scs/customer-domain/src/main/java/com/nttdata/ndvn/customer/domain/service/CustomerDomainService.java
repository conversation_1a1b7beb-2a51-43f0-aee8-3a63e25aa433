package com.nttdata.ndvn.customer.domain.service;

import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerSegment;
import com.nttdata.ndvn.customer.domain.model.CustomerSegmentAssignment;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.repository.CustomerRepository;
import com.nttdata.ndvn.customer.domain.repository.CustomerSegmentRepository;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * Domain service for customer-related business logic.
 * 
 * This service encapsulates complex business rules and operations that don't
 * naturally belong to a single entity or value object.
 */
@Service
public class CustomerDomainService {
    
    private final CustomerRepository customerRepository;
    private final CustomerSegmentRepository segmentRepository;
    
    public CustomerDomainService(CustomerRepository customerRepository,
                               CustomerSegmentRepository segmentRepository) {
        this.customerRepository = customerRepository;
        this.segmentRepository = segmentRepository;
    }
    
    /**
     * Generate a unique customer number.
     */
    public String generateCustomerNumber() {
        String prefix = "CUST";
        String timestamp = String.valueOf(System.currentTimeMillis());
        String suffix = String.valueOf((int) (Math.random() * 1000));
        
        String customerNumber;
        do {
            customerNumber = prefix + timestamp + suffix;
            suffix = String.valueOf((int) (Math.random() * 1000));
        } while (customerRepository.existsByCustomerNumber(customerNumber));
        
        return customerNumber;
    }
    
    /**
     * Validate customer data for creation.
     */
    public void validateCustomerForCreation(Customer customer) {
        if (customer.getEmail() == null || customer.getEmail().trim().isEmpty()) {
            throw new IllegalArgumentException("Email is required");
        }
        
        if (customerRepository.existsByEmail(customer.getEmail())) {
            throw new IllegalArgumentException("Email already exists: " + customer.getEmail());
        }
        
        if (customer.getUserId() != null && customerRepository.existsByUserId(customer.getUserId())) {
            throw new IllegalArgumentException("User ID already linked to another customer: " + customer.getUserId());
        }
        
        validateBusinessCustomerData(customer);
    }
    
    /**
     * Validate business customer specific data.
     */
    private void validateBusinessCustomerData(Customer customer) {
        if (customer.isBusiness()) {
            if (customer.getCompanyName() == null || customer.getCompanyName().trim().isEmpty()) {
                throw new IllegalArgumentException("Company name is required for business customers");
            }
        }
    }
    
    /**
     * Determine customer classification based on business rules.
     */
    public String determineCustomerClassification(Customer customer) {
        if (customer.isBusiness()) {
            return determineBusinessClassification(customer);
        } else {
            return determineIndividualClassification(customer);
        }
    }
    
    private String determineBusinessClassification(Customer customer) {
        BigDecimal creditLimit = customer.getCreditLimit();
        
        if (creditLimit.compareTo(new BigDecimal("1000000")) >= 0) {
            return "ENTERPRISE";
        } else if (creditLimit.compareTo(new BigDecimal("100000")) >= 0) {
            return "CORPORATE";
        } else if (creditLimit.compareTo(new BigDecimal("10000")) >= 0) {
            return "SME";
        } else {
            return "SMALL_BUSINESS";
        }
    }
    
    private String determineIndividualClassification(Customer customer) {
        BigDecimal creditLimit = customer.getCreditLimit();
        
        if (creditLimit.compareTo(new BigDecimal("100000")) >= 0) {
            return "PREMIUM";
        } else if (creditLimit.compareTo(new BigDecimal("50000")) >= 0) {
            return "GOLD";
        } else if (creditLimit.compareTo(new BigDecimal("10000")) >= 0) {
            return "SILVER";
        } else {
            return "STANDARD";
        }
    }
    
    /**
     * Check if customer can be deactivated.
     */
    public boolean canDeactivateCustomer(Customer customer) {
        // Business rule: Cannot deactivate customers with active orders
        // This would typically check with other services
        return customer.getStatus() == CustomerStatus.ACTIVE;
    }
    
    /**
     * Assign customer to segment.
     */
    public void assignCustomerToSegment(Customer customer, String segmentName, String assignedBy) {
        CustomerSegment segment = segmentRepository.findByName(segmentName)
            .orElseThrow(() -> new IllegalArgumentException("Segment not found: " + segmentName));
        
        if (!segment.isActive()) {
            throw new IllegalArgumentException("Cannot assign to inactive segment: " + segmentName);
        }
        
        // Check if already assigned
        boolean alreadyAssigned = customer.getSegmentAssignments().stream()
            .anyMatch(assignment -> assignment.getSegment().getName().equals(segmentName));
        
        if (alreadyAssigned) {
            throw new IllegalArgumentException("Customer already assigned to segment: " + segmentName);
        }
        
        CustomerSegmentAssignment assignment = CustomerSegmentAssignment.builder()
            .customer(customer)
            .segment(segment)
            .assignedBy(assignedBy)
            .build();
        
        customer.getSegmentAssignments().add(assignment);
    }
    
    /**
     * Remove customer from segment.
     */
    public void removeCustomerFromSegment(Customer customer, String segmentName) {
        customer.getSegmentAssignments().removeIf(
            assignment -> assignment.getSegment().getName().equals(segmentName)
        );
    }
    
    /**
     * Check if customer qualifies for segment based on criteria.
     */
    public boolean customerQualifiesForSegment(Customer customer, CustomerSegment segment) {
        // This would implement complex business logic to evaluate segment criteria
        // For now, return true as a placeholder
        return segment.isActive();
    }
    
    /**
     * Calculate customer lifetime value (placeholder).
     */
    public BigDecimal calculateCustomerLifetimeValue(Customer customer) {
        // This would integrate with order and payment services
        // For now, return credit limit as a placeholder
        return customer.getCreditLimit();
    }
    
    /**
     * Validate email format.
     */
    public boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return false;
        }
        
        String emailRegex = "^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$";
        return email.matches(emailRegex);
    }
    
    /**
     * Validate phone number format.
     */
    public boolean isValidPhoneNumber(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return true; // Phone is optional
        }
        
        // Simple validation for Vietnamese phone numbers
        String phoneRegex = "^(\\+84|0)[0-9]{9,10}$";
        return phone.replaceAll("\\s+", "").matches(phoneRegex);
    }
    
    /**
     * Check if customer can be archived.
     */
    public boolean canArchiveCustomer(Customer customer) {
        return customer.getStatus() == CustomerStatus.INACTIVE &&
               customer.getCreatedAt().isBefore(LocalDateTime.now().minusYears(2));
    }
}
