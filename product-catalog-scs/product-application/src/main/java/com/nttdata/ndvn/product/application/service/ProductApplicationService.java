package com.nttdata.ndvn.product.application.service;

import com.nttdata.ndvn.product.application.dto.ProductDto;
import com.nttdata.ndvn.product.application.mapper.ProductMapper;
import com.nttdata.ndvn.product.domain.model.Product;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import com.nttdata.ndvn.product.domain.repository.ProductRepository;
import com.nttdata.ndvn.product.domain.service.ProductDomainService;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for Product management.
 * 
 * This service orchestrates product-related use cases and coordinates
 * between the domain layer and infrastructure layer.
 */
@Service
@Transactional
public class ProductApplicationService {
    
    private final ProductRepository productRepository;
    private final ProductDomainService productDomainService;
    private final ProductMapper productMapper;
    
    public ProductApplicationService(ProductRepository productRepository,
                                   ProductDomainService productDomainService,
                                   ProductMapper productMapper) {
        this.productRepository = productRepository;
        this.productDomainService = productDomainService;
        this.productMapper = productMapper;
    }
    
    /**
     * Creates a new product.
     */
    @CacheEvict(value = {"products", "product_search"}, allEntries = true)
    public ProductDto createProduct(ProductDto productDto) {
        // Generate SKU if not provided
        if (productDto.getSku() == null || productDto.getSku().trim().isEmpty()) {
            String sku = productDomainService.generateSku("PROD");
            productDto.setSku(sku);
        }
        
        Product product = productMapper.toNewEntity(productDto);
        
        // Validate product for creation
        productDomainService.validateProductForCreation(product);
        
        // Save product
        Product savedProduct = productRepository.save(product);
        
        return productMapper.toDto(savedProduct);
    }
    
    /**
     * Updates an existing product.
     */
    @CacheEvict(value = {"products", "product_search", "products_by_sku"}, allEntries = true)
    public ProductDto updateProduct(UUID productId, ProductDto productDto) {
        Product existingProduct = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        // Update product fields
        productMapper.updateEntityFromDto(productDto, existingProduct);
        
        // Validate product for update
        productDomainService.validateProductForUpdate(existingProduct);
        
        // Save updated product
        Product savedProduct = productRepository.save(existingProduct);
        
        return productMapper.toDto(savedProduct);
    }
    
    /**
     * Finds a product by ID.
     */
    @Cacheable(value = "products", key = "#productId")
    @Transactional(readOnly = true)
    public Optional<ProductDto> findProductById(UUID productId) {
        return productRepository.findById(productId)
                .map(productMapper::toDto);
    }
    
    /**
     * Finds a product by SKU.
     */
    @Cacheable(value = "products_by_sku", key = "#sku")
    @Transactional(readOnly = true)
    public Optional<ProductDto> findProductBySku(String sku) {
        return productRepository.findBySku(sku)
                .map(productMapper::toDto);
    }
    
    /**
     * Finds all products with pagination.
     */
    @Transactional(readOnly = true)
    public Page<ProductDto> findAllProducts(Pageable pageable) {
        return productRepository.findAll(pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Finds products by status.
     */
    @Transactional(readOnly = true)
    public Page<ProductDto> findProductsByStatus(ProductStatus status, Pageable pageable) {
        return productRepository.findByStatus(status, pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Finds products by category.
     */
    @Transactional(readOnly = true)
    public Page<ProductDto> findProductsByCategory(UUID categoryId, Pageable pageable) {
        return productRepository.findByCategoryId(categoryId, pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Finds products by brand.
     */
    @Transactional(readOnly = true)
    public Page<ProductDto> findProductsByBrand(UUID brandId, Pageable pageable) {
        return productRepository.findByBrandId(brandId, pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Finds featured products.
     */
    @Cacheable(value = "featured_products")
    @Transactional(readOnly = true)
    public Page<ProductDto> findFeaturedProducts(Pageable pageable) {
        return productRepository.findByFeatured(true, pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Finds products on sale.
     */
    @Transactional(readOnly = true)
    public Page<ProductDto> findProductsOnSale(Pageable pageable) {
        return productRepository.findProductsOnSale(pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Searches products by text.
     */
    @Cacheable(value = "product_search", key = "#searchTerm + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    @Transactional(readOnly = true)
    public Page<ProductDto> searchProducts(String searchTerm, Pageable pageable) {
        return productRepository.searchProducts(searchTerm, pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Advanced product search with multiple criteria.
     */
    @Transactional(readOnly = true)
    public Page<ProductDto> searchProductsByCriteria(String searchTerm, List<UUID> categoryIds, 
                                                    List<UUID> brandIds, List<ProductStatus> statuses,
                                                    BigDecimal minPrice, BigDecimal maxPrice,
                                                    Boolean featured, Boolean digital, Boolean onSale,
                                                    Pageable pageable) {
        return productRepository.findProductsByCriteria(searchTerm, categoryIds, brandIds, statuses,
                minPrice, maxPrice, featured, digital, onSale, pageable)
                .map(productMapper::toSimpleDto);
    }
    
    /**
     * Changes product status.
     */
    @CacheEvict(value = {"products", "product_search", "products_by_sku"}, allEntries = true)
    public ProductDto changeProductStatus(UUID productId, ProductStatus newStatus) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        // Validate status transition
        productDomainService.validateStatusTransition(product, newStatus);
        
        // Change status
        product.changeStatus(newStatus);
        
        // Save product
        Product savedProduct = productRepository.save(product);
        
        return productMapper.toDto(savedProduct);
    }
    
    /**
     * Activates a product.
     */
    @CacheEvict(value = {"products", "product_search", "products_by_sku"}, allEntries = true)
    public ProductDto activateProduct(UUID productId) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        // Check if product can be activated
        if (!productDomainService.canActivateProduct(product)) {
            throw new IllegalStateException("Product cannot be activated in its current state");
        }
        
        product.activate();
        Product savedProduct = productRepository.save(product);
        
        return productMapper.toDto(savedProduct);
    }
    
    /**
     * Deactivates a product.
     */
    @CacheEvict(value = {"products", "product_search", "products_by_sku"}, allEntries = true)
    public ProductDto deactivateProduct(UUID productId) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        product.deactivate();
        Product savedProduct = productRepository.save(product);
        
        return productMapper.toDto(savedProduct);
    }
    
    /**
     * Updates product pricing.
     */
    @CacheEvict(value = {"products", "product_search", "products_by_sku"}, allEntries = true)
    public ProductDto updateProductPricing(UUID productId, BigDecimal basePrice, BigDecimal salePrice) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        product.updatePricing(basePrice, salePrice);
        Product savedProduct = productRepository.save(product);
        
        return productMapper.toDto(savedProduct);
    }
    
    /**
     * Deletes a product.
     */
    @CacheEvict(value = {"products", "product_search", "products_by_sku"}, allEntries = true)
    public void deleteProduct(UUID productId) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        // Check if product can be deleted
        if (!productDomainService.canDeleteProduct(product)) {
            throw new IllegalStateException("Product cannot be deleted in its current state");
        }
        
        productRepository.delete(product);
    }
    
    /**
     * Gets product recommendations.
     */
    @Cacheable(value = "product_recommendations", key = "#productId + '_' + #limit")
    @Transactional(readOnly = true)
    public List<ProductDto> getProductRecommendations(UUID productId, int limit) {
        Product product = productRepository.findById(productId)
                .orElseThrow(() -> new IllegalArgumentException("Product not found: " + productId));
        
        // Get related products by category
        List<Product> relatedByCategory = productRepository.findRelatedProductsByCategory(
                product.getCategoryId(), productId, limit / 2);
        
        // Get related products by brand
        List<Product> relatedByBrand = productRepository.findRelatedProductsByBrand(
                product.getBrandId(), productId, limit / 2);
        
        // Combine and limit results
        relatedByCategory.addAll(relatedByBrand);
        
        return relatedByCategory.stream()
                .distinct()
                .limit(limit)
                .map(productMapper::toSimpleDto)
                .toList();
    }
    
    /**
     * Gets popular products.
     */
    @Cacheable(value = "popular_products", key = "#limit")
    @Transactional(readOnly = true)
    public List<ProductDto> getPopularProducts(int limit) {
        List<Product> products = productRepository.findBestSellingProducts(limit);
        return productMapper.toDtoList(products);
    }
    
    /**
     * Gets newest products.
     */
    @Transactional(readOnly = true)
    public List<ProductDto> getNewestProducts(int limit) {
        List<Product> products = productRepository.findNewestProducts(limit);
        return productMapper.toDtoList(products);
    }
    
    /**
     * Gets product statistics.
     */
    @Transactional(readOnly = true)
    public ProductStatistics getProductStatistics() {
        long totalProducts = productRepository.count();
        long activeProducts = productRepository.countByStatus(ProductStatus.ACTIVE);
        long featuredProducts = productRepository.countByFeatured(true);
        
        return new ProductStatistics(totalProducts, activeProducts, featuredProducts);
    }
    
    /**
     * Product statistics DTO.
     */
    public record ProductStatistics(long totalProducts, long activeProducts, long featuredProducts) {}
}
