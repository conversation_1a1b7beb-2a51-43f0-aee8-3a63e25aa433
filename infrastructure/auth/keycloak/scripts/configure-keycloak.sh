#!/bin/bash

# Keycloak Configuration Script for NDVN SCS Platform
# This script configures Keycloak with realms, clients, and users for the SCS platform

set -e

# Configuration variables
KEYCLOAK_URL="http://keycloak:8080"
ADMIN_USER="admin"
ADMIN_PASSWORD="admin-password"
REALM_NAME="ndvn"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Wait for Keycloak to be ready
wait_for_keycloak() {
    log_info "Waiting for Keycloak to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f "$KEYCLOAK_URL/health/ready" > /dev/null 2>&1; then
            log_success "Keycloak is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Keycloak not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Keycloak failed to start within expected time"
    return 1
}

# Get admin access token
get_admin_token() {
    log_info "Getting admin access token..."
    
    ADMIN_TOKEN=$(curl -s -X POST "$KEYCLOAK_URL/realms/master/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=$ADMIN_USER" \
        -d "password=$ADMIN_PASSWORD" \
        -d "grant_type=password" \
        -d "client_id=admin-cli" | jq -r '.access_token')
    
    if [ "$ADMIN_TOKEN" = "null" ] || [ -z "$ADMIN_TOKEN" ]; then
        log_error "Failed to get admin access token"
        exit 1
    fi
    
    log_success "Admin access token obtained"
}

# Create NDVN realm
create_realm() {
    log_info "Creating NDVN realm..."
    
    # Check if realm already exists
    REALM_EXISTS=$(curl -s -H "Authorization: Bearer $ADMIN_TOKEN" \
        "$KEYCLOAK_URL/admin/realms/$REALM_NAME" | jq -r '.realm // empty')
    
    if [ "$REALM_EXISTS" = "$REALM_NAME" ]; then
        log_warning "Realm $REALM_NAME already exists"
        return 0
    fi
    
    # Create realm
    curl -s -X POST "$KEYCLOAK_URL/admin/realms" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "realm": "'$REALM_NAME'",
            "displayName": "NDVN SCS Platform",
            "enabled": true,
            "registrationAllowed": true,
            "registrationEmailAsUsername": true,
            "rememberMe": true,
            "verifyEmail": false,
            "loginWithEmailAllowed": true,
            "duplicateEmailsAllowed": false,
            "resetPasswordAllowed": true,
            "editUsernameAllowed": false,
            "bruteForceProtected": true,
            "permanentLockout": false,
            "maxFailureWaitSeconds": 900,
            "minimumQuickLoginWaitSeconds": 60,
            "waitIncrementSeconds": 60,
            "quickLoginCheckMilliSeconds": 1000,
            "maxDeltaTimeSeconds": 43200,
            "failureFactor": 30,
            "defaultRoles": ["default-roles-'$REALM_NAME'"],
            "requiredCredentials": ["password"],
            "passwordPolicy": "length(8) and digits(1) and lowerCase(1) and upperCase(1) and specialChars(1) and notUsername",
            "otpPolicyType": "totp",
            "otpPolicyAlgorithm": "HmacSHA1",
            "otpPolicyInitialCounter": 0,
            "otpPolicyDigits": 6,
            "otpPolicyLookAheadWindow": 1,
            "otpPolicyPeriod": 30,
            "sslRequired": "external",
            "accessTokenLifespan": 300,
            "accessTokenLifespanForImplicitFlow": 900,
            "ssoSessionIdleTimeout": 1800,
            "ssoSessionMaxLifespan": 36000,
            "offlineSessionIdleTimeout": 2592000,
            "accessCodeLifespan": 60,
            "accessCodeLifespanUserAction": 300,
            "accessCodeLifespanLogin": 1800,
            "actionTokenGeneratedByAdminLifespan": 43200,
            "actionTokenGeneratedByUserLifespan": 300,
            "oauth2DeviceCodeLifespan": 600,
            "oauth2DevicePollingInterval": 5,
            "internationalizationEnabled": true,
            "supportedLocales": ["en", "vi"],
            "defaultLocale": "en"
        }'
    
    log_success "Realm $REALM_NAME created"
}

# Create API Gateway client
create_api_gateway_client() {
    log_info "Creating API Gateway client..."
    
    curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "clientId": "api-gateway",
            "name": "API Gateway",
            "description": "API Gateway for NDVN SCS Platform",
            "enabled": true,
            "clientAuthenticatorType": "client-secret",
            "secret": "api-gateway-secret",
            "redirectUris": ["http://localhost:8080/*", "https://localhost/*"],
            "webOrigins": ["http://localhost:8080", "https://localhost"],
            "protocol": "openid-connect",
            "publicClient": false,
            "bearerOnly": false,
            "standardFlowEnabled": true,
            "implicitFlowEnabled": false,
            "directAccessGrantsEnabled": true,
            "serviceAccountsEnabled": true,
            "authorizationServicesEnabled": true,
            "fullScopeAllowed": true,
            "nodeReRegistrationTimeout": -1,
            "defaultClientScopes": ["web-origins", "role_list", "profile", "roles", "email"],
            "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]
        }'
    
    log_success "API Gateway client created"
}

# Create SCS service clients
create_service_clients() {
    log_info "Creating SCS service clients..."
    
    # Array of services
    services=("user-management" "customer-management" "product-catalog" "order-management" "notification")
    
    for service in "${services[@]}"; do
        log_info "Creating client for $service-service..."
        
        curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "clientId": "'$service'-service",
                "name": "'$service' Service",
                "description": "'$service' service for NDVN SCS Platform",
                "enabled": true,
                "clientAuthenticatorType": "client-secret",
                "secret": "'$service'-service-secret",
                "protocol": "openid-connect",
                "publicClient": false,
                "bearerOnly": true,
                "standardFlowEnabled": false,
                "implicitFlowEnabled": false,
                "directAccessGrantsEnabled": false,
                "serviceAccountsEnabled": true,
                "authorizationServicesEnabled": false,
                "fullScopeAllowed": false,
                "nodeReRegistrationTimeout": -1,
                "defaultClientScopes": ["role_list", "profile", "roles", "email"],
                "optionalClientScopes": ["address", "phone", "offline_access"]
            }'
    done
    
    log_success "SCS service clients created"
}

# Create token service client
create_token_service_client() {
    log_info "Creating Token Service client..."
    
    curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/clients" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "clientId": "token-service",
            "name": "Token Service",
            "description": "Token introspection service for NDVN SCS Platform",
            "enabled": true,
            "clientAuthenticatorType": "client-secret",
            "secret": "token-service-secret",
            "protocol": "openid-connect",
            "publicClient": false,
            "bearerOnly": false,
            "standardFlowEnabled": false,
            "implicitFlowEnabled": false,
            "directAccessGrantsEnabled": true,
            "serviceAccountsEnabled": true,
            "authorizationServicesEnabled": false,
            "fullScopeAllowed": true,
            "nodeReRegistrationTimeout": -1
        }'
    
    log_success "Token Service client created"
}

# Create realm roles
create_realm_roles() {
    log_info "Creating realm roles..."
    
    # Array of roles
    roles=("admin" "user" "customer" "manager" "support" "api-access")
    
    for role in "${roles[@]}"; do
        log_info "Creating role: $role"
        
        curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/roles" \
            -H "Authorization: Bearer $ADMIN_TOKEN" \
            -H "Content-Type: application/json" \
            -d '{
                "name": "'$role'",
                "description": "'$role' role for NDVN SCS Platform",
                "composite": false,
                "clientRole": false
            }'
    done
    
    log_success "Realm roles created"
}

# Create test users
create_test_users() {
    log_info "Creating test users..."
    
    # Admin user
    curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "admin",
            "email": "<EMAIL>",
            "firstName": "Admin",
            "lastName": "User",
            "enabled": true,
            "emailVerified": true,
            "credentials": [{
                "type": "password",
                "value": "admin123",
                "temporary": false
            }],
            "realmRoles": ["admin", "user", "api-access"],
            "attributes": {
                "department": ["IT"],
                "location": ["Ho Chi Minh City"]
            }
        }'
    
    # Regular user
    curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "user",
            "email": "<EMAIL>",
            "firstName": "Regular",
            "lastName": "User",
            "enabled": true,
            "emailVerified": true,
            "credentials": [{
                "type": "password",
                "value": "user123",
                "temporary": false
            }],
            "realmRoles": ["user", "customer", "api-access"],
            "attributes": {
                "department": ["Business"],
                "location": ["Ho Chi Minh City"]
            }
        }'
    
    # Manager user
    curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/users" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "username": "manager",
            "email": "<EMAIL>",
            "firstName": "Manager",
            "lastName": "User",
            "enabled": true,
            "emailVerified": true,
            "credentials": [{
                "type": "password",
                "value": "manager123",
                "temporary": false
            }],
            "realmRoles": ["manager", "user", "api-access"],
            "attributes": {
                "department": ["Management"],
                "location": ["Ho Chi Minh City"]
            }
        }'
    
    log_success "Test users created"
}

# Configure client scopes
configure_client_scopes() {
    log_info "Configuring client scopes..."
    
    # Create custom scope for SCS services
    curl -s -X POST "$KEYCLOAK_URL/admin/realms/$REALM_NAME/client-scopes" \
        -H "Authorization: Bearer $ADMIN_TOKEN" \
        -H "Content-Type: application/json" \
        -d '{
            "name": "scs-services",
            "description": "Scope for SCS service access",
            "protocol": "openid-connect",
            "attributes": {
                "include.in.token.scope": "true",
                "display.on.consent.screen": "true",
                "consent.screen.text": "Access to SCS services"
            }
        }'
    
    log_success "Client scopes configured"
}

# Main configuration function
main() {
    log_info "Starting Keycloak configuration..."
    
    wait_for_keycloak
    get_admin_token
    create_realm
    create_api_gateway_client
    create_service_clients
    create_token_service_client
    create_realm_roles
    create_test_users
    configure_client_scopes
    
    log_success "Keycloak configuration completed successfully!"
    
    log_info "Configuration Summary:"
    echo "  - Realm: $REALM_NAME"
    echo "  - Admin Console: $KEYCLOAK_URL/admin"
    echo "  - Realm URL: $KEYCLOAK_URL/realms/$REALM_NAME"
    echo "  - Test Users:"
    echo "    - admin/admin123 (admin role)"
    echo "    - user/user123 (user role)"
    echo "    - manager/manager123 (manager role)"
}

# Run main function
main "$@"
