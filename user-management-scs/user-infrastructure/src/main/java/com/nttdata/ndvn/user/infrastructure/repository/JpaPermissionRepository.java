package com.nttdata.ndvn.user.infrastructure.repository;

import com.nttdata.ndvn.user.domain.model.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * JPA repository for Permission entity.
 * 
 * This repository provides data access operations for Permission entities using Spring Data JPA.
 */
@Repository
public interface JpaPermissionRepository extends JpaRepository<Permission, UUID> {
    
    /**
     * Finds a permission by its name.
     */
    Optional<Permission> findByName(String name);
    
    /**
     * Finds permissions by resource.
     */
    Page<Permission> findByResource(String resource, Pageable pageable);
    
    /**
     * Finds permissions by action.
     */
    Page<Permission> findByAction(String action, Pageable pageable);
    
    /**
     * Finds permissions by resource and action.
     */
    Optional<Permission> findByResourceAndAction(String resource, String action);
    
    /**
     * Finds permissions by active status.
     */
    Page<Permission> findByActive(boolean active, Pageable pageable);
    
    /**
     * Finds permissions by system permission status.
     */
    Page<Permission> findBySystemPermission(boolean systemPermission, Pageable pageable);
    
    /**
     * Finds permissions by name containing text (case-insensitive).
     */
    Page<Permission> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Finds permissions by description containing text (case-insensitive).
     */
    Page<Permission> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Finds permissions by names.
     */
    List<Permission> findByNameIn(Set<String> names);
    
    /**
     * Checks if a permission exists with the specified name.
     */
    boolean existsByName(String name);
    
    /**
     * Checks if a permission exists with the specified resource and action.
     */
    boolean existsByResourceAndAction(String resource, String action);
    
    /**
     * Counts permissions by active status.
     */
    long countByActive(boolean active);
    
    /**
     * Counts permissions by system permission status.
     */
    long countBySystemPermission(boolean systemPermission);
    
    /**
     * Counts permissions by resource.
     */
    long countByResource(String resource);
    
    // Additional JPA-specific queries
    
    /**
     * Finds permissions assigned to a specific role.
     */
    @Query("SELECT p FROM Permission p JOIN p.roles r WHERE r.id = :roleId")
    List<Permission> findPermissionsByRoleId(@Param("roleId") UUID roleId);
    
    /**
     * Finds permissions not assigned to any role.
     */
    @Query("SELECT p FROM Permission p WHERE p.roles IS EMPTY")
    Page<Permission> findUnassignedPermissions(Pageable pageable);
    
    /**
     * Finds permissions assigned to multiple roles.
     */
    @Query("SELECT p FROM Permission p WHERE SIZE(p.roles) > 1")
    Page<Permission> findPermissionsWithMultipleRoles(Pageable pageable);
    
    /**
     * Finds permissions by resource pattern.
     */
    @Query("SELECT p FROM Permission p WHERE p.resource LIKE :resourcePattern")
    List<Permission> findByResourcePattern(@Param("resourcePattern") String resourcePattern);
    
    /**
     * Finds permissions by action pattern.
     */
    @Query("SELECT p FROM Permission p WHERE p.action LIKE :actionPattern")
    List<Permission> findByActionPattern(@Param("actionPattern") String actionPattern);
    
    /**
     * Searches permissions by multiple criteria.
     */
    @Query("SELECT p FROM Permission p WHERE " +
           "(:name IS NULL OR LOWER(p.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND " +
           "(:resource IS NULL OR LOWER(p.resource) LIKE LOWER(CONCAT('%', :resource, '%'))) AND " +
           "(:action IS NULL OR LOWER(p.action) LIKE LOWER(CONCAT('%', :action, '%'))) AND " +
           "(:active IS NULL OR p.active = :active) AND " +
           "(:systemPermission IS NULL OR p.systemPermission = :systemPermission)")
    Page<Permission> searchPermissions(@Param("name") String name,
                                     @Param("resource") String resource,
                                     @Param("action") String action,
                                     @Param("active") Boolean active,
                                     @Param("systemPermission") Boolean systemPermission,
                                     Pageable pageable);
    
    /**
     * Gets permission statistics.
     */
    @Query("SELECT " +
           "COUNT(p) as totalPermissions, " +
           "COUNT(CASE WHEN p.active = true THEN 1 END) as activePermissions, " +
           "COUNT(CASE WHEN p.systemPermission = true THEN 1 END) as systemPermissions, " +
           "COUNT(DISTINCT p.resource) as uniqueResources, " +
           "COUNT(DISTINCT p.action) as uniqueActions " +
           "FROM Permission p")
    Object[] getPermissionStatistics();
    
    /**
     * Finds permissions that can be safely deleted.
     */
    @Query("SELECT p FROM Permission p WHERE p.systemPermission = false AND SIZE(p.roles) = 0")
    List<Permission> findSafelyDeletablePermissions();
    
    /**
     * Gets all unique resources.
     */
    @Query("SELECT DISTINCT p.resource FROM Permission p ORDER BY p.resource")
    List<String> findAllUniqueResources();
    
    /**
     * Gets all unique actions.
     */
    @Query("SELECT DISTINCT p.action FROM Permission p ORDER BY p.action")
    List<String> findAllUniqueActions();
    
    /**
     * Finds permissions for a specific user through their roles.
     */
    @Query("SELECT DISTINCT p FROM Permission p JOIN p.roles r JOIN r.users u WHERE u.id = :userId AND p.active = true AND r.active = true")
    List<Permission> findPermissionsByUserId(@Param("userId") UUID userId);
    
    /**
     * Bulk updates permission active status.
     */
    @Query("UPDATE Permission p SET p.active = :active, p.updatedAt = CURRENT_TIMESTAMP WHERE p.id IN :permissionIds AND p.systemPermission = false")
    int bulkUpdateActiveStatus(@Param("permissionIds") List<UUID> permissionIds, @Param("active") boolean active);
}
