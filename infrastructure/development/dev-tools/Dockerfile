# Development Tools Container for NDVN SCS Platform
FROM ubuntu:22.04

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    vim \
    nano \
    htop \
    tree \
    jq \
    unzip \
    zip \
    build-essential \
    ca-certificates \
    gnupg \
    lsb-release \
    software-properties-common \
    apt-transport-https \
    && rm -rf /var/lib/apt/lists/*

# Install Java 21
RUN wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | apt-key add - && \
    echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list && \
    apt-get update && \
    apt-get install -y temurin-21-jdk && \
    rm -rf /var/lib/apt/lists/*

# Install Gradle
ENV GRADLE_VERSION=8.5
RUN wget https://services.gradle.org/distributions/gradle-${GRADLE_VERSION}-bin.zip -P /tmp && \
    unzip -d /opt/gradle /tmp/gradle-${GRADLE_VERSION}-bin.zip && \
    ln -s /opt/gradle/gradle-${GRADLE_VERSION}/bin/gradle /usr/local/bin/gradle && \
    rm /tmp/gradle-${GRADLE_VERSION}-bin.zip

# Install Maven
ENV MAVEN_VERSION=3.9.6
RUN wget https://archive.apache.org/dist/maven/maven-3/${MAVEN_VERSION}/binaries/apache-maven-${MAVEN_VERSION}-bin.tar.gz -P /tmp && \
    tar xf /tmp/apache-maven-${MAVEN_VERSION}-bin.tar.gz -C /opt && \
    ln -s /opt/apache-maven-${MAVEN_VERSION}/bin/mvn /usr/local/bin/mvn && \
    rm /tmp/apache-maven-${MAVEN_VERSION}-bin.tar.gz

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs

# Install Docker CLI
RUN curl -fsSL https://download.docker.com/linux/ubuntu/gpg | gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce-cli && \
    rm -rf /var/lib/apt/lists/*

# Install Docker Compose
RUN curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose && \
    chmod +x /usr/local/bin/docker-compose

# Install kubectl
RUN curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" && \
    install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl && \
    rm kubectl

# Install Helm
RUN curl https://baltocdn.com/helm/signing.asc | gpg --dearmor | tee /usr/share/keyrings/helm.gpg > /dev/null && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/helm.gpg] https://baltocdn.com/helm/stable/debian/ all main" | tee /etc/apt/sources.list.d/helm-stable-debian.list && \
    apt-get update && \
    apt-get install -y helm && \
    rm -rf /var/lib/apt/lists/*

# Install Kafka CLI tools
RUN wget https://archive.apache.org/dist/kafka/2.13-3.6.0/kafka_2.13-3.6.0.tgz -P /tmp && \
    tar xf /tmp/kafka_2.13-3.6.0.tgz -C /opt && \
    ln -s /opt/kafka_2.13-3.6.0/bin/kafka-topics.sh /usr/local/bin/kafka-topics && \
    ln -s /opt/kafka_2.13-3.6.0/bin/kafka-console-producer.sh /usr/local/bin/kafka-console-producer && \
    ln -s /opt/kafka_2.13-3.6.0/bin/kafka-console-consumer.sh /usr/local/bin/kafka-console-consumer && \
    rm /tmp/kafka_2.13-3.6.0.tgz

# Install PostgreSQL client
RUN apt-get update && \
    apt-get install -y postgresql-client && \
    rm -rf /var/lib/apt/lists/*

# Install Redis CLI
RUN apt-get update && \
    apt-get install -y redis-tools && \
    rm -rf /var/lib/apt/lists/*

# Install useful development tools
RUN npm install -g \
    @angular/cli \
    @vue/cli \
    create-react-app \
    typescript \
    ts-node \
    nodemon \
    pm2

# Install Python and pip
RUN apt-get update && \
    apt-get install -y python3 python3-pip && \
    rm -rf /var/lib/apt/lists/*

# Install useful Python packages
RUN pip3 install \
    requests \
    httpie \
    awscli \
    ansible \
    terraform

# Create developer user
RUN useradd -m -s /bin/bash developer && \
    usermod -aG sudo developer && \
    echo "developer ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

# Set up environment variables
ENV JAVA_HOME=/usr/lib/jvm/temurin-21-jdk-amd64
ENV GRADLE_HOME=/opt/gradle/gradle-${GRADLE_VERSION}
ENV MAVEN_HOME=/opt/apache-maven-${MAVEN_VERSION}
ENV PATH=$PATH:$JAVA_HOME/bin:$GRADLE_HOME/bin:$MAVEN_HOME/bin

# Create workspace directory
RUN mkdir -p /workspace && \
    chown developer:developer /workspace

# Switch to developer user
USER developer
WORKDIR /home/<USER>

# Set up developer environment
RUN echo 'export PS1="\[\033[01;32m\]\u@\h\[\033[00m\]:\[\033[01;34m\]\w\[\033[00m\]\$ "' >> ~/.bashrc && \
    echo 'alias ll="ls -alF"' >> ~/.bashrc && \
    echo 'alias la="ls -A"' >> ~/.bashrc && \
    echo 'alias l="ls -CF"' >> ~/.bashrc && \
    echo 'alias grep="grep --color=auto"' >> ~/.bashrc && \
    echo 'alias fgrep="fgrep --color=auto"' >> ~/.bashrc && \
    echo 'alias egrep="egrep --color=auto"' >> ~/.bashrc

# Create useful scripts
RUN mkdir -p ~/bin && \
    echo '#!/bin/bash' > ~/bin/kafka-list-topics && \
    echo 'kafka-topics --bootstrap-server kafka:9092 --list' >> ~/bin/kafka-list-topics && \
    chmod +x ~/bin/kafka-list-topics

RUN echo '#!/bin/bash' > ~/bin/kafka-create-topic && \
    echo 'kafka-topics --bootstrap-server kafka:9092 --create --topic $1 --partitions ${2:-3} --replication-factor ${3:-1}' >> ~/bin/kafka-create-topic && \
    chmod +x ~/bin/kafka-create-topic

RUN echo '#!/bin/bash' > ~/bin/db-connect && \
    echo 'case $1 in' >> ~/bin/db-connect && \
    echo '  user) psql -h user-db -U user_service -d user_management ;;' >> ~/bin/db-connect && \
    echo '  customer) psql -h customer-db -U customer_service -d customer_management ;;' >> ~/bin/db-connect && \
    echo '  catalog) psql -h catalog-db -U catalog_service -d product_catalog ;;' >> ~/bin/db-connect && \
    echo '  order) psql -h order-db -U order_service -d order_management ;;' >> ~/bin/db-connect && \
    echo '  notification) psql -h notification-db -U notification_service -d notification_service ;;' >> ~/bin/db-connect && \
    echo '  *) echo "Usage: db-connect {user|customer|catalog|order|notification}" ;;' >> ~/bin/db-connect && \
    echo 'esac' >> ~/bin/db-connect && \
    chmod +x ~/bin/db-connect

# Add bin directory to PATH
RUN echo 'export PATH=$PATH:~/bin' >> ~/.bashrc

# Set working directory to workspace
WORKDIR /workspace

# Default command
CMD ["/bin/bash"]
