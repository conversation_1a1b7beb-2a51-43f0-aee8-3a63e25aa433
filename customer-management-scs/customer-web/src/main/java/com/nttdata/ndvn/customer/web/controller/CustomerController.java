package com.nttdata.ndvn.customer.web.controller;

import com.nttdata.ndvn.customer.application.dto.CustomerDto;
import com.nttdata.ndvn.customer.application.service.CustomerApplicationService;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * REST controller for customer management operations.
 */
@RestController
@RequestMapping("/api/v1/customers")
@Tag(name = "Customer Management", description = "Customer lifecycle and profile management operations")
@SecurityRequirement(name = "oauth2")
public class CustomerController {
    
    private final CustomerApplicationService customerService;
    
    public CustomerController(CustomerApplicationService customerService) {
        this.customerService = customerService;
    }
    
    @Operation(summary = "Create a new customer", description = "Creates a new customer with the provided information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Customer created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid customer data"),
        @ApiResponse(responseCode = "409", description = "Customer already exists")
    })
    @PostMapping
    @PreAuthorize("hasAuthority('SCOPE_customer:write')")
    public ResponseEntity<CustomerDto> createCustomer(@Valid @RequestBody CustomerDto customerDto) {
        CustomerDto createdCustomer = customerService.createCustomer(customerDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdCustomer);
    }
    
    @Operation(summary = "Get customer by ID", description = "Retrieves a customer by their unique identifier")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer found"),
        @ApiResponse(responseCode = "404", description = "Customer not found")
    })
    @GetMapping("/{customerId}")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<CustomerDto> getCustomerById(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId) {
        return customerService.getCustomerById(customerId)
            .map(customer -> ResponseEntity.ok(customer))
            .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get customer by customer number", description = "Retrieves a customer by their customer number")
    @GetMapping("/number/{customerNumber}")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<CustomerDto> getCustomerByNumber(
            @Parameter(description = "Customer number") @PathVariable String customerNumber) {
        return customerService.getCustomerByNumber(customerNumber)
            .map(customer -> ResponseEntity.ok(customer))
            .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get customer by email", description = "Retrieves a customer by their email address")
    @GetMapping("/email/{email}")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<CustomerDto> getCustomerByEmail(
            @Parameter(description = "Email address") @PathVariable String email) {
        return customerService.getCustomerByEmail(email)
            .map(customer -> ResponseEntity.ok(customer))
            .orElse(ResponseEntity.notFound().build());
    }
    
    @Operation(summary = "Get all customers", description = "Retrieves all customers with pagination")
    @GetMapping
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<Page<CustomerDto>> getAllCustomers(
            @PageableDefault(size = 20) Pageable pageable) {
        Page<CustomerDto> customers = customerService.getAllCustomers(pageable);
        return ResponseEntity.ok(customers);
    }
    
    @Operation(summary = "Search customers", description = "Search customers by various criteria")
    @GetMapping("/search")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<Page<CustomerDto>> searchCustomers(
            @Parameter(description = "Search term") @RequestParam String q,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<CustomerDto> customers = customerService.searchCustomers(q, pageable);
        return ResponseEntity.ok(customers);
    }
    
    @Operation(summary = "Get customers by status", description = "Retrieves customers filtered by status")
    @GetMapping("/status/{status}")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<Page<CustomerDto>> getCustomersByStatus(
            @Parameter(description = "Customer status") @PathVariable CustomerStatus status,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<CustomerDto> customers = customerService.getCustomersByStatus(status, pageable);
        return ResponseEntity.ok(customers);
    }
    
    @Operation(summary = "Get customers by type", description = "Retrieves customers filtered by type")
    @GetMapping("/type/{type}")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<Page<CustomerDto>> getCustomersByType(
            @Parameter(description = "Customer type") @PathVariable CustomerType type,
            @PageableDefault(size = 20) Pageable pageable) {
        Page<CustomerDto> customers = customerService.getCustomersByType(type, pageable);
        return ResponseEntity.ok(customers);
    }
    
    @Operation(summary = "Update customer", description = "Updates an existing customer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Customer updated successfully"),
        @ApiResponse(responseCode = "404", description = "Customer not found"),
        @ApiResponse(responseCode = "400", description = "Invalid customer data")
    })
    @PutMapping("/{customerId}")
    @PreAuthorize("hasAuthority('SCOPE_customer:write')")
    public ResponseEntity<CustomerDto> updateCustomer(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId,
            @Valid @RequestBody CustomerDto customerDto) {
        CustomerDto updatedCustomer = customerService.updateCustomer(customerId, customerDto);
        return ResponseEntity.ok(updatedCustomer);
    }
    
    @Operation(summary = "Update customer status", description = "Updates the status of a customer")
    @PatchMapping("/{customerId}/status")
    @PreAuthorize("hasAuthority('SCOPE_customer:write')")
    public ResponseEntity<CustomerDto> updateCustomerStatus(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId,
            @Parameter(description = "New status") @RequestParam CustomerStatus status) {
        CustomerDto updatedCustomer = customerService.updateCustomerStatus(customerId, status);
        return ResponseEntity.ok(updatedCustomer);
    }
    
    @Operation(summary = "Update credit limit", description = "Updates the credit limit of a customer")
    @PatchMapping("/{customerId}/credit-limit")
    @PreAuthorize("hasAuthority('SCOPE_customer:write')")
    public ResponseEntity<CustomerDto> updateCreditLimit(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId,
            @Parameter(description = "Credit limit") @RequestParam BigDecimal creditLimit) {
        CustomerDto updatedCustomer = customerService.updateCreditLimit(customerId, creditLimit);
        return ResponseEntity.ok(updatedCustomer);
    }
    
    @Operation(summary = "Assign customer to segment", description = "Assigns a customer to a specific segment")
    @PostMapping("/{customerId}/segments/{segmentName}")
    @PreAuthorize("hasAuthority('SCOPE_customer:write')")
    public ResponseEntity<CustomerDto> assignToSegment(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId,
            @Parameter(description = "Segment name") @PathVariable String segmentName,
            @Parameter(description = "Assigned by") @RequestParam String assignedBy) {
        CustomerDto updatedCustomer = customerService.assignToSegment(customerId, segmentName, assignedBy);
        return ResponseEntity.ok(updatedCustomer);
    }
    
    @Operation(summary = "Remove customer from segment", description = "Removes a customer from a specific segment")
    @DeleteMapping("/{customerId}/segments/{segmentName}")
    @PreAuthorize("hasAuthority('SCOPE_customer:write')")
    public ResponseEntity<CustomerDto> removeFromSegment(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId,
            @Parameter(description = "Segment name") @PathVariable String segmentName) {
        CustomerDto updatedCustomer = customerService.removeFromSegment(customerId, segmentName);
        return ResponseEntity.ok(updatedCustomer);
    }
    
    @Operation(summary = "Delete customer", description = "Deletes a customer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Customer deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Customer not found")
    })
    @DeleteMapping("/{customerId}")
    @PreAuthorize("hasAuthority('SCOPE_customer:delete')")
    public ResponseEntity<Void> deleteCustomer(
            @Parameter(description = "Customer ID") @PathVariable UUID customerId) {
        customerService.deleteCustomer(customerId);
        return ResponseEntity.noContent().build();
    }
    
    @Operation(summary = "Check email availability", description = "Checks if an email address is available")
    @GetMapping("/check-email/{email}")
    public ResponseEntity<Boolean> checkEmailAvailability(
            @Parameter(description = "Email address") @PathVariable String email) {
        boolean exists = customerService.emailExists(email);
        return ResponseEntity.ok(!exists);
    }
    
    @Operation(summary = "Check customer number availability", description = "Checks if a customer number is available")
    @GetMapping("/check-number/{customerNumber}")
    public ResponseEntity<Boolean> checkCustomerNumberAvailability(
            @Parameter(description = "Customer number") @PathVariable String customerNumber) {
        boolean exists = customerService.customerNumberExists(customerNumber);
        return ResponseEntity.ok(!exists);
    }
    
    @Operation(summary = "Get customer statistics", description = "Retrieves customer statistics")
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('SCOPE_customer:read')")
    public ResponseEntity<CustomerApplicationService.CustomerStatsDto> getCustomerStatistics() {
        CustomerApplicationService.CustomerStatsDto stats = customerService.getCustomerStatistics();
        return ResponseEntity.ok(stats);
    }
}
