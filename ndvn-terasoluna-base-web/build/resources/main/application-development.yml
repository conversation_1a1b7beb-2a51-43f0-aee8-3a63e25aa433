# Development environment configuration
spring:
  # Database configuration for development
  datasource:
    url: jdbc:h2:mem:devdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # H2 Console enabled for development
  h2:
    console:
      enabled: true
      path: /h2-console
      settings:
        web-allow-others: true
  
  # JPA configuration for development
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true

# Development server configuration
server:
  port: 8080

# Logging configuration for development
logging:
  level:
    com.nttdata.ndvn: DEBUG
    org.springframework: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
