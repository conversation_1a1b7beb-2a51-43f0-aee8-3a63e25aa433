package com.nttdata.ndvn.infrastructure.config;

import javax.sql.DataSource;

import org.apache.commons.dbcp2.BasicDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * DataSource configuration class.
 * This class provides DataSource bean configuration using Java-based configuration.
 */
@Configuration
public class DataSourceConfig {
    
    @Value("${database.url:jdbc:h2:mem:testdb}")
    private String databaseUrl;
    
    @Value("${database.username:sa}")
    private String databaseUsername;
    
    @Value("${database.password:}")
    private String databasePassword;
    
    @Value("${database.driverClassName:org.h2.Driver}")
    private String driverClassName;
    
    /**
     * Creates and configures the DataSource bean.
     * 
     * @return configured DataSource
     */
    @Bean
    @Profile("!test")
    public DataSource dataSource() {
        BasicDataSource dataSource = new BasicDataSource();
        dataSource.setUrl(databaseUrl);
        dataSource.setUsername(databaseUsername);
        dataSource.setPassword(databasePassword);
        dataSource.setDriverClassName(driverClassName);
        
        // Connection pool settings
        dataSource.setInitialSize(5);
        dataSource.setMaxTotal(10);
        dataSource.setMaxIdle(5);
        dataSource.setMinIdle(1);
        dataSource.setMaxWaitMillis(60000);
        
        return dataSource;
    }
}
