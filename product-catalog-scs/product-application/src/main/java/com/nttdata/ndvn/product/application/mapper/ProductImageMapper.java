package com.nttdata.ndvn.product.application.mapper;

import com.nttdata.ndvn.product.application.dto.ProductImageDto;
import com.nttdata.ndvn.product.domain.model.ProductImage;
import org.mapstruct.*;

import java.util.List;

/**
 * MapStruct mapper for ProductImage entity and DTO.
 */
@Mapper(componentModel = "spring", 
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ProductImageMapper {
    
    @Mapping(target = "aspectRatio", ignore = true)
    @Mapping(target = "landscape", ignore = true)
    @Mapping(target = "portrait", ignore = true)
    @Mapping(target = "square", ignore = true)
    ProductImageDto toDto(ProductImage image);
    
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ProductImage toEntity(ProductImageDto imageDto);
    
    List<ProductImageDto> toDtoList(List<ProductImage> images);
    List<ProductImage> toEntityList(List<ProductImageDto> imageDtos);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(ProductImageDto imageDto, @MappingTarget ProductImage image);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "product", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    ProductImage toNewEntity(ProductImageDto imageDto);
}
