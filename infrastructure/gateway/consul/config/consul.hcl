# Consul Configuration for NDVN SCS Platform

# Basic Configuration
datacenter = "ndvn-dc1"
data_dir = "/consul/data"
log_level = "INFO"
node_name = "consul-server-1"
server = true

# Cluster Configuration
bootstrap_expect = 1
retry_join = []

# Network Configuration
bind_addr = "0.0.0.0"
client_addr = "0.0.0.0"

# UI Configuration
ui_config {
  enabled = true
}

# Connect Configuration (for service mesh)
connect {
  enabled = true
}

# Ports Configuration
ports {
  grpc = 8502
  grpc_tls = 8503
}

# ACL Configuration (disabled for development)
acl = {
  enabled = false
  default_policy = "allow"
  enable_token_persistence = true
}

# Performance Configuration
performance {
  raft_multiplier = 1
}

# Logging Configuration
log_rotate_duration = "24h"
log_rotate_max_files = 3

# Health Check Configuration
check_update_interval = "5m"

# Service Definitions
services {
  name = "consul"
  port = 8500
  tags = ["consul", "service-discovery", "ndvn-scs"]
  
  check {
    name = "Consul HTTP"
    http = "http://localhost:8500/v1/status/leader"
    interval = "30s"
    timeout = "10s"
  }
}

# Default Service Configuration
config_entries {
  bootstrap = [
    {
      kind = "proxy-defaults"
      name = "global"
      
      config {
        protocol = "http"
        
        # Envoy proxy configuration
        envoy_gateway_bind_tagged_addresses = true
        envoy_gateway_bind_addresses = {
          "lan" = {
            address = "0.0.0.0"
            port = 8080
          }
        }
      }
      
      # Mesh gateway configuration
      mesh_gateway {
        mode = "local"
      }
      
      # Expose configuration
      expose {
        checks = true
        paths = [
          {
            path = "/actuator/health"
            local_path_port = 8080
            listener_port = 8080
            protocol = "http"
          }
        ]
      }
    },
    {
      kind = "service-defaults"
      name = "api-gateway"
      
      protocol = "http"
      
      # External source configuration
      external_sni = "api-gateway.service.consul"
      
      # Mesh gateway configuration
      mesh_gateway {
        mode = "local"
      }
      
      # Upstream configuration
      upstream_config {
        overrides = [
          {
            name = "user-management-service"
            passive_health_check {
              max_failures = 3
              interval = "30s"
            }
            load_balancer {
              policy = "round_robin"
              hash_policies = [
                {
                  field = "header"
                  field_value = "x-user-id"
                }
              ]
            }
          },
          {
            name = "customer-management-service"
            passive_health_check {
              max_failures = 3
              interval = "30s"
            }
            load_balancer {
              policy = "round_robin"
            }
          },
          {
            name = "product-catalog-service"
            passive_health_check {
              max_failures = 3
              interval = "30s"
            }
            load_balancer {
              policy = "round_robin"
            }
          },
          {
            name = "order-management-service"
            passive_health_check {
              max_failures = 3
              interval = "30s"
            }
            load_balancer {
              policy = "round_robin"
            }
          },
          {
            name = "notification-service"
            passive_health_check {
              max_failures = 3
              interval = "30s"
            }
            load_balancer {
              policy = "round_robin"
            }
          }
        ]
      }
    }
  ]
}
