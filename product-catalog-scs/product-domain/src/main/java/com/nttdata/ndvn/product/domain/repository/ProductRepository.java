package com.nttdata.ndvn.product.domain.repository;

import com.nttdata.ndvn.product.domain.model.Product;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Product aggregate.
 * 
 * This interface defines the contract for product data access operations,
 * following the Repository pattern from Domain-Driven Design.
 */
public interface ProductRepository {
    
    /**
     * Save a product entity.
     */
    Product save(Product product);
    
    /**
     * Find product by ID.
     */
    Optional<Product> findById(UUID id);
    
    /**
     * Find product by SKU.
     */
    Optional<Product> findBySku(String sku);
    
    /**
     * Find all products with pagination.
     */
    Page<Product> findAll(Pageable pageable);
    
    /**
     * Find products by status.
     */
    Page<Product> findByStatus(ProductStatus status, Pageable pageable);
    
    /**
     * Find products by category.
     */
    Page<Product> findByCategoryId(UUID categoryId, Pageable pageable);
    
    /**
     * Find products by brand.
     */
    Page<Product> findByBrandId(UUID brandId, Pageable pageable);
    
    /**
     * Find featured products.
     */
    Page<Product> findByFeatured(boolean featured, Pageable pageable);
    
    /**
     * Find products by price range.
     */
    Page<Product> findByBasePriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    /**
     * Find products on sale.
     */
    Page<Product> findProductsOnSale(Pageable pageable);
    
    /**
     * Search products by name or description.
     */
    Page<Product> searchProducts(String searchTerm, Pageable pageable);
    
    /**
     * Find products created after a specific date.
     */
    Page<Product> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find products updated after a specific date.
     */
    Page<Product> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find products by multiple categories.
     */
    Page<Product> findByCategoryIdIn(List<UUID> categoryIds, Pageable pageable);
    
    /**
     * Find products by multiple brands.
     */
    Page<Product> findByBrandIdIn(List<UUID> brandIds, Pageable pageable);
    
    /**
     * Find products by multiple statuses.
     */
    Page<Product> findByStatusIn(List<ProductStatus> statuses, Pageable pageable);
    
    /**
     * Find digital products.
     */
    Page<Product> findByDigital(boolean digital, Pageable pageable);
    
    /**
     * Find products that require shipping.
     */
    Page<Product> findByRequiresShipping(boolean requiresShipping, Pageable pageable);
    
    /**
     * Find taxable products.
     */
    Page<Product> findByTaxable(boolean taxable, Pageable pageable);
    
    /**
     * Check if SKU exists.
     */
    boolean existsBySku(String sku);
    
    /**
     * Check if product exists by ID.
     */
    boolean existsById(UUID id);
    
    /**
     * Count products by status.
     */
    long countByStatus(ProductStatus status);
    
    /**
     * Count products by category.
     */
    long countByCategoryId(UUID categoryId);
    
    /**
     * Count products by brand.
     */
    long countByBrandId(UUID brandId);
    
    /**
     * Count featured products.
     */
    long countByFeatured(boolean featured);
    
    /**
     * Count products created after a specific date.
     */
    long countByCreatedAtAfter(LocalDateTime createdAfter);
    
    /**
     * Find products with low inventory.
     */
    List<Product> findProductsWithLowInventory();
    
    /**
     * Find products without images.
     */
    Page<Product> findProductsWithoutImages(Pageable pageable);
    
    /**
     * Find products without variants.
     */
    Page<Product> findProductsWithoutVariants(Pageable pageable);
    
    /**
     * Find products by name containing (case-insensitive).
     */
    Page<Product> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find products by description containing (case-insensitive).
     */
    Page<Product> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find products by meta keywords containing.
     */
    Page<Product> findByMetaKeywordsContaining(String keywords, Pageable pageable);
    
    /**
     * Find products ordered by creation date.
     */
    Page<Product> findAllOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * Find products ordered by update date.
     */
    Page<Product> findAllOrderByUpdatedAtDesc(Pageable pageable);
    
    /**
     * Find products ordered by name.
     */
    Page<Product> findAllOrderByNameAsc(Pageable pageable);
    
    /**
     * Find products ordered by price.
     */
    Page<Product> findAllOrderByBasePriceAsc(Pageable pageable);
    
    /**
     * Find products ordered by sort order.
     */
    Page<Product> findAllOrderBySortOrderAsc(Pageable pageable);
    
    /**
     * Advanced search with multiple criteria.
     */
    Page<Product> findProductsByCriteria(
        String searchTerm,
        List<UUID> categoryIds,
        List<UUID> brandIds,
        List<ProductStatus> statuses,
        BigDecimal minPrice,
        BigDecimal maxPrice,
        Boolean featured,
        Boolean digital,
        Boolean onSale,
        Pageable pageable
    );
    
    /**
     * Delete product by ID.
     */
    void deleteById(UUID id);
    
    /**
     * Delete product entity.
     */
    void delete(Product product);
    
    /**
     * Find all products by IDs.
     */
    List<Product> findAllById(Iterable<UUID> ids);
    
    /**
     * Save all products.
     */
    List<Product> saveAll(Iterable<Product> products);
    
    /**
     * Get products count.
     */
    long count();
    
    /**
     * Find random products for recommendations.
     */
    List<Product> findRandomProducts(int limit);
    
    /**
     * Find related products by category.
     */
    List<Product> findRelatedProductsByCategory(UUID categoryId, UUID excludeProductId, int limit);
    
    /**
     * Find related products by brand.
     */
    List<Product> findRelatedProductsByBrand(UUID brandId, UUID excludeProductId, int limit);
    
    /**
     * Find best selling products.
     */
    List<Product> findBestSellingProducts(int limit);
    
    /**
     * Find newest products.
     */
    List<Product> findNewestProducts(int limit);
}
