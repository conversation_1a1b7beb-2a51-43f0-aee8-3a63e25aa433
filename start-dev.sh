#!/bin/bash

# NDVN Terasoluna Base - Development Startup Script

echo "🚀 Starting NDVN Terasoluna Base Application..."
echo "📋 Framework: Terasoluna 5.10.0.RELEASE"
echo "🌱 Spring Boot: 3.4.1"
echo "☕ Java: $(java -version 2>&1 | head -n 1)"
echo ""

# Check if Java 17+ is available
java_version=$(java -version 2>&1 | head -n 1 | cut -d'"' -f2 | cut -d'.' -f1)
if [ "$java_version" -lt 17 ]; then
    echo "❌ Error: Java 17 or higher is required. Current version: $java_version"
    exit 1
fi

echo "✅ Java version check passed"
echo ""

# Build the project
echo "🔨 Building the project..."
./gradlew build

if [ $? -eq 0 ]; then
    echo "✅ Build successful"
    echo ""
    
    # Start the application
    echo "🌐 Starting the application on http://localhost:8080"
    echo "🔍 Health check: http://localhost:8080/api/health"
    echo "🗄️  H2 Console: http://localhost:8080/h2-console"
    echo ""
    echo "Press Ctrl+C to stop the application"
    echo ""
    
    ./gradlew :ndvn-terasoluna-base-web:bootRun --args='--spring.profiles.active=development'
else
    echo "❌ Build failed. Please check the error messages above."
    exit 1
fi
