package com.nttdata.ndvn.user.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * UserSession entity representing an active user session with authentication token.
 * 
 * This entity tracks user sessions for security purposes, enabling session management,
 * concurrent session control, and security auditing within the User Management bounded context.
 */
@Entity
@Table(name = "user_sessions", indexes = {
    @Index(name = "idx_user_sessions_token", columnList = "session_token"),
    @Index(name = "idx_user_sessions_user_id", columnList = "user_id"),
    @Index(name = "idx_user_sessions_expires_at", columnList = "expires_at"),
    @Index(name = "idx_user_sessions_active", columnList = "active")
})
public class UserSession {
    
    @Id
    @Column(name = "id")
    private UUID id;
    
    @NotNull(message = "User is required")
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    private User user;
    
    @NotBlank(message = "Session token is required")
    @Column(name = "session_token", unique = true, nullable = false)
    private String sessionToken;
    
    @NotNull(message = "Expires at is required")
    @Column(name = "expires_at", nullable = false)
    private LocalDateTime expiresAt;
    
    @Column(name = "active", nullable = false)
    private boolean active = true;
    
    @Column(name = "ip_address", length = 45)
    private String ipAddress;
    
    @Column(name = "user_agent", length = 500)
    private String userAgent;
    
    @Column(name = "device_info", length = 255)
    private String deviceInfo;
    
    @Column(name = "location", length = 100)
    private String location;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "last_accessed_at", nullable = false)
    private LocalDateTime lastAccessedAt;
    
    @Column(name = "terminated_at")
    private LocalDateTime terminatedAt;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "termination_reason", length = 50)
    private TerminationReason terminationReason;
    
    // Constructors
    protected UserSession() {
        // JPA constructor
    }
    
    public UserSession(User user, String sessionToken, LocalDateTime expiresAt) {
        this.id = UUID.randomUUID();
        this.user = user;
        this.sessionToken = sessionToken;
        this.expiresAt = expiresAt;
        this.createdAt = LocalDateTime.now();
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    public UserSession(User user, String sessionToken, LocalDateTime expiresAt, 
                      String ipAddress, String userAgent) {
        this(user, sessionToken, expiresAt);
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
    }
    
    // Business methods
    
    /**
     * Checks if this session is currently valid.
     * A session is valid if it's active and not expired.
     * 
     * @return true if the session is valid, false otherwise
     */
    public boolean isValid() {
        return active && !isExpired();
    }
    
    /**
     * Checks if this session has expired.
     * 
     * @return true if the session has expired, false otherwise
     */
    public boolean isExpired() {
        return LocalDateTime.now().isAfter(expiresAt);
    }
    
    /**
     * Updates the last accessed time to the current time.
     * This should be called whenever the session is used.
     */
    public void updateLastAccessed() {
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    /**
     * Extends the session expiration time.
     * 
     * @param newExpiresAt the new expiration time
     * @throws IllegalArgumentException if the new expiration time is in the past
     */
    public void extendExpiration(LocalDateTime newExpiresAt) {
        if (newExpiresAt.isBefore(LocalDateTime.now())) {
            throw new IllegalArgumentException("New expiration time cannot be in the past");
        }
        this.expiresAt = newExpiresAt;
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    /**
     * Terminates this session with the specified reason.
     * 
     * @param reason the reason for termination
     */
    public void terminate(TerminationReason reason) {
        this.active = false;
        this.terminatedAt = LocalDateTime.now();
        this.terminationReason = reason;
    }
    
    /**
     * Terminates this session due to logout.
     */
    public void logout() {
        terminate(TerminationReason.LOGOUT);
    }
    
    /**
     * Terminates this session due to expiration.
     */
    public void expire() {
        terminate(TerminationReason.EXPIRED);
    }
    
    /**
     * Terminates this session due to security reasons.
     */
    public void terminateForSecurity() {
        terminate(TerminationReason.SECURITY);
    }
    
    /**
     * Updates session metadata.
     * 
     * @param ipAddress the IP address
     * @param userAgent the user agent
     * @param deviceInfo the device information
     * @param location the location
     */
    public void updateMetadata(String ipAddress, String userAgent, String deviceInfo, String location) {
        this.ipAddress = ipAddress;
        this.userAgent = userAgent;
        this.deviceInfo = deviceInfo;
        this.location = location;
        this.lastAccessedAt = LocalDateTime.now();
    }
    
    /**
     * Calculates the session duration in minutes.
     * 
     * @return the session duration in minutes
     */
    public long getDurationInMinutes() {
        LocalDateTime endTime = terminatedAt != null ? terminatedAt : LocalDateTime.now();
        return java.time.Duration.between(createdAt, endTime).toMinutes();
    }
    
    // JPA lifecycle callbacks
    
    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        LocalDateTime now = LocalDateTime.now();
        if (createdAt == null) {
            createdAt = now;
        }
        if (lastAccessedAt == null) {
            lastAccessedAt = now;
        }
    }
    
    // Getters and setters
    
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public User getUser() {
        return user;
    }
    
    public void setUser(User user) {
        this.user = user;
    }
    
    public String getSessionToken() {
        return sessionToken;
    }
    
    public void setSessionToken(String sessionToken) {
        this.sessionToken = sessionToken;
    }
    
    public LocalDateTime getExpiresAt() {
        return expiresAt;
    }
    
    public void setExpiresAt(LocalDateTime expiresAt) {
        this.expiresAt = expiresAt;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public String getUserAgent() {
        return userAgent;
    }
    
    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
    
    public String getDeviceInfo() {
        return deviceInfo;
    }
    
    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }
    
    public String getLocation() {
        return location;
    }
    
    public void setLocation(String location) {
        this.location = location;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public LocalDateTime getLastAccessedAt() {
        return lastAccessedAt;
    }
    
    public LocalDateTime getTerminatedAt() {
        return terminatedAt;
    }
    
    public TerminationReason getTerminationReason() {
        return terminationReason;
    }
    
    // equals, hashCode, toString
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserSession that = (UserSession) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "UserSession{" +
                "id=" + id +
                ", sessionToken='" + sessionToken + '\'' +
                ", expiresAt=" + expiresAt +
                ", active=" + active +
                ", ipAddress='" + ipAddress + '\'' +
                ", createdAt=" + createdAt +
                '}';
    }
    
    /**
     * Enumeration of session termination reasons.
     */
    public enum TerminationReason {
        LOGOUT,
        EXPIRED,
        SECURITY,
        ADMIN_TERMINATED,
        CONCURRENT_SESSION_LIMIT,
        SYSTEM_SHUTDOWN
    }
}
