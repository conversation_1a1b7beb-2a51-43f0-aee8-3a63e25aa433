package com.nttdata.ndvn.user.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Role entity representing a collection of permissions that can be assigned to users.
 * 
 * Roles provide a way to group permissions and assign them to users, implementing
 * role-based access control (RBAC) within the User Management bounded context.
 */
@Entity
@Table(name = "roles", indexes = {
    @Index(name = "idx_roles_name", columnList = "name")
})
public class Role {
    
    @Id
    @Column(name = "id")
    private UUID id;
    
    @NotBlank(message = "Role name is required")
    @Size(min = 2, max = 50, message = "Role name must be between 2 and 50 characters")
    @Column(name = "name", unique = true, nullable = false, length = 50)
    private String name;
    
    @Size(max = 255, message = "Description must not exceed 255 characters")
    @Column(name = "description", length = 255)
    private String description;
    
    @Column(name = "system_role", nullable = false)
    private boolean systemRole = false;
    
    @Column(name = "active", nullable = false)
    private boolean active = true;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
        name = "role_permissions",
        joinColumns = @JoinColumn(name = "role_id"),
        inverseJoinColumns = @JoinColumn(name = "permission_id")
    )
    private Set<Permission> permissions = new HashSet<>();
    
    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>();
    
    // Constructors
    public Role() {
        // JPA constructor
    }
    
    public Role(String name) {
        this.id = UUID.randomUUID();
        this.name = name;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public Role(String name, String description) {
        this(name);
        this.description = description;
    }
    
    // Business methods
    
    /**
     * Grants a permission to this role.
     * 
     * @param permission the permission to grant
     * @throws IllegalArgumentException if permission is null
     */
    public void grantPermission(Permission permission) {
        if (permission == null) {
            throw new IllegalArgumentException("Permission cannot be null");
        }
        this.permissions.add(permission);
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Revokes a permission from this role.
     * 
     * @param permission the permission to revoke
     */
    public void revokePermission(Permission permission) {
        if (permission != null) {
            this.permissions.remove(permission);
            this.updatedAt = LocalDateTime.now();
        }
    }
    
    /**
     * Checks if this role has a specific permission.
     * 
     * @param permissionName the name of the permission to check
     * @return true if the role has the permission, false otherwise
     */
    public boolean hasPermission(String permissionName) {
        return permissions.stream()
            .anyMatch(permission -> permission.getName().equals(permissionName));
    }
    
    /**
     * Checks if this role has any of the specified permissions.
     * 
     * @param permissionNames the names of the permissions to check
     * @return true if the role has any of the permissions, false otherwise
     */
    public boolean hasAnyPermission(String... permissionNames) {
        Set<String> permissionNameSet = Set.of(permissionNames);
        return permissions.stream()
            .anyMatch(permission -> permissionNameSet.contains(permission.getName()));
    }
    
    /**
     * Checks if this role has all of the specified permissions.
     * 
     * @param permissionNames the names of the permissions to check
     * @return true if the role has all of the permissions, false otherwise
     */
    public boolean hasAllPermissions(String... permissionNames) {
        Set<String> permissionNameSet = Set.of(permissionNames);
        Set<String> rolePermissionNames = permissions.stream()
            .map(Permission::getName)
            .collect(java.util.stream.Collectors.toSet());
        return rolePermissionNames.containsAll(permissionNameSet);
    }
    
    /**
     * Activates this role.
     */
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates this role.
     */
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Marks this role as a system role.
     * System roles cannot be deleted and have restricted modification.
     */
    public void markAsSystemRole() {
        this.systemRole = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Checks if this role can be deleted.
     * System roles and roles with assigned users cannot be deleted.
     * 
     * @return true if the role can be deleted, false otherwise
     */
    public boolean canBeDeleted() {
        return !systemRole && users.isEmpty();
    }
    
    /**
     * Updates the role description.
     * 
     * @param description the new description
     */
    public void updateDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    // JPA lifecycle callbacks
    
    @PrePersist
    protected void onCreate() {
        if (id == null) {
            id = UUID.randomUUID();
        }
        LocalDateTime now = LocalDateTime.now();
        createdAt = now;
        updatedAt = now;
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and setters
    
    public UUID getId() {
        return id;
    }
    
    public void setId(UUID id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
        this.updatedAt = LocalDateTime.now();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public boolean isSystemRole() {
        return systemRole;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }
    
    public Set<Permission> getPermissions() {
        return Collections.unmodifiableSet(permissions);
    }
    
    public Set<User> getUsers() {
        return Collections.unmodifiableSet(users);
    }
    
    // equals, hashCode, toString
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Role role = (Role) o;
        return Objects.equals(id, role.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", systemRole=" + systemRole +
                ", active=" + active +
                '}';
    }
}
