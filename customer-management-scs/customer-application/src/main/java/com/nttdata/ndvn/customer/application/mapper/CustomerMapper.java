package com.nttdata.ndvn.customer.application.mapper;

import com.nttdata.ndvn.customer.application.dto.CustomerAddressDto;
import com.nttdata.ndvn.customer.application.dto.CustomerDto;
import com.nttdata.ndvn.customer.application.dto.CustomerNoteDto;
import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerAddress;
import com.nttdata.ndvn.customer.domain.model.CustomerNote;
import com.nttdata.ndvn.customer.domain.model.CustomerSegmentAssignment;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.List;
import java.util.stream.Collectors;

/**
 * MapStruct mapper for Customer entities and DTOs.
 */
@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface CustomerMapper {
    
    @Mapping(target = "segmentNames", source = "segmentAssignments", qualifiedByName = "mapSegmentNames")
    CustomerDto toDto(Customer customer);
    
    @Mapping(target = "segmentAssignments", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    @Mapping(target = "id", ignore = true)
    Customer toEntity(CustomerDto customerDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "segmentAssignments", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateEntityFromDto(CustomerDto customerDto, @MappingTarget Customer customer);
    
    List<CustomerDto> toDtoList(List<Customer> customers);
    
    List<Customer> toEntityList(List<CustomerDto> customerDtos);
    
    // Address mappings
    CustomerAddressDto toDto(CustomerAddress address);
    
    @Mapping(target = "customer", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    CustomerAddress toEntity(CustomerAddressDto addressDto);
    
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "customer", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "updatedAt", ignore = true)
    void updateAddressFromDto(CustomerAddressDto addressDto, @MappingTarget CustomerAddress address);
    
    List<CustomerAddressDto> toAddressDtoList(List<CustomerAddress> addresses);
    
    List<CustomerAddress> toAddressEntityList(List<CustomerAddressDto> addressDtos);
    
    // Note mappings
    CustomerNoteDto toDto(CustomerNote note);
    
    @Mapping(target = "customer", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    CustomerNote toEntity(CustomerNoteDto noteDto);
    
    List<CustomerNoteDto> toNoteDtoList(List<CustomerNote> notes);
    
    List<CustomerNote> toNoteEntityList(List<CustomerNoteDto> noteDtos);
    
    // Custom mapping methods
    @Named("mapSegmentNames")
    default List<String> mapSegmentNames(List<CustomerSegmentAssignment> assignments) {
        if (assignments == null) {
            return null;
        }
        return assignments.stream()
            .map(assignment -> assignment.getSegment().getName())
            .collect(Collectors.toList());
    }
}
