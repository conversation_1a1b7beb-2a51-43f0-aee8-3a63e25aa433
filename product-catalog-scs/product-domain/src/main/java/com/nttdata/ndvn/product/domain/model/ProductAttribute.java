package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * ProductAttribute entity representing custom attributes for products.
 * 
 * This entity allows for flexible product attribute management,
 * supporting various attribute types and values.
 */
@Entity
@Table(name = "product_attributes", indexes = {
    @Index(name = "idx_attribute_product", columnList = "productId"),
    @Index(name = "idx_attribute_name", columnList = "attributeName"),
    @Index(name = "idx_attribute_type", columnList = "attributeType")
})
@Getter
@Setter
@NoArgsConstructor
public class ProductAttribute {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotNull(message = "Product is required")
    @Column(name = "product_id", nullable = false)
    private UUID productId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;
    
    @NotBlank(message = "Attribute name is required")
    @Size(max = 255, message = "Attribute name must not exceed 255 characters")
    @Column(name = "attribute_name", nullable = false)
    private String attributeName;
    
    @NotNull(message = "Attribute type is required")
    @Enumerated(EnumType.STRING)
    @Column(name = "attribute_type", nullable = false)
    private AttributeType attributeType;
    
    @NotBlank(message = "Attribute value is required")
    @Size(max = 1000, message = "Attribute value must not exceed 1000 characters")
    @Column(name = "attribute_value", nullable = false, length = 1000)
    private String attributeValue;
    
    @Size(max = 100, message = "Unit must not exceed 100 characters")
    @Column(name = "unit", length = 100)
    private String unit;
    
    @Column(name = "is_filterable")
    private boolean filterable = false;
    
    @Column(name = "is_searchable")
    private boolean searchable = false;
    
    @Column(name = "is_visible")
    private boolean visible = true;
    
    @Min(value = 0, message = "Sort order must be non-negative")
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    /**
     * Enumeration for attribute types.
     */
    public enum AttributeType {
        TEXT,
        NUMBER,
        BOOLEAN,
        DATE,
        URL,
        EMAIL,
        COLOR,
        DIMENSION,
        WEIGHT,
        PERCENTAGE,
        CURRENCY
    }
    
    // Business methods
    
    /**
     * Updates the attribute information.
     */
    public void updateAttribute(String attributeName, AttributeType attributeType, String attributeValue, String unit) {
        this.attributeName = attributeName;
        this.attributeType = attributeType;
        this.attributeValue = attributeValue;
        this.unit = unit;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the attribute visibility and search settings.
     */
    public void updateSettings(boolean filterable, boolean searchable, boolean visible) {
        this.filterable = filterable;
        this.searchable = searchable;
        this.visible = visible;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the sort order.
     */
    public void updateSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Gets the formatted attribute value with unit if applicable.
     */
    public String getFormattedValue() {
        if (unit != null && !unit.trim().isEmpty()) {
            return attributeValue + " " + unit;
        }
        return attributeValue;
    }
    
    /**
     * Validates the attribute value based on its type.
     */
    public boolean isValidValue() {
        if (attributeValue == null || attributeValue.trim().isEmpty()) {
            return false;
        }
        
        try {
            switch (attributeType) {
                case NUMBER:
                case DIMENSION:
                case WEIGHT:
                case PERCENTAGE:
                    Double.parseDouble(attributeValue);
                    return true;
                case BOOLEAN:
                    return "true".equalsIgnoreCase(attributeValue) || "false".equalsIgnoreCase(attributeValue);
                case EMAIL:
                    return attributeValue.matches("^[A-Za-z0-9+_.-]+@(.+)$");
                case URL:
                    return attributeValue.matches("^(http|https)://.*$");
                case COLOR:
                    return attributeValue.matches("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$") || 
                           attributeValue.matches("^[a-zA-Z]+$");
                case DATE:
                    // Basic date format validation - could be enhanced
                    return attributeValue.matches("^\\d{4}-\\d{2}-\\d{2}$");
                case TEXT:
                case CURRENCY:
                default:
                    return true;
            }
        } catch (Exception e) {
            return false;
        }
    }
    
    // Builder pattern
    public static class Builder {
        private UUID productId;
        private String attributeName;
        private AttributeType attributeType;
        private String attributeValue;
        private String unit;
        private boolean filterable = false;
        private boolean searchable = false;
        private boolean visible = true;
        private Integer sortOrder = 0;
        
        public Builder productId(UUID productId) {
            this.productId = productId;
            return this;
        }
        
        public Builder attributeName(String attributeName) {
            this.attributeName = attributeName;
            return this;
        }
        
        public Builder attributeType(AttributeType attributeType) {
            this.attributeType = attributeType;
            return this;
        }
        
        public Builder attributeValue(String attributeValue) {
            this.attributeValue = attributeValue;
            return this;
        }
        
        public Builder unit(String unit) {
            this.unit = unit;
            return this;
        }
        
        public Builder filterable(boolean filterable) {
            this.filterable = filterable;
            return this;
        }
        
        public Builder searchable(boolean searchable) {
            this.searchable = searchable;
            return this;
        }
        
        public Builder visible(boolean visible) {
            this.visible = visible;
            return this;
        }
        
        public Builder sortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
            return this;
        }
        
        public ProductAttribute build() {
            ProductAttribute attribute = new ProductAttribute();
            attribute.productId = this.productId;
            attribute.attributeName = this.attributeName;
            attribute.attributeType = this.attributeType;
            attribute.attributeValue = this.attributeValue;
            attribute.unit = this.unit;
            attribute.filterable = this.filterable;
            attribute.searchable = this.searchable;
            attribute.visible = this.visible;
            attribute.sortOrder = this.sortOrder;
            return attribute;
        }
    }
}
