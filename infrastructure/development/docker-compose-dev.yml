version: '3.8'

# Complete development environment for NDVN SCS Platform
# This compose file brings up all infrastructure services needed for local development

services:
  # PostgreSQL databases for each service
  user-db:
    image: postgres:15
    hostname: user-db
    container_name: ndvn-user-db
    environment:
      POSTGRES_DB: user_management
      POSTGRES_USER: user_service
      POSTGRES_PASSWORD: password
    volumes:
      - user-db-data:/var/lib/postgresql/data
      - ./databases/init-scripts/user-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - ndvn-network
    restart: unless-stopped

  customer-db:
    image: postgres:15
    hostname: customer-db
    container_name: ndvn-customer-db
    environment:
      POSTGRES_DB: customer_management
      POSTGRES_USER: customer_service
      POSTGRES_PASSWORD: password
    volumes:
      - customer-db-data:/var/lib/postgresql/data
      - ./databases/init-scripts/customer-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"
    networks:
      - ndvn-network
    restart: unless-stopped

  catalog-db:
    image: postgres:15
    hostname: catalog-db
    container_name: ndvn-catalog-db
    environment:
      POSTGRES_DB: product_catalog
      POSTGRES_USER: catalog_service
      POSTGRES_PASSWORD: password
    volumes:
      - catalog-db-data:/var/lib/postgresql/data
      - ./databases/init-scripts/catalog-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5434:5432"
    networks:
      - ndvn-network
    restart: unless-stopped

  order-db:
    image: postgres:15
    hostname: order-db
    container_name: ndvn-order-db
    environment:
      POSTGRES_DB: order_management
      POSTGRES_USER: order_service
      POSTGRES_PASSWORD: password
    volumes:
      - order-db-data:/var/lib/postgresql/data
      - ./databases/init-scripts/order-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5435:5432"
    networks:
      - ndvn-network
    restart: unless-stopped

  notification-db:
    image: postgres:15
    hostname: notification-db
    container_name: ndvn-notification-db
    environment:
      POSTGRES_DB: notification_service
      POSTGRES_USER: notification_service
      POSTGRES_PASSWORD: password
    volumes:
      - notification-db-data:/var/lib/postgresql/data
      - ./databases/init-scripts/notification-db.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5436:5432"
    networks:
      - ndvn-network
    restart: unless-stopped

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    hostname: redis
    container_name: ndvn-redis
    command: redis-server --appendonly yes --requirepass redis-password
    volumes:
      - redis-data:/data
    ports:
      - "6379:6379"
    networks:
      - ndvn-network
    restart: unless-stopped

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    hostname: mailhog
    container_name: ndvn-mailhog
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - ndvn-network
    restart: unless-stopped

  # MinIO for S3-compatible object storage
  minio:
    image: minio/minio:latest
    hostname: minio
    container_name: ndvn-minio
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio-data:/data
    ports:
      - "9000:9000"  # API
      - "9001:9001"  # Console
    networks:
      - ndvn-network
    restart: unless-stopped

  # Adminer for database management
  adminer:
    image: adminer:latest
    hostname: adminer
    container_name: ndvn-adminer
    environment:
      ADMINER_DEFAULT_SERVER: user-db
    ports:
      - "8082:8080"
    networks:
      - ndvn-network
    restart: unless-stopped

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    hostname: redis-commander
    container_name: ndvn-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379:0:redis-password
    ports:
      - "8083:8081"
    networks:
      - ndvn-network
    restart: unless-stopped

  # Nginx for local reverse proxy and static file serving
  nginx:
    image: nginx:alpine
    hostname: nginx
    container_name: ndvn-nginx
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/html:/usr/share/nginx/html:ro
    ports:
      - "8084:80"
    networks:
      - ndvn-network
    restart: unless-stopped

  # Development tools container
  dev-tools:
    image: ndvn/dev-tools:latest
    hostname: dev-tools
    container_name: ndvn-dev-tools
    build:
      context: ./dev-tools
      dockerfile: Dockerfile
    volumes:
      - ../..:/workspace
      - dev-tools-home:/home/<USER>
    working_dir: /workspace
    networks:
      - ndvn-network
    stdin_open: true
    tty: true
    restart: unless-stopped

volumes:
  user-db-data:
    driver: local
  customer-db-data:
    driver: local
  catalog-db-data:
    driver: local
  order-db-data:
    driver: local
  notification-db-data:
    driver: local
  redis-data:
    driver: local
  minio-data:
    driver: local
  dev-tools-home:
    driver: local

networks:
  ndvn-network:
    external: true
    name: ndvn-scs-network
