package com.nttdata.ndvn.customer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import java.util.UUID;

public class CustomerStatusChangedEvent {
    private UUID eventId;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    private UUID customerId;
    private String customerNumber;
    private String previousStatus;
    private String newStatus;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime changedAt;

    public CustomerStatusChangedEvent() {}

    private CustomerStatusChangedEvent(Builder builder) {
        this.eventId = builder.eventId;
        this.eventTime = builder.eventTime;
        this.customerId = builder.customerId;
        this.customerNumber = builder.customerNumber;
        this.previousStatus = builder.previousStatus;
        this.newStatus = builder.newStatus;
        this.changedAt = builder.changedAt;
    }

    public static Builder builder() { return new Builder(); }

    public static class Builder {
        private UUID eventId;
        private LocalDateTime eventTime;
        private UUID customerId;
        private String customerNumber;
        private String previousStatus;
        private String newStatus;
        private LocalDateTime changedAt;

        public Builder eventId(UUID eventId) { this.eventId = eventId; return this; }
        public Builder eventTime(LocalDateTime eventTime) { this.eventTime = eventTime; return this; }
        public Builder customerId(UUID customerId) { this.customerId = customerId; return this; }
        public Builder customerNumber(String customerNumber) { this.customerNumber = customerNumber; return this; }
        public Builder previousStatus(String previousStatus) { this.previousStatus = previousStatus; return this; }
        public Builder newStatus(String newStatus) { this.newStatus = newStatus; return this; }
        public Builder changedAt(LocalDateTime changedAt) { this.changedAt = changedAt; return this; }
        public CustomerStatusChangedEvent build() { return new CustomerStatusChangedEvent(this); }
    }

    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    public String getPreviousStatus() { return previousStatus; }
    public void setPreviousStatus(String previousStatus) { this.previousStatus = previousStatus; }
    public String getNewStatus() { return newStatus; }
    public void setNewStatus(String newStatus) { this.newStatus = newStatus; }
    public LocalDateTime getChangedAt() { return changedAt; }
    public void setChangedAt(LocalDateTime changedAt) { this.changedAt = changedAt; }
}
