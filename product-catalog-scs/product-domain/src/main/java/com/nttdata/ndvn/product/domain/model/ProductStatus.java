package com.nttdata.ndvn.product.domain.model;

/**
 * Enumeration representing the lifecycle states of a product.
 * 
 * This enum defines the various states a product can be in throughout its lifecycle,
 * from initial creation to final archival.
 */
public enum ProductStatus {
    /**
     * Product is in draft state - not yet published or available for sale.
     */
    DRAFT,
    
    /**
     * Product is active and available for sale.
     */
    ACTIVE,
    
    /**
     * Product is temporarily inactive but may be reactivated.
     */
    INACTIVE,
    
    /**
     * Product is discontinued - no longer available for new orders.
     */
    DISCONTINUED,
    
    /**
     * Product is archived - historical record only.
     */
    ARCHIVED
}
