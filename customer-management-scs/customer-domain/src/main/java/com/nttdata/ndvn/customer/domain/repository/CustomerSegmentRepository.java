package com.nttdata.ndvn.customer.domain.repository;

import com.nttdata.ndvn.customer.domain.model.CustomerSegment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for CustomerSegment entity.
 */
public interface CustomerSegmentRepository {
    
    /**
     * Save a customer segment entity.
     */
    CustomerSegment save(CustomerSegment segment);
    
    /**
     * Find segment by ID.
     */
    Optional<CustomerSegment> findById(UUID id);
    
    /**
     * Find segment by name.
     */
    Optional<CustomerSegment> findByName(String name);
    
    /**
     * Find all segments with pagination.
     */
    Page<CustomerSegment> findAll(Pageable pageable);
    
    /**
     * Find active segments.
     */
    Page<CustomerSegment> findByIsActive(boolean isActive, Pageable pageable);
    
    /**
     * Find segments by name containing.
     */
    Page<CustomerSegment> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Check if segment name exists.
     */
    boolean existsByName(String name);
    
    /**
     * Count active segments.
     */
    long countByIsActive(boolean isActive);
    
    /**
     * Find all active segments.
     */
    List<CustomerSegment> findAllActive();
    
    /**
     * Delete segment by ID.
     */
    void deleteById(UUID id);
    
    /**
     * Delete segment entity.
     */
    void delete(CustomerSegment segment);
    
    /**
     * Check if segment exists by ID.
     */
    boolean existsById(UUID id);
}
