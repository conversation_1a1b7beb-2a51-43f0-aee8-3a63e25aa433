#!/bin/bash

# User Management SCS Verification Script
# This script verifies that the User Management service is working correctly

set -e

echo "🚀 User Management SCS Verification Script"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SERVICE_URL="http://localhost:8081"
KEYCLOAK_URL="http://localhost:8090"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if service is running
check_service() {
    local url=$1
    local service_name=$2
    
    print_status "Checking $service_name at $url..."
    
    if curl -s -f "$url" > /dev/null; then
        print_success "$service_name is running"
        return 0
    else
        print_error "$service_name is not accessible"
        return 1
    fi
}

# Function to get JWT token
get_jwt_token() {
    print_status "Getting JWT token from Keycloak..."
    
    local token_response=$(curl -s -X POST "$KEYCLOAK_URL/realms/ndvn/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=admin" \
        -d "password=admin123" \
        -d "grant_type=password" \
        -d "client_id=api-gateway" \
        -d "client_secret=api-gateway-secret" 2>/dev/null)
    
    if [ $? -eq 0 ] && [ -n "$token_response" ]; then
        local access_token=$(echo "$token_response" | grep -o '"access_token":"[^"]*' | cut -d'"' -f4)
        if [ -n "$access_token" ]; then
            print_success "JWT token obtained successfully"
            echo "$access_token"
            return 0
        fi
    fi
    
    print_warning "Could not get JWT token (Keycloak may not be running)"
    echo ""
    return 1
}

# Function to test API endpoint
test_api_endpoint() {
    local endpoint=$1
    local method=$2
    local token=$3
    local description=$4
    
    print_status "Testing $description..."
    
    local auth_header=""
    if [ -n "$token" ]; then
        auth_header="-H \"Authorization: Bearer $token\""
    fi
    
    local response=$(eval curl -s -w "%{http_code}" -X "$method" "$SERVICE_URL$endpoint" $auth_header)
    local http_code="${response: -3}"
    local body="${response%???}"
    
    if [[ "$http_code" =~ ^[23] ]]; then
        print_success "$description - HTTP $http_code"
        return 0
    else
        print_warning "$description - HTTP $http_code"
        return 1
    fi
}

# Main verification process
main() {
    echo
    print_status "Starting User Management SCS verification..."
    echo
    
    # Check if the service is built
    if [ ! -f "user-web/build/libs/user-management-service.jar" ]; then
        print_error "Service JAR not found. Please run './gradlew build' first."
        exit 1
    fi
    
    print_success "Service JAR found"
    echo
    
    # Test public endpoints (no authentication required)
    print_status "Testing public endpoints..."
    test_api_endpoint "/actuator/health" "GET" "" "Health check"
    test_api_endpoint "/actuator/info" "GET" "" "Service info"
    test_api_endpoint "/api/v1/users/check-username/testuser123" "GET" "" "Username availability check"
    test_api_endpoint "/api/v1/users/check-email/<EMAIL>" "GET" "" "Email availability check"
    test_api_endpoint "/api-docs" "GET" "" "API documentation"
    echo
    
    # Try to get JWT token
    JWT_TOKEN=$(get_jwt_token)
    
    if [ -n "$JWT_TOKEN" ]; then
        print_status "Testing authenticated endpoints..."
        test_api_endpoint "/api/v1/users" "GET" "$JWT_TOKEN" "Get all users"
        test_api_endpoint "/api/v1/users/username/admin" "GET" "$JWT_TOKEN" "Get user by username"
        test_api_endpoint "/api/v1/users/search?q=admin" "GET" "$JWT_TOKEN" "Search users"
        echo
    else
        print_warning "Skipping authenticated endpoint tests (no JWT token)"
        echo
    fi
    
    # Service information
    print_status "Service Information:"
    echo "  • Service URL: $SERVICE_URL"
    echo "  • API Documentation: $SERVICE_URL/swagger-ui.html"
    echo "  • Health Check: $SERVICE_URL/actuator/health"
    echo "  • Metrics: $SERVICE_URL/actuator/prometheus"
    echo
    
    # Database information
    print_status "Database Information:"
    echo "  • Database: PostgreSQL"
    echo "  • Host: localhost:5432"
    echo "  • Database: user_management"
    echo "  • Username: user_service"
    echo
    
    # Quick start instructions
    print_status "Quick Start Instructions:"
    echo "  1. Start infrastructure: ./infrastructure/scripts/start-all.sh"
    echo "  2. Run service: ./gradlew :user-web:bootRun"
    echo "  3. Access API docs: http://localhost:8081/swagger-ui.html"
    echo "  4. Get JWT token from Keycloak for authenticated endpoints"
    echo
    
    print_success "User Management SCS verification completed!"
    echo
    print_status "The service is ready for deployment and testing."
}

# Run main function
main "$@"
