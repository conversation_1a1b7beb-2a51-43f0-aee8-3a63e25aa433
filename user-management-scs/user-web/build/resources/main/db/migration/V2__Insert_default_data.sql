-- User Management SCS Default Data
-- Version 2.0 - Insert default roles, permissions, and admin user

-- Insert default permissions
INSERT INTO permissions (id, name, description, resource, action, system_permission, active) VALUES
-- User permissions
(uuid_generate_v4(), 'USER_READ', 'Read user information', 'USER', 'READ', true, true),
(uuid_generate_v4(), 'USER_WRITE', 'Create and update users', 'USER', 'WRITE', true, true),
(uuid_generate_v4(), 'USER_DELETE', 'Delete users', 'USER', 'DELETE', true, true),

-- Role permissions
(uuid_generate_v4(), 'ROLE_READ', 'Read role information', 'ROLE', 'READ', true, true),
(uuid_generate_v4(), 'ROLE_WRITE', 'Create and update roles', 'ROLE', 'WRITE', true, true),
(uuid_generate_v4(), 'ROLE_DELETE', 'Delete roles', 'ROLE', 'DELETE', true, true),

-- Permission permissions
(uuid_generate_v4(), 'PERMISSION_READ', 'Read permission information', 'PERMISSION', 'READ', true, true),
(uuid_generate_v4(), 'PERMISSION_WRITE', 'Create and update permissions', 'PERMISSION', 'WRITE', true, true),
(uuid_generate_v4(), 'PERMISSION_DELETE', 'Delete permissions', 'PERMISSION', 'DELETE', true, true),

-- Session permissions
(uuid_generate_v4(), 'SESSION_READ', 'Read session information', 'SESSION', 'READ', true, true),
(uuid_generate_v4(), 'SESSION_WRITE', 'Manage user sessions', 'SESSION', 'WRITE', true, true),

-- System permissions
(uuid_generate_v4(), 'SYSTEM_ADMIN', 'Full system administration access', 'SYSTEM', 'ADMIN', true, true),
(uuid_generate_v4(), 'SYSTEM_MONITOR', 'System monitoring and health checks', 'SYSTEM', 'MONITOR', true, true),

-- Profile permissions
(uuid_generate_v4(), 'PROFILE_READ', 'Read own profile', 'PROFILE', 'READ', true, true),
(uuid_generate_v4(), 'PROFILE_WRITE', 'Update own profile', 'PROFILE', 'WRITE', true, true);

-- Insert default roles
INSERT INTO roles (id, name, description, system_role, active) VALUES
-- System Administrator role
(uuid_generate_v4(), 'SYSTEM_ADMIN', 'System Administrator with full access', true, true),

-- User Administrator role
(uuid_generate_v4(), 'USER_ADMIN', 'User Administrator with user management access', true, true),

-- Regular User role
(uuid_generate_v4(), 'USER', 'Regular user with basic access', true, true),

-- Guest role
(uuid_generate_v4(), 'GUEST', 'Guest user with read-only access', true, true),

-- Service role for inter-service communication
(uuid_generate_v4(), 'SERVICE', 'Service account for inter-service communication', true, true);

-- Assign permissions to SYSTEM_ADMIN role (all permissions)
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'SYSTEM_ADMIN';

-- Assign permissions to USER_ADMIN role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'USER_ADMIN'
AND p.name IN (
    'USER_READ', 'USER_WRITE', 'USER_DELETE',
    'ROLE_READ', 'ROLE_WRITE',
    'PERMISSION_READ',
    'SESSION_READ', 'SESSION_WRITE',
    'SYSTEM_MONITOR',
    'PROFILE_READ', 'PROFILE_WRITE'
);

-- Assign permissions to USER role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'USER'
AND p.name IN (
    'PROFILE_READ', 'PROFILE_WRITE',
    'SESSION_READ'
);

-- Assign permissions to GUEST role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'GUEST'
AND p.name IN (
    'PROFILE_READ'
);

-- Assign permissions to SERVICE role
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'SERVICE'
AND p.name IN (
    'USER_READ', 'USER_WRITE',
    'ROLE_READ',
    'PERMISSION_READ',
    'SESSION_READ', 'SESSION_WRITE',
    'SYSTEM_MONITOR'
);

-- Insert default admin user
-- Password: admin123 (BCrypt hash with strength 12)
INSERT INTO users (id, username, email, password_hash, enabled, email_verified, account_non_expired, account_non_locked, credentials_non_expired)
VALUES (
    uuid_generate_v4(),
    'admin',
    '<EMAIL>',
    '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqyc6YVs2wlMWEK4lzdU6cK', -- admin123
    true,
    true,
    true,
    true,
    true
);

-- Insert default service user for inter-service communication
-- Password: service123 (BCrypt hash with strength 12)
INSERT INTO users (id, username, email, password_hash, enabled, email_verified, account_non_expired, account_non_locked, credentials_non_expired)
VALUES (
    uuid_generate_v4(),
    'service-user',
    '<EMAIL>',
    '$2a$12$8.UnVuG9HHgffUDAlk8qfOuVGkqRdvEEHZEr.4rAiryRdFUdN/YDO', -- service123
    true,
    true,
    true,
    true,
    true
);

-- Insert test user
-- Password: user123 (BCrypt hash with strength 12)
INSERT INTO users (id, username, email, password_hash, enabled, email_verified, account_non_expired, account_non_locked, credentials_non_expired)
VALUES (
    uuid_generate_v4(),
    'testuser',
    '<EMAIL>',
    '$2a$12$4.VuG9HHgffUDAlk8qfOuVGkqRdvEEHZEr.4rAiryRdFUdN/YDO', -- user123
    true,
    true,
    true,
    true,
    true
);

-- Assign SYSTEM_ADMIN role to admin user
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u, roles r
WHERE u.username = 'admin' AND r.name = 'SYSTEM_ADMIN';

-- Assign SERVICE role to service user
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u, roles r
WHERE u.username = 'service-user' AND r.name = 'SERVICE';

-- Assign USER role to test user
INSERT INTO user_roles (user_id, role_id)
SELECT u.id, r.id
FROM users u, roles r
WHERE u.username = 'testuser' AND r.name = 'USER';

-- Update statistics
ANALYZE users;
ANALYZE roles;
ANALYZE permissions;
ANALYZE user_roles;
ANALYZE role_permissions;
