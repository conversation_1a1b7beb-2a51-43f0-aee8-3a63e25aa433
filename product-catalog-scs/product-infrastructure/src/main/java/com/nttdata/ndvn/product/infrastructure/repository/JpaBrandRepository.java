package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Brand;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA repository interface for Brand entity.
 */
@Repository
public interface JpaBrandRepository extends JpaRepository<Brand, UUID> {
    
    /**
     * Find brand by slug.
     */
    Optional<Brand> findBySlug(String slug);
    
    /**
     * Find brand by name.
     */
    Optional<Brand> findByName(String name);
    
    /**
     * Find active brands.
     */
    Page<Brand> findByActive(boolean active, Pageable pageable);
    
    /**
     * Find brands by name containing (case-insensitive).
     */
    Page<Brand> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find brands by description containing (case-insensitive).
     */
    Page<Brand> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find brands created after a specific date.
     */
    Page<Brand> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find brands updated after a specific date.
     */
    Page<Brand> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find brands ordered by sort order.
     */
    List<Brand> findAllByOrderBySortOrderAsc();
    
    /**
     * Find brands ordered by name.
     */
    Page<Brand> findAllByOrderByNameAsc(Pageable pageable);
    
    /**
     * Find brands ordered by creation date.
     */
    Page<Brand> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * Search brands by name or description.
     */
    @Query("SELECT b FROM Brand b WHERE " +
           "LOWER(b.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.description) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Brand> searchBrands(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Find brands with logos.
     */
    Page<Brand> findByLogoUrlIsNotNull(Pageable pageable);
    
    /**
     * Find brands without logos.
     */
    Page<Brand> findByLogoUrlIsNull(Pageable pageable);
    
    /**
     * Find brands with websites.
     */
    Page<Brand> findByWebsiteUrlIsNotNull(Pageable pageable);
    
    /**
     * Find brands without websites.
     */
    Page<Brand> findByWebsiteUrlIsNull(Pageable pageable);
    
    /**
     * Check if slug exists.
     */
    boolean existsBySlug(String slug);
    
    /**
     * Check if name exists.
     */
    boolean existsByName(String name);
    
    /**
     * Count active brands.
     */
    long countByActive(boolean active);
    
    /**
     * Count brands with products.
     */
    @Query("SELECT COUNT(DISTINCT p.brandId) FROM Product p WHERE p.brandId IS NOT NULL")
    long countBrandsWithProducts();
    
    /**
     * Find popular brands (with most products).
     */
    @Query(value = "SELECT b.* FROM brands b " +
                   "LEFT JOIN products p ON b.id = p.brand_id " +
                   "WHERE b.active = true " +
                   "GROUP BY b.id " +
                   "ORDER BY COUNT(p.id) DESC " +
                   "LIMIT :limit", 
           nativeQuery = true)
    List<Brand> findPopularBrands(@Param("limit") int limit);
}
