package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Inventory;
import com.nttdata.ndvn.product.domain.repository.InventoryRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of InventoryRepository using JPA.
 */
@Repository
public class InventoryRepositoryImpl implements InventoryRepository {
    
    private final JpaInventoryRepository jpaInventoryRepository;
    
    public InventoryRepositoryImpl(JpaInventoryRepository jpaInventoryRepository) {
        this.jpaInventoryRepository = jpaInventoryRepository;
    }
    
    @Override
    public Inventory save(Inventory inventory) {
        return jpaInventoryRepository.save(inventory);
    }
    
    @Override
    public Optional<Inventory> findById(UUID id) {
        return jpaInventoryRepository.findById(id);
    }
    
    @Override
    public Optional<Inventory> findByProductIdAndLocationCode(UUID productId, String locationCode) {
        return jpaInventoryRepository.findByProductIdAndLocationCode(productId, locationCode);
    }
    
    @Override
    public Optional<Inventory> findByVariantIdAndLocationCode(UUID variantId, String locationCode) {
        return jpaInventoryRepository.findByVariantIdAndLocationCode(variantId, locationCode);
    }
    
    @Override
    public Page<Inventory> findAll(Pageable pageable) {
        return jpaInventoryRepository.findAll(pageable);
    }
    
    @Override
    public List<Inventory> findByProductId(UUID productId) {
        return jpaInventoryRepository.findByProductId(productId);
    }
    
    @Override
    public List<Inventory> findByVariantId(UUID variantId) {
        return jpaInventoryRepository.findByVariantId(variantId);
    }
    
    @Override
    public Page<Inventory> findByLocationCode(String locationCode, Pageable pageable) {
        return jpaInventoryRepository.findByLocationCode(locationCode, pageable);
    }
    
    @Override
    public Page<Inventory> findByActive(boolean active, Pageable pageable) {
        return jpaInventoryRepository.findByActive(active, pageable);
    }
    
    @Override
    public Page<Inventory> findByTrackInventory(boolean trackInventory, Pageable pageable) {
        return jpaInventoryRepository.findByTrackInventory(trackInventory, pageable);
    }
    
    @Override
    public Page<Inventory> findByAllowBackorder(boolean allowBackorder, Pageable pageable) {
        return jpaInventoryRepository.findByAllowBackorder(allowBackorder, pageable);
    }
    
    @Override
    public List<Inventory> findLowStockInventory() {
        return jpaInventoryRepository.findLowStockInventory();
    }
    
    @Override
    public List<Inventory> findOutOfStockInventory() {
        return jpaInventoryRepository.findOutOfStockInventory();
    }
    
    @Override
    public List<Inventory> findInventoryNeedingReorder() {
        return jpaInventoryRepository.findInventoryNeedingReorder();
    }
    
    @Override
    public Page<Inventory> findByAvailableQuantityBetween(Integer minQuantity, Integer maxQuantity, Pageable pageable) {
        return jpaInventoryRepository.findByAvailableQuantityBetween(minQuantity, maxQuantity, pageable);
    }
    
    @Override
    public Page<Inventory> findByTotalQuantityBetween(Integer minQuantity, Integer maxQuantity, Pageable pageable) {
        return jpaInventoryRepository.findByTotalQuantityBetween(minQuantity, maxQuantity, pageable);
    }
    
    @Override
    public Page<Inventory> findByReservedQuantityGreaterThan(Integer quantity, Pageable pageable) {
        return jpaInventoryRepository.findByReservedQuantityGreaterThan(quantity, pageable);
    }
    
    @Override
    public Page<Inventory> findByDamagedQuantityGreaterThan(Integer quantity, Pageable pageable) {
        return jpaInventoryRepository.findByDamagedQuantityGreaterThan(quantity, pageable);
    }
    
    @Override
    public Page<Inventory> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable) {
        return jpaInventoryRepository.findByUpdatedAtAfter(updatedAfter, pageable);
    }
    
    @Override
    public Page<Inventory> findByLastStockCheckBefore(LocalDateTime date, Pageable pageable) {
        return jpaInventoryRepository.findByLastStockCheckBefore(date, pageable);
    }
    
    @Override
    public List<Inventory> findByProductIdIn(List<UUID> productIds) {
        return jpaInventoryRepository.findByProductIdIn(productIds);
    }
    
    @Override
    public List<Inventory> findByVariantIdIn(List<UUID> variantIds) {
        return jpaInventoryRepository.findByVariantIdIn(variantIds);
    }
    
    @Override
    public Page<Inventory> findByLocationCodeIn(List<String> locationCodes, Pageable pageable) {
        return jpaInventoryRepository.findByLocationCodeIn(locationCodes, pageable);
    }
    
    @Override
    public boolean existsByProductIdAndLocationCode(UUID productId, String locationCode) {
        return jpaInventoryRepository.existsByProductIdAndLocationCode(productId, locationCode);
    }
    
    @Override
    public boolean existsByVariantIdAndLocationCode(UUID variantId, String locationCode) {
        return jpaInventoryRepository.existsByVariantIdAndLocationCode(variantId, locationCode);
    }
    
    @Override
    public boolean existsById(UUID id) {
        return jpaInventoryRepository.existsById(id);
    }
    
    @Override
    public long countByProductId(UUID productId) {
        return jpaInventoryRepository.countByProductId(productId);
    }
    
    @Override
    public long countByLocationCode(String locationCode) {
        return jpaInventoryRepository.countByLocationCode(locationCode);
    }
    
    @Override
    public long countByActive(boolean active) {
        return jpaInventoryRepository.countByActive(active);
    }
    
    @Override
    public long countLowStockInventory() {
        return jpaInventoryRepository.countLowStockInventory();
    }
    
    @Override
    public long countOutOfStockInventory() {
        return jpaInventoryRepository.countOutOfStockInventory();
    }
    
    @Override
    public Integer sumTotalQuantityByProductId(UUID productId) {
        return jpaInventoryRepository.sumTotalQuantityByProductId(productId);
    }
    
    @Override
    public Integer sumAvailableQuantityByProductId(UUID productId) {
        return jpaInventoryRepository.sumAvailableQuantityByProductId(productId);
    }
    
    @Override
    public Integer sumReservedQuantityByProductId(UUID productId) {
        return jpaInventoryRepository.sumReservedQuantityByProductId(productId);
    }
    
    @Override
    public Integer sumTotalQuantityByLocationCode(String locationCode) {
        return jpaInventoryRepository.sumTotalQuantityByLocationCode(locationCode);
    }
    
    @Override
    public Integer sumAvailableQuantityByLocationCode(String locationCode) {
        return jpaInventoryRepository.sumAvailableQuantityByLocationCode(locationCode);
    }
    
    @Override
    public List<String> findLocationsByProductId(UUID productId) {
        return jpaInventoryRepository.findLocationsByProductId(productId);
    }
    
    @Override
    public List<String> findLocationsByVariantId(UUID variantId) {
        return jpaInventoryRepository.findLocationsByVariantId(variantId);
    }
    
    @Override
    public List<String> findAllLocationCodes() {
        return jpaInventoryRepository.findAllLocationCodes();
    }
    
    @Override
    public List<Object[]> findInventorySummaryByLocation() {
        return jpaInventoryRepository.findInventorySummaryByLocation();
    }
    
    @Override
    public List<Object[]> findInventorySummaryByProduct() {
        return jpaInventoryRepository.findInventorySummaryByProduct();
    }
    
    @Override
    public void deleteById(UUID id) {
        jpaInventoryRepository.deleteById(id);
    }
    
    @Override
    public void delete(Inventory inventory) {
        jpaInventoryRepository.delete(inventory);
    }
    
    @Override
    public void deleteByProductId(UUID productId) {
        jpaInventoryRepository.deleteByProductId(productId);
    }
    
    @Override
    public void deleteByVariantId(UUID variantId) {
        jpaInventoryRepository.deleteByVariantId(variantId);
    }
    
    @Override
    public List<Inventory> saveAll(Iterable<Inventory> inventories) {
        return jpaInventoryRepository.saveAll(inventories);
    }
    
    @Override
    public List<Inventory> findAllById(Iterable<UUID> ids) {
        return jpaInventoryRepository.findAllById(ids);
    }
    
    @Override
    public long count() {
        return jpaInventoryRepository.count();
    }
}
