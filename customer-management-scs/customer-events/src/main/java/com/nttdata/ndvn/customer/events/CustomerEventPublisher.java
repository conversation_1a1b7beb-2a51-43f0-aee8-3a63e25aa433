package com.nttdata.ndvn.customer.events;

import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerAddress;
import com.nttdata.ndvn.customer.domain.model.CustomerNote;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Publisher for customer domain events.
 * 
 * This component publishes domain events to Kafka topics for consumption
 * by other services in the SCS architecture.
 */
@Component
public class CustomerEventPublisher {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomerEventPublisher.class);
    
    private static final String CUSTOMER_TOPIC = "customer-events";
    
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public CustomerEventPublisher(KafkaTemplate<String, Object> kafkaTemplate) {
        this.kafkaTemplate = kafkaTemplate;
    }
    
    /**
     * Publish customer created event.
     */
    public void publishCustomerCreated(Customer customer) {
        try {
            CustomerCreatedEvent event = CustomerCreatedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .customerType(customer.getCustomerType())
                .email(customer.getEmail())
                .displayName(customer.getDisplayName())
                .status(customer.getStatus())
                .classification(customer.getClassification())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerCreatedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerCreatedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer updated event.
     */
    public void publishCustomerUpdated(Customer customer) {
        try {
            CustomerUpdatedEvent event = CustomerUpdatedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .customerType(customer.getCustomerType())
                .email(customer.getEmail())
                .displayName(customer.getDisplayName())
                .status(customer.getStatus())
                .classification(customer.getClassification())
                .updatedAt(customer.getUpdatedAt())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerUpdatedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerUpdatedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer status changed event.
     */
    public void publishCustomerStatusChanged(Customer customer, String previousStatus) {
        try {
            CustomerStatusChangedEvent event = CustomerStatusChangedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .previousStatus(previousStatus)
                .newStatus(customer.getStatus().toString())
                .changedAt(customer.getUpdatedAt())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerStatusChangedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerStatusChangedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer deleted event.
     */
    public void publishCustomerDeleted(Customer customer) {
        try {
            CustomerDeletedEvent event = CustomerDeletedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .email(customer.getEmail())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerDeletedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerDeletedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer address added event.
     */
    public void publishCustomerAddressAdded(Customer customer, CustomerAddress address) {
        try {
            CustomerAddressAddedEvent event = CustomerAddressAddedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .addressId(address.getId())
                .addressType(address.getAddressType())
                .city(address.getCity())
                .country(address.getCountry())
                .isPrimary(address.isPrimary())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerAddressAddedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerAddressAddedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer note added event.
     */
    public void publishCustomerNoteAdded(Customer customer, CustomerNote note) {
        try {
            CustomerNoteAddedEvent event = CustomerNoteAddedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .noteId(note.getId())
                .noteType(note.getNoteType())
                .subject(note.getSubject())
                .isInternal(note.isInternal())
                .createdBy(note.getCreatedBy())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerNoteAddedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerNoteAddedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer segment assigned event.
     */
    public void publishCustomerSegmentAssigned(Customer customer, String segmentName, String assignedBy) {
        try {
            CustomerSegmentAssignedEvent event = CustomerSegmentAssignedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .segmentName(segmentName)
                .assignedBy(assignedBy)
                .assignedAt(LocalDateTime.now())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerSegmentAssignedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerSegmentAssignedEvent for customer: {}", customer.getId(), e);
        }
    }
    
    /**
     * Publish customer segment removed event.
     */
    public void publishCustomerSegmentRemoved(Customer customer, String segmentName) {
        try {
            CustomerSegmentRemovedEvent event = CustomerSegmentRemovedEvent.builder()
                .eventId(UUID.randomUUID())
                .eventTime(LocalDateTime.now())
                .customerId(customer.getId())
                .customerNumber(customer.getCustomerNumber())
                .segmentName(segmentName)
                .removedAt(LocalDateTime.now())
                .build();
            
            kafkaTemplate.send(CUSTOMER_TOPIC, customer.getId().toString(), event);
            logger.info("Published CustomerSegmentRemovedEvent for customer: {}", customer.getId());
            
        } catch (Exception e) {
            logger.error("Failed to publish CustomerSegmentRemovedEvent for customer: {}", customer.getId(), e);
        }
    }
}
