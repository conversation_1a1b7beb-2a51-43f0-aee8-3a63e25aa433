server:
  port: 8081

spring:
  application:
    name: user-management-service
  
  profiles:
    active: local
  
  # Database configuration
  datasource:
    url: ************************************************
    username: user_service
    password: password
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      idle-timeout: 300000
      connection-timeout: 20000
      leak-detection-threshold: 60000
  
  # JPA configuration
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
    open-in-view: false
  
  # Flyway migration
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true
  
  # Redis configuration
  data:
    redis:
      host: localhost
      port: 6379
      password: redis-password
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
  
  # Cache configuration
  cache:
    type: redis
    redis:
      time-to-live: 600000
      cache-null-values: false
  
  # Kafka configuration
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: io.confluent.kafka.serializers.KafkaAvroSerializer
      acks: 1
      retries: 3
      compression-type: snappy
      properties:
        schema.registry.url: http://localhost:8081
    consumer:
      group-id: ${spring.application.name}
      auto-offset-reset: earliest
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: io.confluent.kafka.serializers.KafkaAvroDeserializer
      properties:
        schema.registry.url: http://localhost:8081
        specific.avro.reader: true
  
  # Security configuration
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8090/realms/ndvn
          jwk-set-uri: http://localhost:8090/realms/ndvn/protocol/openid-connect/certs
  
  # Service discovery
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        register: true
        health-check-enabled: true
        health-check-path: /actuator/health
        health-check-interval: 30s
        instance-id: ${spring.application.name}-${server.port}
        service-name: ${spring.application.name}
        hostname: localhost
        port: ${server.port}
        prefer-ip-address: false
        tags:
          - user-management
          - scs
          - ndvn

# Management and monitoring
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      tracing:
        endpoint: http://localhost:9411/api/v2/spans

# Logging configuration
logging:
  level:
    com.nttdata.ndvn.user: DEBUG
    org.springframework.security: INFO
    org.springframework.web: INFO
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"

# Application-specific configuration
app:
  security:
    jwt:
      secret: ${JWT_SECRET:ndvn-user-management-secret-key-change-in-production}
      expiration: 86400000 # 24 hours
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digits: true
      require-special-chars: true
    session:
      max-concurrent-sessions: 5
      timeout: 1800000 # 30 minutes
  
  user:
    registration:
      enabled: true
      email-verification-required: true
      default-role: USER
    password-reset:
      token-expiration: 3600000 # 1 hour
      max-attempts: 3
  
  audit:
    enabled: true
    retention-days: 90

# API Documentation
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true
  info:
    title: User Management Service API
    description: REST API for User Management Self-Contained System
    version: 1.0.0
    contact:
      name: NDVN Development Team
      email: <EMAIL>

---
# Docker profile
spring:
  config:
    activate:
      on-profile: docker
  datasource:
    url: **********************************************
  data:
    redis:
      host: redis
  kafka:
    bootstrap-servers: kafka:9092
    producer:
      properties:
        schema.registry.url: http://schema-registry:8081
    consumer:
      properties:
        schema.registry.url: http://schema-registry:8081
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://keycloak:8080/realms/ndvn
          jwk-set-uri: http://keycloak:8080/realms/ndvn/protocol/openid-connect/certs
  cloud:
    consul:
      host: consul

management:
  tracing:
    zipkin:
      tracing:
        endpoint: http://jaeger:9411/api/v2/spans

---
# Test profile
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  jpa:
    hibernate:
      ddl-auto: create-drop
    database-platform: org.hibernate.dialect.H2Dialect
  flyway:
    enabled: false
  kafka:
    bootstrap-servers: ${spring.embedded.kafka.brokers}
  data:
    redis:
      host: localhost
      port: 6370 # Different port for tests
