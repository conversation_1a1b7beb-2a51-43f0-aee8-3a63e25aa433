package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Product;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import com.nttdata.ndvn.product.domain.repository.ProductRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of ProductRepository using JPA.
 * 
 * This class adapts the JPA repository to the domain repository interface.
 */
@Repository
public class ProductRepositoryImpl implements ProductRepository {
    
    private final JpaProductRepository jpaProductRepository;
    
    public ProductRepositoryImpl(JpaProductRepository jpaProductRepository) {
        this.jpaProductRepository = jpaProductRepository;
    }
    
    @Override
    public Product save(Product product) {
        return jpaProductRepository.save(product);
    }
    
    @Override
    public Optional<Product> findById(UUID id) {
        return jpaProductRepository.findById(id);
    }
    
    @Override
    public Optional<Product> findBySku(String sku) {
        return jpaProductRepository.findBySku(sku);
    }
    
    @Override
    public Page<Product> findAll(Pageable pageable) {
        return jpaProductRepository.findAll(pageable);
    }
    
    @Override
    public Page<Product> findByStatus(ProductStatus status, Pageable pageable) {
        return jpaProductRepository.findByStatus(status, pageable);
    }
    
    @Override
    public Page<Product> findByCategoryId(UUID categoryId, Pageable pageable) {
        return jpaProductRepository.findByCategoryId(categoryId, pageable);
    }
    
    @Override
    public Page<Product> findByBrandId(UUID brandId, Pageable pageable) {
        return jpaProductRepository.findByBrandId(brandId, pageable);
    }
    
    @Override
    public Page<Product> findByFeatured(boolean featured, Pageable pageable) {
        return jpaProductRepository.findByFeatured(featured, pageable);
    }
    
    @Override
    public Page<Product> findByBasePriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable) {
        return jpaProductRepository.findByBasePriceBetween(minPrice, maxPrice, pageable);
    }
    
    @Override
    public Page<Product> findProductsOnSale(Pageable pageable) {
        return jpaProductRepository.findProductsOnSale(pageable);
    }
    
    @Override
    public Page<Product> searchProducts(String searchTerm, Pageable pageable) {
        return jpaProductRepository.searchProducts(searchTerm, pageable);
    }
    
    @Override
    public Page<Product> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable) {
        return jpaProductRepository.findByCreatedAtAfter(createdAfter, pageable);
    }
    
    @Override
    public Page<Product> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable) {
        return jpaProductRepository.findByUpdatedAtAfter(updatedAfter, pageable);
    }
    
    @Override
    public Page<Product> findByCategoryIdIn(List<UUID> categoryIds, Pageable pageable) {
        return jpaProductRepository.findByCategoryIdIn(categoryIds, pageable);
    }
    
    @Override
    public Page<Product> findByBrandIdIn(List<UUID> brandIds, Pageable pageable) {
        return jpaProductRepository.findByBrandIdIn(brandIds, pageable);
    }
    
    @Override
    public Page<Product> findByStatusIn(List<ProductStatus> statuses, Pageable pageable) {
        return jpaProductRepository.findByStatusIn(statuses, pageable);
    }
    
    @Override
    public Page<Product> findByDigital(boolean digital, Pageable pageable) {
        return jpaProductRepository.findByDigital(digital, pageable);
    }
    
    @Override
    public Page<Product> findByRequiresShipping(boolean requiresShipping, Pageable pageable) {
        return jpaProductRepository.findByRequiresShipping(requiresShipping, pageable);
    }
    
    @Override
    public Page<Product> findByTaxable(boolean taxable, Pageable pageable) {
        return jpaProductRepository.findByTaxable(taxable, pageable);
    }
    
    @Override
    public boolean existsBySku(String sku) {
        return jpaProductRepository.existsBySku(sku);
    }
    
    @Override
    public boolean existsById(UUID id) {
        return jpaProductRepository.existsById(id);
    }
    
    @Override
    public long countByStatus(ProductStatus status) {
        return jpaProductRepository.countByStatus(status);
    }
    
    @Override
    public long countByCategoryId(UUID categoryId) {
        return jpaProductRepository.countByCategoryId(categoryId);
    }
    
    @Override
    public long countByBrandId(UUID brandId) {
        return jpaProductRepository.countByBrandId(brandId);
    }
    
    @Override
    public long countByFeatured(boolean featured) {
        return jpaProductRepository.countByFeatured(featured);
    }
    
    @Override
    public long countByCreatedAtAfter(LocalDateTime createdAfter) {
        return jpaProductRepository.countByCreatedAtAfter(createdAfter);
    }
    
    @Override
    public List<Product> findProductsWithLowInventory() {
        return jpaProductRepository.findProductsWithLowInventory();
    }
    
    @Override
    public Page<Product> findProductsWithoutImages(Pageable pageable) {
        return jpaProductRepository.findProductsWithoutImages(pageable);
    }
    
    @Override
    public Page<Product> findProductsWithoutVariants(Pageable pageable) {
        return jpaProductRepository.findProductsWithoutVariants(pageable);
    }
    
    @Override
    public Page<Product> findByNameContainingIgnoreCase(String name, Pageable pageable) {
        return jpaProductRepository.findByNameContainingIgnoreCase(name, pageable);
    }
    
    @Override
    public Page<Product> findByDescriptionContainingIgnoreCase(String description, Pageable pageable) {
        return jpaProductRepository.findByDescriptionContainingIgnoreCase(description, pageable);
    }
    
    @Override
    public Page<Product> findByMetaKeywordsContaining(String keywords, Pageable pageable) {
        return jpaProductRepository.findByMetaKeywordsContaining(keywords, pageable);
    }
    
    @Override
    public Page<Product> findAllOrderByCreatedAtDesc(Pageable pageable) {
        return jpaProductRepository.findAllByOrderByCreatedAtDesc(pageable);
    }
    
    @Override
    public Page<Product> findAllOrderByUpdatedAtDesc(Pageable pageable) {
        return jpaProductRepository.findAllByOrderByUpdatedAtDesc(pageable);
    }
    
    @Override
    public Page<Product> findAllOrderByNameAsc(Pageable pageable) {
        return jpaProductRepository.findAllByOrderByNameAsc(pageable);
    }
    
    @Override
    public Page<Product> findAllOrderByBasePriceAsc(Pageable pageable) {
        return jpaProductRepository.findAllByOrderByBasePriceAsc(pageable);
    }
    
    @Override
    public Page<Product> findAllOrderBySortOrderAsc(Pageable pageable) {
        return jpaProductRepository.findAllByOrderBySortOrderAsc(pageable);
    }
    
    @Override
    public Page<Product> findProductsByCriteria(String searchTerm, List<UUID> categoryIds, List<UUID> brandIds,
                                               List<ProductStatus> statuses, BigDecimal minPrice, BigDecimal maxPrice,
                                               Boolean featured, Boolean digital, Boolean onSale, Pageable pageable) {
        return jpaProductRepository.findProductsByCriteria(searchTerm, categoryIds, brandIds, statuses,
                minPrice, maxPrice, featured, digital, onSale, pageable);
    }
    
    @Override
    public void deleteById(UUID id) {
        jpaProductRepository.deleteById(id);
    }
    
    @Override
    public void delete(Product product) {
        jpaProductRepository.delete(product);
    }
    
    @Override
    public List<Product> findAllById(Iterable<UUID> ids) {
        return jpaProductRepository.findAllById(ids);
    }
    
    @Override
    public List<Product> saveAll(Iterable<Product> products) {
        return jpaProductRepository.saveAll(products);
    }
    
    @Override
    public long count() {
        return jpaProductRepository.count();
    }
    
    @Override
    public List<Product> findRandomProducts(int limit) {
        return jpaProductRepository.findRandomProducts(limit);
    }
    
    @Override
    public List<Product> findRelatedProductsByCategory(UUID categoryId, UUID excludeProductId, int limit) {
        return jpaProductRepository.findRelatedProductsByCategory(categoryId, excludeProductId, 
                PageRequest.of(0, limit));
    }
    
    @Override
    public List<Product> findRelatedProductsByBrand(UUID brandId, UUID excludeProductId, int limit) {
        return jpaProductRepository.findRelatedProductsByBrand(brandId, excludeProductId, 
                PageRequest.of(0, limit));
    }
    
    @Override
    public List<Product> findBestSellingProducts(int limit) {
        return jpaProductRepository.findBestSellingProducts(limit);
    }
    
    @Override
    public List<Product> findNewestProducts(int limit) {
        return jpaProductRepository.findNewestProducts(PageRequest.of(0, limit));
    }
}
