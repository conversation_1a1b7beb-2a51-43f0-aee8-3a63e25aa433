package com.nttdata.ndvn.customer.infrastructure.repository;

import com.nttdata.ndvn.customer.domain.model.CustomerSegment;
import com.nttdata.ndvn.customer.domain.repository.CustomerSegmentRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA implementation of CustomerSegmentRepository.
 */
@Repository
public interface JpaCustomerSegmentRepository extends JpaRepository<CustomerSegment, UUID>, CustomerSegmentRepository {
    
    @Override
    Optional<CustomerSegment> findByName(String name);
    
    @Override
    Page<CustomerSegment> findByIsActive(boolean isActive, Pageable pageable);
    
    @Override
    Page<CustomerSegment> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    @Override
    boolean existsByName(String name);
    
    @Override
    long countByIsActive(boolean isActive);
    
    @Override
    @Query("SELECT s FROM CustomerSegment s WHERE s.isActive = true ORDER BY s.name")
    List<CustomerSegment> findAllActive();
    
    // Additional JPA-specific queries
    
    /**
     * Find segments with customer count.
     */
    @Query("SELECT s, COUNT(sa) FROM CustomerSegment s LEFT JOIN s.assignments sa GROUP BY s")
    List<Object[]> findSegmentsWithCustomerCount();
    
    /**
     * Find segments by description containing.
     */
    Page<CustomerSegment> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
}
