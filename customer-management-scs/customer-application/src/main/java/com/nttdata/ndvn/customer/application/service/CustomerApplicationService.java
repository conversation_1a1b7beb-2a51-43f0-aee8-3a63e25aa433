package com.nttdata.ndvn.customer.application.service;

import com.nttdata.ndvn.customer.application.dto.CustomerDto;
import com.nttdata.ndvn.customer.application.mapper.CustomerMapper;
import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import com.nttdata.ndvn.customer.domain.repository.CustomerRepository;
import com.nttdata.ndvn.customer.domain.service.CustomerDomainService;
import com.nttdata.ndvn.customer.infrastructure.config.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.UUID;

/**
 * Application service for customer management operations.
 * 
 * This service orchestrates customer-related use cases and coordinates between
 * the domain layer and external interfaces.
 */
@Service
@Transactional
public class CustomerApplicationService {
    
    private final CustomerRepository customerRepository;
    private final CustomerDomainService customerDomainService;
    private final CustomerMapper customerMapper;
    
    public CustomerApplicationService(CustomerRepository customerRepository,
                                    CustomerDomainService customerDomainService,
                                    CustomerMapper customerMapper) {
        this.customerRepository = customerRepository;
        this.customerDomainService = customerDomainService;
        this.customerMapper = customerMapper;
    }
    
    /**
     * Create a new customer.
     */
    @CacheEvict(value = {CacheConfig.CUSTOMER_SEARCH_CACHE}, allEntries = true)
    public CustomerDto createCustomer(CustomerDto customerDto) {
        // Generate customer number if not provided
        if (customerDto.getCustomerNumber() == null || customerDto.getCustomerNumber().trim().isEmpty()) {
            customerDto.setCustomerNumber(customerDomainService.generateCustomerNumber());
        }
        
        Customer customer = customerMapper.toEntity(customerDto);
        
        // Validate customer data
        customerDomainService.validateCustomerForCreation(customer);
        
        // Determine classification if not provided
        if (customer.getClassification() == null || customer.getClassification().trim().isEmpty()) {
            customer.updateClassification(customerDomainService.determineCustomerClassification(customer));
        }
        
        Customer savedCustomer = customerRepository.save(customer);
        return customerMapper.toDto(savedCustomer);
    }
    
    /**
     * Update an existing customer.
     */
    @CacheEvict(value = {
        CacheConfig.CUSTOMER_CACHE,
        CacheConfig.CUSTOMER_BY_EMAIL_CACHE,
        CacheConfig.CUSTOMER_BY_NUMBER_CACHE,
        CacheConfig.CUSTOMER_SEARCH_CACHE
    }, allEntries = true)
    public CustomerDto updateCustomer(UUID customerId, CustomerDto customerDto) {
        Customer existingCustomer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("Customer not found: " + customerId));
        
        customerMapper.updateEntityFromDto(customerDto, existingCustomer);
        
        Customer savedCustomer = customerRepository.save(existingCustomer);
        return customerMapper.toDto(savedCustomer);
    }
    
    /**
     * Get customer by ID.
     */
    @Cacheable(value = CacheConfig.CUSTOMER_CACHE, key = "#customerId")
    @Transactional(readOnly = true)
    public Optional<CustomerDto> getCustomerById(UUID customerId) {
        return customerRepository.findById(customerId)
            .map(customerMapper::toDto);
    }
    
    /**
     * Get customer by customer number.
     */
    @Cacheable(value = CacheConfig.CUSTOMER_BY_NUMBER_CACHE, key = "#customerNumber")
    @Transactional(readOnly = true)
    public Optional<CustomerDto> getCustomerByNumber(String customerNumber) {
        return customerRepository.findByCustomerNumber(customerNumber)
            .map(customerMapper::toDto);
    }
    
    /**
     * Get customer by email.
     */
    @Cacheable(value = CacheConfig.CUSTOMER_BY_EMAIL_CACHE, key = "#email")
    @Transactional(readOnly = true)
    public Optional<CustomerDto> getCustomerByEmail(String email) {
        return customerRepository.findByEmail(email)
            .map(customerMapper::toDto);
    }
    
    /**
     * Get all customers with pagination.
     */
    @Transactional(readOnly = true)
    public Page<CustomerDto> getAllCustomers(Pageable pageable) {
        return customerRepository.findAll(pageable)
            .map(customerMapper::toDto);
    }
    
    /**
     * Search customers.
     */
    @Cacheable(value = CacheConfig.CUSTOMER_SEARCH_CACHE, key = "#searchTerm + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    @Transactional(readOnly = true)
    public Page<CustomerDto> searchCustomers(String searchTerm, Pageable pageable) {
        return customerRepository.searchCustomers(searchTerm, pageable)
            .map(customerMapper::toDto);
    }
    
    /**
     * Get customers by status.
     */
    @Transactional(readOnly = true)
    public Page<CustomerDto> getCustomersByStatus(CustomerStatus status, Pageable pageable) {
        return customerRepository.findByStatus(status, pageable)
            .map(customerMapper::toDto);
    }
    
    /**
     * Get customers by type.
     */
    @Transactional(readOnly = true)
    public Page<CustomerDto> getCustomersByType(CustomerType customerType, Pageable pageable) {
        return customerRepository.findByCustomerType(customerType, pageable)
            .map(customerMapper::toDto);
    }
    
    /**
     * Update customer status.
     */
    @CacheEvict(value = {
        CacheConfig.CUSTOMER_CACHE,
        CacheConfig.CUSTOMER_BY_EMAIL_CACHE,
        CacheConfig.CUSTOMER_BY_NUMBER_CACHE,
        CacheConfig.CUSTOMER_SEARCH_CACHE
    }, allEntries = true)
    public CustomerDto updateCustomerStatus(UUID customerId, CustomerStatus newStatus) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("Customer not found: " + customerId));
        
        customer.changeStatus(newStatus);
        Customer savedCustomer = customerRepository.save(customer);
        return customerMapper.toDto(savedCustomer);
    }
    
    /**
     * Update customer credit limit.
     */
    @CacheEvict(value = {
        CacheConfig.CUSTOMER_CACHE,
        CacheConfig.CUSTOMER_SEARCH_CACHE
    }, allEntries = true)
    public CustomerDto updateCreditLimit(UUID customerId, BigDecimal creditLimit) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("Customer not found: " + customerId));
        
        customer.updateCreditLimit(creditLimit);
        
        // Update classification based on new credit limit
        String newClassification = customerDomainService.determineCustomerClassification(customer);
        customer.updateClassification(newClassification);
        
        Customer savedCustomer = customerRepository.save(customer);
        return customerMapper.toDto(savedCustomer);
    }
    
    /**
     * Assign customer to segment.
     */
    @CacheEvict(value = {
        CacheConfig.CUSTOMER_CACHE,
        CacheConfig.CUSTOMER_SEARCH_CACHE
    }, allEntries = true)
    public CustomerDto assignToSegment(UUID customerId, String segmentName, String assignedBy) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("Customer not found: " + customerId));
        
        customerDomainService.assignCustomerToSegment(customer, segmentName, assignedBy);
        Customer savedCustomer = customerRepository.save(customer);
        return customerMapper.toDto(savedCustomer);
    }
    
    /**
     * Remove customer from segment.
     */
    @CacheEvict(value = {
        CacheConfig.CUSTOMER_CACHE,
        CacheConfig.CUSTOMER_SEARCH_CACHE
    }, allEntries = true)
    public CustomerDto removeFromSegment(UUID customerId, String segmentName) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("Customer not found: " + customerId));
        
        customerDomainService.removeCustomerFromSegment(customer, segmentName);
        Customer savedCustomer = customerRepository.save(customer);
        return customerMapper.toDto(savedCustomer);
    }
    
    /**
     * Delete customer.
     */
    @CacheEvict(value = {
        CacheConfig.CUSTOMER_CACHE,
        CacheConfig.CUSTOMER_BY_EMAIL_CACHE,
        CacheConfig.CUSTOMER_BY_NUMBER_CACHE,
        CacheConfig.CUSTOMER_SEARCH_CACHE
    }, allEntries = true)
    public void deleteCustomer(UUID customerId) {
        Customer customer = customerRepository.findById(customerId)
            .orElseThrow(() -> new IllegalArgumentException("Customer not found: " + customerId));
        
        customerRepository.delete(customer);
    }
    
    /**
     * Check if email exists.
     */
    @Transactional(readOnly = true)
    public boolean emailExists(String email) {
        return customerRepository.existsByEmail(email);
    }
    
    /**
     * Check if customer number exists.
     */
    @Transactional(readOnly = true)
    public boolean customerNumberExists(String customerNumber) {
        return customerRepository.existsByCustomerNumber(customerNumber);
    }
    
    /**
     * Get customer statistics.
     */
    @Transactional(readOnly = true)
    public CustomerStatsDto getCustomerStatistics() {
        long totalCustomers = customerRepository.countByStatus(CustomerStatus.ACTIVE);
        long businessCustomers = customerRepository.countByCustomerType(CustomerType.BUSINESS);
        long individualCustomers = customerRepository.countByCustomerType(CustomerType.INDIVIDUAL);
        
        return new CustomerStatsDto(totalCustomers, businessCustomers, individualCustomers);
    }
    
    // Inner class for statistics
    public static class CustomerStatsDto {
        private final long totalCustomers;
        private final long businessCustomers;
        private final long individualCustomers;
        
        public CustomerStatsDto(long totalCustomers, long businessCustomers, long individualCustomers) {
            this.totalCustomers = totalCustomers;
            this.businessCustomers = businessCustomers;
            this.individualCustomers = individualCustomers;
        }
        
        public long getTotalCustomers() { return totalCustomers; }
        public long getBusinessCustomers() { return businessCustomers; }
        public long getIndividualCustomers() { return individualCustomers; }
    }
}
