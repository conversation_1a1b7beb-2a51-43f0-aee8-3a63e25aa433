plugins {
    id 'java-library'
}

description = 'Product Catalog Application Layer - Use cases and application services'

dependencies {
    api project(':product-domain')
    api project(':product-infrastructure')

    // Application services
    api 'org.springframework:spring-tx'
    api 'org.springframework.boot:spring-boot-starter-validation'
    api 'org.springframework.data:spring-data-commons'

    // Mapping
    api "org.mapstruct:mapstruct:${mapstructVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

    // JSON processing
    api 'com.fasterxml.jackson.core:jackson-databind'
    api 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
}
