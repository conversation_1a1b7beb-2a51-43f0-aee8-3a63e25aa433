-- Product Catalog SCS Database Schema
-- Version 1.0 - Initial schema creation

-- Create brands table
CREATE TABLE brands (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    logo_url VARCHAR(500),
    website_url VARCHAR(500),
    active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    meta_title VARCHAR(500),
    meta_description TEXT,
    meta_keywords VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create categories table
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    active BOOLEAN NOT NULL DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    level INTEGER DEFAULT 0,
    path VARCHAR(1000),
    image_url VARCHAR(500),
    icon VARCHAR(500),
    meta_title VARCHAR(500),
    meta_description TEXT,
    meta_keywords VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    sku VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    long_description TEXT,
    category_id UUID NOT NULL REFERENCES categories(id),
    brand_id UUID REFERENCES brands(id),
    base_price DECIMAL(12,2) NOT NULL,
    sale_price DECIMAL(12,2),
    status VARCHAR(20) NOT NULL DEFAULT 'DRAFT',
    weight DECIMAL(11,3),
    weight_unit VARCHAR(50) DEFAULT 'kg',
    length DECIMAL(10,2),
    width DECIMAL(10,2),
    height DECIMAL(10,2),
    dimension_unit VARCHAR(50) DEFAULT 'cm',
    is_featured BOOLEAN DEFAULT false,
    is_digital BOOLEAN DEFAULT false,
    requires_shipping BOOLEAN DEFAULT true,
    is_taxable BOOLEAN DEFAULT true,
    meta_title VARCHAR(500),
    meta_description TEXT,
    meta_keywords VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create product_variants table
CREATE TABLE product_variants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    sku VARCHAR(100) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price_adjustment DECIMAL(12,2) DEFAULT 0.00,
    weight DECIMAL(11,3),
    length DECIMAL(10,2),
    width DECIMAL(10,2),
    height DECIMAL(10,2),
    color VARCHAR(100),
    size VARCHAR(100),
    material VARCHAR(100),
    style VARCHAR(100),
    active BOOLEAN NOT NULL DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    image_url VARCHAR(500),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create product_attributes table
CREATE TABLE product_attributes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    attribute_name VARCHAR(255) NOT NULL,
    attribute_type VARCHAR(50) NOT NULL,
    attribute_value TEXT NOT NULL,
    unit VARCHAR(100),
    is_filterable BOOLEAN DEFAULT false,
    is_searchable BOOLEAN DEFAULT false,
    is_visible BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create product_images table
CREATE TABLE product_images (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    image_url VARCHAR(1000) NOT NULL,
    thumbnail_url VARCHAR(1000),
    alt_text VARCHAR(255) NOT NULL,
    caption VARCHAR(500),
    image_type VARCHAR(50) NOT NULL DEFAULT 'GALLERY',
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    width INTEGER,
    height INTEGER,
    file_size BIGINT,
    file_format VARCHAR(50),
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create inventory table
CREATE TABLE inventory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES product_variants(id) ON DELETE CASCADE,
    location_code VARCHAR(50) NOT NULL,
    location_name VARCHAR(255) NOT NULL,
    total_quantity INTEGER NOT NULL DEFAULT 0,
    available_quantity INTEGER NOT NULL DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,
    damaged_quantity INTEGER DEFAULT 0,
    minimum_stock_level INTEGER DEFAULT 0,
    maximum_stock_level INTEGER DEFAULT 1000,
    reorder_point INTEGER DEFAULT 10,
    reorder_quantity INTEGER DEFAULT 50,
    track_inventory BOOLEAN DEFAULT true,
    allow_backorder BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    last_stock_check TIMESTAMP,
    notes TEXT,
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(product_id, variant_id, location_code)
);

-- Create indexes for brands
CREATE INDEX idx_brand_name ON brands(name);
CREATE INDEX idx_brand_slug ON brands(slug);
CREATE INDEX idx_brand_active ON brands(active);

-- Create indexes for categories
CREATE INDEX idx_category_name ON categories(name);
CREATE INDEX idx_category_slug ON categories(slug);
CREATE INDEX idx_category_parent ON categories(parent_id);
CREATE INDEX idx_category_active ON categories(active);
CREATE INDEX idx_category_sort_order ON categories(sort_order);
CREATE INDEX idx_category_level ON categories(level);
CREATE INDEX idx_category_path ON categories(path);

-- Create indexes for products
CREATE INDEX idx_product_sku ON products(sku);
CREATE INDEX idx_product_name ON products(name);
CREATE INDEX idx_product_category ON products(category_id);
CREATE INDEX idx_product_brand ON products(brand_id);
CREATE INDEX idx_product_status ON products(status);
CREATE INDEX idx_product_created_at ON products(created_at);
CREATE INDEX idx_product_featured ON products(is_featured);
CREATE INDEX idx_product_base_price ON products(base_price);
CREATE INDEX idx_product_sale_price ON products(sale_price);

-- Create indexes for product_variants
CREATE INDEX idx_variant_sku ON product_variants(sku);
CREATE INDEX idx_variant_product ON product_variants(product_id);
CREATE INDEX idx_variant_active ON product_variants(active);
CREATE INDEX idx_variant_default ON product_variants(is_default);

-- Create indexes for product_attributes
CREATE INDEX idx_attribute_product ON product_attributes(product_id);
CREATE INDEX idx_attribute_name ON product_attributes(attribute_name);
CREATE INDEX idx_attribute_type ON product_attributes(attribute_type);
CREATE INDEX idx_attribute_filterable ON product_attributes(is_filterable);
CREATE INDEX idx_attribute_searchable ON product_attributes(is_searchable);

-- Create indexes for product_images
CREATE INDEX idx_image_product ON product_images(product_id);
CREATE INDEX idx_image_type ON product_images(image_type);
CREATE INDEX idx_image_primary ON product_images(is_primary);
CREATE INDEX idx_image_sort_order ON product_images(sort_order);

-- Create indexes for inventory
CREATE INDEX idx_inventory_product ON inventory(product_id);
CREATE INDEX idx_inventory_variant ON inventory(variant_id);
CREATE INDEX idx_inventory_location ON inventory(location_code);
CREATE INDEX idx_inventory_available ON inventory(available_quantity);
CREATE INDEX idx_inventory_active ON inventory(is_active);

-- Create constraints
ALTER TABLE products ADD CONSTRAINT chk_product_status 
    CHECK (status IN ('DRAFT', 'ACTIVE', 'INACTIVE', 'DISCONTINUED', 'ARCHIVED'));

ALTER TABLE products ADD CONSTRAINT chk_product_base_price_positive 
    CHECK (base_price > 0);

ALTER TABLE products ADD CONSTRAINT chk_product_sale_price_non_negative 
    CHECK (sale_price IS NULL OR sale_price >= 0);

ALTER TABLE product_attributes ADD CONSTRAINT chk_attribute_type 
    CHECK (attribute_type IN ('TEXT', 'NUMBER', 'BOOLEAN', 'DATE', 'URL', 'EMAIL', 'COLOR', 'DIMENSION', 'WEIGHT', 'PERCENTAGE', 'CURRENCY'));

ALTER TABLE product_images ADD CONSTRAINT chk_image_type 
    CHECK (image_type IN ('GALLERY', 'THUMBNAIL', 'HERO', 'DETAIL', 'VARIANT', 'LIFESTYLE', 'TECHNICAL'));

ALTER TABLE inventory ADD CONSTRAINT chk_inventory_quantities_non_negative 
    CHECK (total_quantity >= 0 AND available_quantity >= 0 AND reserved_quantity >= 0 AND damaged_quantity >= 0);

ALTER TABLE inventory ADD CONSTRAINT chk_inventory_stock_levels 
    CHECK (minimum_stock_level >= 0 AND maximum_stock_level >= minimum_stock_level AND reorder_point >= 0 AND reorder_quantity >= 0);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_brands_updated_at BEFORE UPDATE ON brands
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_variants_updated_at BEFORE UPDATE ON product_variants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_attributes_updated_at BEFORE UPDATE ON product_attributes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_images_updated_at BEFORE UPDATE ON product_images
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_inventory_updated_at BEFORE UPDATE ON inventory
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
