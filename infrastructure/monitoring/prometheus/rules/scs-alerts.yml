# Alerting rules for NDVN SCS Platform
groups:
  - name: scs-platform-alerts
    rules:
      # Service availability alerts
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service {{ $labels.job }} is down"
          description: "Service {{ $labels.job }} on instance {{ $labels.instance }} has been down for more than 1 minute."

      # High error rate alerts
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected for {{ $labels.job }}"
          description: "Error rate for {{ $labels.job }} is {{ $value }} errors per second."

      # High response time alerts
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High response time for {{ $labels.job }}"
          description: "95th percentile response time for {{ $labels.job }} is {{ $value }} seconds."

      # Memory usage alerts
      - alert: HighMemoryUsage
        expr: (container_memory_usage_bytes / container_spec_memory_limit_bytes) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage for {{ $labels.name }}"
          description: "Memory usage for container {{ $labels.name }} is {{ $value }}%."

      # CPU usage alerts
      - alert: HighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage for {{ $labels.name }}"
          description: "CPU usage for container {{ $labels.name }} is {{ $value }}%."

      # Disk space alerts
      - alert: LowDiskSpace
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) * 100 < 10
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "Low disk space on {{ $labels.instance }}"
          description: "Disk space on {{ $labels.instance }} is {{ $value }}% full."

      # Database connection alerts
      - alert: DatabaseConnectionPoolExhausted
        expr: hikaricp_connections_active / hikaricp_connections_max > 0.9
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "Database connection pool nearly exhausted for {{ $labels.job }}"
          description: "Connection pool usage for {{ $labels.job }} is {{ $value }}."

      # Kafka consumer lag alerts
      - alert: KafkaConsumerLag
        expr: kafka_consumer_lag_sum > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High Kafka consumer lag for {{ $labels.consumer_group }}"
          description: "Consumer lag for group {{ $labels.consumer_group }} is {{ $value }} messages."

      # JVM heap usage alerts
      - alert: HighJVMHeapUsage
        expr: (jvm_memory_used_bytes{area="heap"} / jvm_memory_max_bytes{area="heap"}) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High JVM heap usage for {{ $labels.job }}"
          description: "JVM heap usage for {{ $labels.job }} is {{ $value }}%."

      # Circuit breaker alerts
      - alert: CircuitBreakerOpen
        expr: resilience4j_circuitbreaker_state{state="open"} == 1
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "Circuit breaker open for {{ $labels.name }}"
          description: "Circuit breaker {{ $labels.name }} in {{ $labels.job }} is open."

  - name: infrastructure-alerts
    rules:
      # Elasticsearch cluster health
      - alert: ElasticsearchClusterRed
        expr: elasticsearch_cluster_health_status{color="red"} == 1
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Elasticsearch cluster status is red"
          description: "Elasticsearch cluster health is red, indicating a critical issue."

      # Elasticsearch cluster yellow
      - alert: ElasticsearchClusterYellow
        expr: elasticsearch_cluster_health_status{color="yellow"} == 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Elasticsearch cluster status is yellow"
          description: "Elasticsearch cluster health is yellow for more than 5 minutes."

      # Redis connection alerts
      - alert: RedisDown
        expr: redis_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Redis is down"
          description: "Redis instance is not responding."

      # Keycloak alerts
      - alert: KeycloakDown
        expr: up{job="keycloak"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Keycloak is down"
          description: "Keycloak authentication server is not responding."

      # API Gateway alerts
      - alert: APIGatewayDown
        expr: up{job="api-gateway"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "API Gateway is down"
          description: "API Gateway is not responding, affecting all service access."

      # Consul alerts
      - alert: ConsulDown
        expr: consul_up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Consul is down"
          description: "Consul service discovery is not responding."

  - name: business-alerts
    rules:
      # Order processing alerts
      - alert: OrderProcessingFailure
        expr: rate(order_processing_failures_total[5m]) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High order processing failure rate"
          description: "Order processing failure rate is {{ $value }} failures per second."

      # User authentication alerts
      - alert: AuthenticationFailures
        expr: rate(authentication_failures_total[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High authentication failure rate"
          description: "Authentication failure rate is {{ $value }} failures per second."

      # Notification delivery alerts
      - alert: NotificationDeliveryFailure
        expr: rate(notification_delivery_failures_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High notification delivery failure rate"
          description: "Notification delivery failure rate is {{ $value }} failures per second."
