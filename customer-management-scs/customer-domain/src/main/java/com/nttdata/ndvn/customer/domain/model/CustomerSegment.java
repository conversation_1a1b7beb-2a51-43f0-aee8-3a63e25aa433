package com.nttdata.ndvn.customer.domain.model;

import jakarta.persistence.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * Customer segment entity for customer classification and segmentation.
 */
@Entity
@Table(name = "customer_segments", indexes = {
    @Index(name = "idx_customer_segment_name", columnList = "name", unique = true),
    @Index(name = "idx_customer_segment_active", columnList = "isActive")
})
public class CustomerSegment {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @Column(name = "name", unique = true, nullable = false, length = 100)
    private String name;
    
    @Column(name = "description", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "criteria", columnDefinition = "JSONB")
    private String criteria; // JSON string containing segmentation criteria
    
    @Column(name = "is_active")
    private boolean isActive = true;
    
    @OneToMany(mappedBy = "segment", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<CustomerSegmentAssignment> assignments = new ArrayList<>();
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Constructors
    protected CustomerSegment() {
        // JPA constructor
    }
    
    private CustomerSegment(Builder builder) {
        this.name = builder.name;
        this.description = builder.description;
        this.criteria = builder.criteria;
        this.isActive = builder.isActive;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    public static Builder builder() {
        return new Builder();
    }
    
    // Business methods
    public void updateDescription(String description) {
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void updateCriteria(String criteria) {
        this.criteria = criteria;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void activate() {
        this.isActive = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void deactivate() {
        this.isActive = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Builder pattern
    public static class Builder {
        private String name;
        private String description;
        private String criteria;
        private boolean isActive = true;
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder description(String description) {
            this.description = description;
            return this;
        }
        
        public Builder criteria(String criteria) {
            this.criteria = criteria;
            return this;
        }
        
        public Builder isActive(boolean isActive) {
            this.isActive = isActive;
            return this;
        }
        
        public CustomerSegment build() {
            Objects.requireNonNull(name, "Name is required");
            return new CustomerSegment(this);
        }
    }
    
    // Getters and setters
    public UUID getId() { return id; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public String getCriteria() { return criteria; }
    public boolean isActive() { return isActive; }
    public List<CustomerSegmentAssignment> getAssignments() { return assignments; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CustomerSegment that = (CustomerSegment) o;
        return Objects.equals(id, that.id);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id);
    }
    
    @Override
    public String toString() {
        return "CustomerSegment{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", isActive=" + isActive +
                '}';
    }
}
