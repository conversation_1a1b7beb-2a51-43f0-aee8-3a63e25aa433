package com.nttdata.ndvn.product.application.dto;

import com.nttdata.ndvn.product.domain.model.ProductImage;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Data Transfer Object for ProductImage.
 */
public class ProductImageDto {
    
    private UUID id;
    private UUID productId;
    
    @NotBlank(message = "Image URL is required")
    @Size(max = 1000, message = "Image URL must not exceed 1000 characters")
    private String imageUrl;
    
    @Size(max = 1000, message = "Thumbnail URL must not exceed 1000 characters")
    private String thumbnailUrl;
    
    @NotBlank(message = "Alt text is required")
    @Size(max = 255, message = "Alt text must not exceed 255 characters")
    private String altText;
    
    @Size(max = 500, message = "Caption must not exceed 500 characters")
    private String caption;
    
    @NotNull(message = "Image type is required")
    private ProductImage.ImageType imageType;
    
    private boolean primary;
    private boolean active;
    private Integer sortOrder;
    private Integer width;
    private Integer height;
    private Long fileSize;
    private String fileFormat;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Computed fields
    private Double aspectRatio;
    private boolean landscape;
    private boolean portrait;
    private boolean square;
    
    // Constructors
    public ProductImageDto() {}
    
    // Getters and setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }
    
    public UUID getProductId() { return productId; }
    public void setProductId(UUID productId) { this.productId = productId; }
    
    public String getImageUrl() { return imageUrl; }
    public void setImageUrl(String imageUrl) { this.imageUrl = imageUrl; }
    
    public String getThumbnailUrl() { return thumbnailUrl; }
    public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
    
    public String getAltText() { return altText; }
    public void setAltText(String altText) { this.altText = altText; }
    
    public String getCaption() { return caption; }
    public void setCaption(String caption) { this.caption = caption; }
    
    public ProductImage.ImageType getImageType() { return imageType; }
    public void setImageType(ProductImage.ImageType imageType) { this.imageType = imageType; }
    
    public boolean isPrimary() { return primary; }
    public void setPrimary(boolean primary) { this.primary = primary; }
    
    public boolean isActive() { return active; }
    public void setActive(boolean active) { this.active = active; }
    
    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    
    public Integer getWidth() { return width; }
    public void setWidth(Integer width) { this.width = width; }
    
    public Integer getHeight() { return height; }
    public void setHeight(Integer height) { this.height = height; }
    
    public Long getFileSize() { return fileSize; }
    public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
    
    public String getFileFormat() { return fileFormat; }
    public void setFileFormat(String fileFormat) { this.fileFormat = fileFormat; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public Double getAspectRatio() { return aspectRatio; }
    public void setAspectRatio(Double aspectRatio) { this.aspectRatio = aspectRatio; }
    
    public boolean isLandscape() { return landscape; }
    public void setLandscape(boolean landscape) { this.landscape = landscape; }
    
    public boolean isPortrait() { return portrait; }
    public void setPortrait(boolean portrait) { this.portrait = portrait; }
    
    public boolean isSquare() { return square; }
    public void setSquare(boolean square) { this.square = square; }
}
