#!/bin/bash

# NDVN SCS Authentication Setup Script
# This script sets up Keycloak OAuth 2.0 Authorization Server and related services

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
AUTH_DIR="$(dirname "$SCRIPT_DIR")"
INFRASTRUCTURE_DIR="$(dirname "$AUTH_DIR")"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    log_info "Checking Docker status..."
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if Docker Compose is available
check_docker_compose() {
    log_info "Checking Docker Compose availability..."
    if ! command -v docker-compose > /dev/null 2>&1; then
        log_error "Docker Compose is not installed. Please install Docker Compose and try again."
        exit 1
    fi
    log_success "Docker Compose is available"
}

# Check if network exists
check_network() {
    log_info "Checking Docker network..."
    if ! docker network ls | grep -q "ndvn-scs-network"; then
        log_info "Creating Docker network: ndvn-scs-network"
        docker network create ndvn-scs-network
    fi
    log_success "Docker network is ready"
}

# Build Token Service image
build_token_service() {
    log_info "Building Token Service Docker image..."
    
    cd "$AUTH_DIR/token-service"
    
    # Build the Spring Boot application
    if command -v ./gradlew > /dev/null 2>&1; then
        ./gradlew clean build -x test
    elif command -v gradle > /dev/null 2>&1; then
        gradle clean build -x test
    else
        log_error "Gradle not found. Please install Gradle or use the Gradle wrapper."
        exit 1
    fi
    
    # Build Docker image
    docker build -t ndvn/token-service:latest .
    
    log_success "Token Service image built successfully"
}

# Start Authentication infrastructure
start_auth() {
    log_info "Starting Authentication infrastructure..."
    
    cd "$AUTH_DIR"
    
    # Start services
    docker-compose -f docker-compose-auth.yml up -d
    
    log_success "Authentication infrastructure started"
}

# Wait for Keycloak to be ready
wait_for_keycloak() {
    log_info "Waiting for Keycloak to be ready..."
    
    local max_attempts=60
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8090/health/ready > /dev/null 2>&1; then
            log_success "Keycloak is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Keycloak not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Keycloak failed to start within expected time"
    return 1
}

# Wait for Token Service to be ready
wait_for_token_service() {
    log_info "Waiting for Token Service to be ready..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost:8092/actuator/health > /dev/null 2>&1; then
            log_success "Token Service is ready"
            return 0
        fi
        
        log_info "Attempt $attempt/$max_attempts: Token Service not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    log_error "Token Service failed to start within expected time"
    return 1
}

# Test authentication flow
test_authentication() {
    log_info "Testing authentication flow..."
    
    # Get access token for test user
    log_info "Getting access token for test user..."
    
    TOKEN_RESPONSE=$(curl -s -X POST "http://localhost:8090/realms/ndvn/protocol/openid-connect/token" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -d "username=user" \
        -d "password=user123" \
        -d "grant_type=password" \
        -d "client_id=api-gateway" \
        -d "client_secret=api-gateway-secret")
    
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.access_token // empty')
    
    if [ -z "$ACCESS_TOKEN" ] || [ "$ACCESS_TOKEN" = "null" ]; then
        log_error "Failed to get access token"
        echo "Response: $TOKEN_RESPONSE"
        return 1
    fi
    
    log_success "Access token obtained successfully"
    
    # Test token introspection
    log_info "Testing token introspection..."
    
    INTROSPECT_RESPONSE=$(curl -s -X POST "http://localhost:8092/oauth/introspect" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        -H "Authorization: Bearer $ACCESS_TOKEN" \
        -d "token=$ACCESS_TOKEN")
    
    ACTIVE=$(echo "$INTROSPECT_RESPONSE" | jq -r '.active // false')
    
    if [ "$ACTIVE" = "true" ]; then
        log_success "Token introspection test passed"
    else
        log_error "Token introspection test failed"
        echo "Response: $INTROSPECT_RESPONSE"
        return 1
    fi
    
    log_success "Authentication flow test completed successfully"
}

# Verify Authentication setup
verify_setup() {
    log_info "Verifying Authentication setup..."
    
    # Check Keycloak admin console
    log_info "Keycloak Admin Console: http://localhost:8090/admin"
    log_info "Admin credentials: admin/admin-password"
    
    # Check realm configuration
    log_info "Checking realm configuration..."
    curl -s "http://localhost:8090/realms/ndvn/.well-known/openid_configuration" | jq .
    
    # Check Token Service health
    log_info "Checking Token Service health..."
    curl -s "http://localhost:8092/actuator/health" | jq .
    
    log_success "Authentication setup verification completed"
}

# Show status
show_status() {
    log_info "Authentication Infrastructure Status:"
    echo ""
    docker-compose -f "$AUTH_DIR/docker-compose-auth.yml" ps
    echo ""
    log_info "Access URLs:"
    echo "  - Keycloak Admin Console: http://localhost:8090/admin (admin/admin-password)"
    echo "  - Keycloak Realm: http://localhost:8090/realms/ndvn"
    echo "  - Token Service: http://localhost:8092"
    echo "  - Token Introspection: http://localhost:8092/oauth/introspect"
    echo ""
    log_info "Test Users:"
    echo "  - admin/admin123 (admin role)"
    echo "  - user/user123 (user role)"
    echo "  - manager/manager123 (manager role)"
    echo ""
}

# Stop Authentication infrastructure
stop_auth() {
    log_info "Stopping Authentication infrastructure..."
    cd "$AUTH_DIR"
    docker-compose -f docker-compose-auth.yml down
    log_success "Authentication infrastructure stopped"
}

# Clean up Authentication infrastructure
cleanup_auth() {
    log_warning "This will remove all Authentication data and containers. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        log_info "Cleaning up Authentication infrastructure..."
        cd "$AUTH_DIR"
        docker-compose -f docker-compose-auth.yml down -v --remove-orphans
        docker rmi ndvn/token-service:latest 2>/dev/null || true
        log_success "Authentication infrastructure cleaned up"
    else
        log_info "Cleanup cancelled"
    fi
}

# Main function
main() {
    case "${1:-start}" in
        start)
            check_docker
            check_docker_compose
            check_network
            build_token_service
            start_auth
            wait_for_keycloak
            wait_for_token_service
            test_authentication
            verify_setup
            show_status
            ;;
        stop)
            stop_auth
            ;;
        restart)
            stop_auth
            sleep 5
            main start
            ;;
        build)
            build_token_service
            ;;
        test)
            test_authentication
            ;;
        status)
            show_status
            ;;
        cleanup)
            cleanup_auth
            ;;
        verify)
            verify_setup
            ;;
        *)
            echo "Usage: $0 {start|stop|restart|build|test|status|cleanup|verify}"
            echo ""
            echo "Commands:"
            echo "  start    - Start Authentication infrastructure"
            echo "  stop     - Stop Authentication infrastructure"
            echo "  restart  - Restart Authentication infrastructure"
            echo "  build    - Build Token Service image"
            echo "  test     - Test authentication flow"
            echo "  status   - Show infrastructure status"
            echo "  cleanup  - Remove all Authentication data and containers"
            echo "  verify   - Verify Authentication setup"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
