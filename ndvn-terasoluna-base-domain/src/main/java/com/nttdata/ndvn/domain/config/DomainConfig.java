package com.nttdata.ndvn.domain.config;

import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * Domain layer configuration class.
 * This configuration class is responsible for setting up domain-specific beans
 * and configurations using Java-based configuration instead of XML.
 */
@Configuration
@ComponentScan(basePackages = {
    "com.nttdata.ndvn.domain.service",
    "com.nttdata.ndvn.domain.model"
})
public class DomainConfig {
    
    // Domain-specific bean configurations can be added here
    // For example: domain services, validators, etc.
}
