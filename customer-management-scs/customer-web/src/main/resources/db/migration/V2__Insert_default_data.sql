-- Customer Management SCS Default Data
-- Version 2.0 - Insert default customer segments and sample data

-- Insert default customer segments
INSERT INTO customer_segments (id, name, description, criteria, is_active, created_at, updated_at) VALUES
(gen_random_uuid(), 'VIP', 'VIP customers with high credit limits and premium service', '{"creditLimit": {"min": 100000}, "classification": ["PREMIUM", "GOLD"]}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'ENTERPRISE', 'Large enterprise business customers', '{"customerType": "BUSINESS", "classification": ["ENTERPRISE"]}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'SME', 'Small and Medium Enterprise customers', '{"customerType": "BUSINESS", "classification": ["SME", "CORPORATE"]}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'RETAIL', 'Individual retail customers', '{"customerType": "INDIVIDUAL", "classification": ["STANDARD", "SILVER"]}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'NEW_CUSTOMER', 'Newly registered customers (last 30 days)', '{"createdDaysAgo": {"max": 30}}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'HIGH_VALUE', 'High value customers based on transaction history', '{"creditLimit": {"min": 50000}}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'INACTIVE', 'Inactive customers for re-engagement campaigns', '{"status": "INACTIVE"}', true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sample customers for testing and demonstration
INSERT INTO customers (id, customer_number, customer_type, company_name, first_name, last_name, email, phone, status, classification, credit_limit, created_at, updated_at) VALUES
-- Business customers
(gen_random_uuid(), 'CUST001', 'BUSINESS', 'NTTDATA Vietnam', null, null, '<EMAIL>', '+84-28-1234-5678', 'ACTIVE', 'ENTERPRISE', 1000000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST002', 'BUSINESS', 'FPT Software', null, null, '<EMAIL>', '+84-24-7300-8866', 'ACTIVE', 'ENTERPRISE', 800000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST003', 'BUSINESS', 'Vietcombank', null, null, '<EMAIL>', '+84-24-3825-1414', 'ACTIVE', 'ENTERPRISE', 2000000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST004', 'BUSINESS', 'Saigon Technology', null, null, '<EMAIL>', '+84-28-3930-2407', 'ACTIVE', 'CORPORATE', 500000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST005', 'BUSINESS', 'TMA Solutions', null, null, '<EMAIL>', '+84-28-3825-5555', 'ACTIVE', 'CORPORATE', 300000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),

-- Individual customers
(gen_random_uuid(), 'CUST006', 'INDIVIDUAL', null, 'Nguyen', 'Van An', '<EMAIL>', '+84-90-123-4567', 'ACTIVE', 'PREMIUM', 150000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST007', 'INDIVIDUAL', null, 'Tran', 'Thi Binh', '<EMAIL>', '+84-91-234-5678', 'ACTIVE', 'GOLD', 100000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST008', 'INDIVIDUAL', null, 'Le', 'Van Cuong', '<EMAIL>', '+84-92-345-6789', 'ACTIVE', 'SILVER', 50000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST009', 'INDIVIDUAL', null, 'Pham', 'Thi Dung', '<EMAIL>', '+84-93-456-7890', 'ACTIVE', 'STANDARD', 25000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
(gen_random_uuid(), 'CUST010', 'INDIVIDUAL', null, 'Hoang', 'Van Em', '<EMAIL>', '+84-94-567-8901', 'INACTIVE', 'STANDARD', 10000.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

-- Insert sample addresses for customers
INSERT INTO customer_addresses (customer_id, address_type, street_address, city, state, postal_code, country, is_primary, is_active, created_at, updated_at)
SELECT 
    c.id,
    'BILLING',
    CASE 
        WHEN c.company_name = 'NTTDATA Vietnam' THEN 'Lotte Center Hanoi, 54 Lieu Giai Street'
        WHEN c.company_name = 'FPT Software' THEN 'FPT Tower, 10 Pham Van Bach Street'
        WHEN c.company_name = 'Vietcombank' THEN '198 Tran Quang Khai Street'
        WHEN c.company_name = 'Saigon Technology' THEN '25 Nguyen Thi Minh Khai Street'
        WHEN c.company_name = 'TMA Solutions' THEN '15 Duy Tan Street'
        WHEN c.first_name = 'Nguyen' THEN '123 Le Loi Street'
        WHEN c.first_name = 'Tran' THEN '456 Nguyen Hue Street'
        WHEN c.first_name = 'Le' THEN '789 Dong Khoi Street'
        WHEN c.first_name = 'Pham' THEN '321 Hai Ba Trung Street'
        WHEN c.first_name = 'Hoang' THEN '654 Tran Hung Dao Street'
    END,
    CASE 
        WHEN c.company_name IN ('NTTDATA Vietnam', 'FPT Software', 'Vietcombank') THEN 'Hanoi'
        ELSE 'Ho Chi Minh City'
    END,
    CASE 
        WHEN c.company_name IN ('NTTDATA Vietnam', 'FPT Software', 'Vietcombank') THEN 'Hanoi'
        ELSE 'Ho Chi Minh City'
    END,
    CASE 
        WHEN c.company_name IN ('NTTDATA Vietnam', 'FPT Software', 'Vietcombank') THEN '100000'
        ELSE '700000'
    END,
    'VN',
    true,
    true,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
FROM customers c;

-- Insert sample notes for some customers
INSERT INTO customer_notes (customer_id, note_type, subject, content, is_internal, created_by, created_at)
SELECT 
    c.id,
    'GENERAL',
    'Customer onboarding completed',
    'Customer has been successfully onboarded and all required documentation has been verified.',
    true,
    'system',
    CURRENT_TIMESTAMP
FROM customers c
WHERE c.customer_number IN ('CUST001', 'CUST002', 'CUST006', 'CUST007');

INSERT INTO customer_notes (customer_id, note_type, subject, content, is_internal, created_by, created_at)
SELECT 
    c.id,
    'CREDIT',
    'Credit limit review',
    'Customer credit limit has been reviewed and approved based on financial assessment.',
    true,
    'credit_team',
    CURRENT_TIMESTAMP
FROM customers c
WHERE c.credit_limit > 100000;

-- Assign customers to appropriate segments
INSERT INTO customer_segment_assignments (customer_id, segment_id, assigned_at, assigned_by)
SELECT 
    c.id,
    s.id,
    CURRENT_TIMESTAMP,
    'system'
FROM customers c
CROSS JOIN customer_segments s
WHERE 
    (s.name = 'ENTERPRISE' AND c.customer_type = 'BUSINESS' AND c.classification = 'ENTERPRISE') OR
    (s.name = 'SME' AND c.customer_type = 'BUSINESS' AND c.classification IN ('CORPORATE', 'SME')) OR
    (s.name = 'VIP' AND c.credit_limit >= 100000 AND c.classification IN ('PREMIUM', 'GOLD')) OR
    (s.name = 'RETAIL' AND c.customer_type = 'INDIVIDUAL' AND c.classification IN ('STANDARD', 'SILVER')) OR
    (s.name = 'HIGH_VALUE' AND c.credit_limit >= 50000) OR
    (s.name = 'INACTIVE' AND c.status = 'INACTIVE');

-- Update statistics (this would typically be done by a scheduled job)
-- For now, we'll just ensure our sample data is consistent

-- Verify data integrity
DO $$
DECLARE
    customer_count INTEGER;
    address_count INTEGER;
    segment_count INTEGER;
    assignment_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO customer_count FROM customers;
    SELECT COUNT(*) INTO address_count FROM customer_addresses;
    SELECT COUNT(*) INTO segment_count FROM customer_segments;
    SELECT COUNT(*) INTO assignment_count FROM customer_segment_assignments;
    
    RAISE NOTICE 'Data insertion completed:';
    RAISE NOTICE '- Customers: %', customer_count;
    RAISE NOTICE '- Addresses: %', address_count;
    RAISE NOTICE '- Segments: %', segment_count;
    RAISE NOTICE '- Segment Assignments: %', assignment_count;
END $$;
