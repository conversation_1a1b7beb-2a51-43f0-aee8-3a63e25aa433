package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Category;
import com.nttdata.ndvn.product.domain.repository.CategoryRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Implementation of CategoryRepository using JPA.
 */
@Repository
public class CategoryRepositoryImpl implements CategoryRepository {
    
    private final JpaCategoryRepository jpaCategoryRepository;
    
    public CategoryRepositoryImpl(JpaCategoryRepository jpaCategoryRepository) {
        this.jpaCategoryRepository = jpaCategoryRepository;
    }
    
    @Override
    public Category save(Category category) {
        return jpaCategoryRepository.save(category);
    }
    
    @Override
    public Optional<Category> findById(UUID id) {
        return jpaCategoryRepository.findById(id);
    }
    
    @Override
    public Optional<Category> findBySlug(String slug) {
        return jpaCategoryRepository.findBySlug(slug);
    }
    
    @Override
    public Page<Category> findAll(Pageable pageable) {
        return jpaCategoryRepository.findAll(pageable);
    }
    
    @Override
    public Page<Category> findByActive(boolean active, Pageable pageable) {
        return jpaCategoryRepository.findByActive(active, pageable);
    }
    
    @Override
    public List<Category> findRootCategories() {
        return jpaCategoryRepository.findRootCategories();
    }
    
    @Override
    public List<Category> findByParentId(UUID parentId) {
        return jpaCategoryRepository.findByParentId(parentId);
    }
    
    @Override
    public Page<Category> findByParentId(UUID parentId, Pageable pageable) {
        return jpaCategoryRepository.findByParentId(parentId, pageable);
    }
    
    @Override
    public List<Category> findByLevel(Integer level) {
        return jpaCategoryRepository.findByLevel(level);
    }
    
    @Override
    public Page<Category> findByLevel(Integer level, Pageable pageable) {
        return jpaCategoryRepository.findByLevel(level, pageable);
    }
    
    @Override
    public Page<Category> findByNameContainingIgnoreCase(String name, Pageable pageable) {
        return jpaCategoryRepository.findByNameContainingIgnoreCase(name, pageable);
    }
    
    @Override
    public Page<Category> findByDescriptionContainingIgnoreCase(String description, Pageable pageable) {
        return jpaCategoryRepository.findByDescriptionContainingIgnoreCase(description, pageable);
    }
    
    @Override
    public Page<Category> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable) {
        return jpaCategoryRepository.findByCreatedAtAfter(createdAfter, pageable);
    }
    
    @Override
    public Page<Category> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable) {
        return jpaCategoryRepository.findByUpdatedAtAfter(updatedAfter, pageable);
    }
    
    @Override
    public List<Category> findAllOrderBySortOrderAsc() {
        return jpaCategoryRepository.findAllByOrderBySortOrderAsc();
    }
    
    @Override
    public List<Category> findByParentIdOrderBySortOrderAsc(UUID parentId) {
        return jpaCategoryRepository.findByParentIdOrderBySortOrderAsc(parentId);
    }
    
    @Override
    public Page<Category> findAllOrderByNameAsc(Pageable pageable) {
        return jpaCategoryRepository.findAllByOrderByNameAsc(pageable);
    }
    
    @Override
    public Page<Category> findAllOrderByCreatedAtDesc(Pageable pageable) {
        return jpaCategoryRepository.findAllByOrderByCreatedAtDesc(pageable);
    }
    
    @Override
    public Page<Category> searchCategories(String searchTerm, Pageable pageable) {
        return jpaCategoryRepository.searchCategories(searchTerm, pageable);
    }
    
    @Override
    public List<Category> findLeafCategories() {
        return jpaCategoryRepository.findLeafCategories();
    }
    
    @Override
    public List<Category> findCategoriesWithChildren() {
        return jpaCategoryRepository.findCategoriesWithChildren();
    }
    
    @Override
    public Page<Category> findByPathContaining(String pathSegment, Pageable pageable) {
        return jpaCategoryRepository.findByPathContaining(pathSegment, pageable);
    }
    
    @Override
    public boolean existsBySlug(String slug) {
        return jpaCategoryRepository.existsBySlug(slug);
    }
    
    @Override
    public boolean existsById(UUID id) {
        return jpaCategoryRepository.existsById(id);
    }
    
    @Override
    public boolean hasChildren(UUID categoryId) {
        return jpaCategoryRepository.hasChildren(categoryId);
    }
    
    @Override
    public boolean hasProducts(UUID categoryId) {
        return jpaCategoryRepository.hasProducts(categoryId);
    }
    
    @Override
    public long countByParentId(UUID parentId) {
        return jpaCategoryRepository.countByParentId(parentId);
    }
    
    @Override
    public long countByActive(boolean active) {
        return jpaCategoryRepository.countByActive(active);
    }
    
    @Override
    public long countByLevel(Integer level) {
        return jpaCategoryRepository.countByLevel(level);
    }
    
    @Override
    public long countRootCategories() {
        return jpaCategoryRepository.countRootCategories();
    }
    
    @Override
    public long countLeafCategories() {
        return jpaCategoryRepository.countLeafCategories();
    }
    
    @Override
    public List<Category> findDescendants(UUID categoryId) {
        Category category = jpaCategoryRepository.findById(categoryId).orElse(null);
        if (category == null || category.getPath() == null) {
            return List.of();
        }
        return jpaCategoryRepository.findDescendants(category.getPath());
    }
    
    @Override
    public List<Category> findAncestors(UUID categoryId) {
        Category category = jpaCategoryRepository.findById(categoryId).orElse(null);
        if (category == null) {
            return List.of();
        }
        return category.getAncestors();
    }
    
    @Override
    public List<Category> findSiblings(UUID categoryId) {
        Category category = jpaCategoryRepository.findById(categoryId).orElse(null);
        if (category == null) {
            return List.of();
        }
        return jpaCategoryRepository.findByParentId(category.getParentId()).stream()
                .filter(c -> !c.getId().equals(categoryId))
                .toList();
    }
    
    @Override
    public List<Category> findAllById(Iterable<UUID> ids) {
        return jpaCategoryRepository.findAllById(ids);
    }
    
    @Override
    public List<Category> findByParentIdIn(List<UUID> parentIds) {
        return jpaCategoryRepository.findByParentIdIn(parentIds);
    }
    
    @Override
    public List<Category> findByLevelIn(List<Integer> levels) {
        return jpaCategoryRepository.findByLevelIn(levels);
    }
    
    @Override
    public Page<Category> findByImageUrlIsNotNull(Pageable pageable) {
        return jpaCategoryRepository.findByImageUrlIsNotNull(pageable);
    }
    
    @Override
    public Page<Category> findByImageUrlIsNull(Pageable pageable) {
        return jpaCategoryRepository.findByImageUrlIsNull(pageable);
    }
    
    @Override
    public Page<Category> findByIconIsNotNull(Pageable pageable) {
        return jpaCategoryRepository.findByIconIsNotNull(pageable);
    }
    
    @Override
    public Page<Category> findByIconIsNull(Pageable pageable) {
        return jpaCategoryRepository.findByIconIsNull(pageable);
    }
    
    @Override
    public Page<Category> findByMetaKeywordsContaining(String keywords, Pageable pageable) {
        return jpaCategoryRepository.findByMetaKeywordsContaining(keywords, pageable);
    }
    
    @Override
    public List<Category> findCategoriesForSitemap() {
        return jpaCategoryRepository.findCategoriesForSitemap();
    }
    
    @Override
    public List<Category> findCategoriesForNavigation(Integer maxLevel) {
        return jpaCategoryRepository.findCategoriesForNavigation(maxLevel);
    }
    
    @Override
    public List<Category> findPopularCategories(int limit) {
        return jpaCategoryRepository.findPopularCategories(limit);
    }
    
    @Override
    public void deleteById(UUID id) {
        jpaCategoryRepository.deleteById(id);
    }
    
    @Override
    public void delete(Category category) {
        jpaCategoryRepository.delete(category);
    }
    
    @Override
    public List<Category> saveAll(Iterable<Category> categories) {
        return jpaCategoryRepository.saveAll(categories);
    }
    
    @Override
    public long count() {
        return jpaCategoryRepository.count();
    }
    
    @Override
    public List<Category> buildCategoryTree() {
        return buildCategoryTree(null);
    }
    
    @Override
    public List<Category> buildCategoryTree(UUID parentId) {
        List<Category> categories = jpaCategoryRepository.findByParentIdOrderBySortOrderAsc(parentId);
        for (Category category : categories) {
            category.setChildren(buildCategoryTree(category.getId()));
        }
        return categories;
    }
    
    @Override
    public List<Category> getCategoryBreadcrumb(UUID categoryId) {
        Category category = jpaCategoryRepository.findById(categoryId).orElse(null);
        if (category == null) {
            return List.of();
        }
        return category.getAncestors();
    }
    
    @Override
    public List<Category> findByMaxDepth(Integer maxDepth) {
        return jpaCategoryRepository.findByLevel(maxDepth);
    }
    
    @Override
    public void updateCategoryPaths(UUID categoryId) {
        Category category = jpaCategoryRepository.findById(categoryId).orElse(null);
        if (category != null) {
            updateCategoryHierarchy(category);
        }
    }
    
    @Override
    public void reorderCategories(UUID parentId, List<UUID> categoryIds) {
        for (int i = 0; i < categoryIds.size(); i++) {
            Category category = jpaCategoryRepository.findById(categoryIds.get(i)).orElse(null);
            if (category != null && 
                ((parentId == null && category.getParentId() == null) || 
                 (parentId != null && parentId.equals(category.getParentId())))) {
                category.setSortOrder(i);
                jpaCategoryRepository.save(category);
            }
        }
    }
    
    private void updateCategoryHierarchy(Category category) {
        // Update level and path
        if (category.getParentId() == null) {
            category.setLevel(0);
            category.setPath(category.getSlug());
        } else {
            Category parent = jpaCategoryRepository.findById(category.getParentId()).orElse(null);
            if (parent != null) {
                category.setLevel(parent.getLevel() + 1);
                category.setPath(parent.getPath() + "/" + category.getSlug());
            }
        }
        
        jpaCategoryRepository.save(category);
        
        // Update all children recursively
        List<Category> children = jpaCategoryRepository.findByParentId(category.getId());
        for (Category child : children) {
            updateCategoryHierarchy(child);
        }
    }
}
