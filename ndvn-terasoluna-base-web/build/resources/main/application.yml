# Default application configuration
spring:
  application:
    name: ndvn-terasoluna-base
  
  # Profile configuration
  profiles:
    active: development
  
  # Database configuration (H2 for development)
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver
  
  # H2 Console (for development only)
  h2:
    console:
      enabled: true
      path: /h2-console
  
  # JPA configuration
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        format_sql: true
        dialect: org.hibernate.dialect.H2Dialect
  
  # Jackson configuration
  jackson:
    serialization:
      write-dates-as-timestamps: false
    time-zone: Asia/Ho_Chi_Minh

# Server configuration
server:
  port: 8080
  servlet:
    context-path: /
  error:
    include-stacktrace: never

# Management endpoints
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

# Logging configuration
logging:
  level:
    com.nttdata.ndvn: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Custom application properties
application:
  name: NDVN Terasoluna Base
  version: 1.0.0-SNAPSHOT
  description: Multi-module Terasoluna backend application
