package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Inventory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA repository interface for Inventory entity.
 */
@Repository
public interface JpaInventoryRepository extends JpaRepository<Inventory, UUID> {
    
    /**
     * Find inventory by product ID and location.
     */
    Optional<Inventory> findByProductIdAndLocationCode(UUID productId, String locationCode);
    
    /**
     * Find inventory by variant ID and location.
     */
    Optional<Inventory> findByVariantIdAndLocationCode(UUID variantId, String locationCode);
    
    /**
     * Find inventory by product ID.
     */
    List<Inventory> findByProductId(UUID productId);
    
    /**
     * Find inventory by variant ID.
     */
    List<Inventory> findByVariantId(UUID variantId);
    
    /**
     * Find inventory by location code.
     */
    Page<Inventory> findByLocationCode(String locationCode, Pageable pageable);
    
    /**
     * Find active inventory.
     */
    Page<Inventory> findByActive(boolean active, Pageable pageable);
    
    /**
     * Find inventory that tracks stock.
     */
    Page<Inventory> findByTrackInventory(boolean trackInventory, Pageable pageable);
    
    /**
     * Find inventory that allows backorder.
     */
    Page<Inventory> findByAllowBackorder(boolean allowBackorder, Pageable pageable);
    
    /**
     * Find inventory with low stock.
     */
    @Query("SELECT i FROM Inventory i WHERE i.active = true AND i.trackInventory = true AND i.availableQuantity <= i.reorderPoint")
    List<Inventory> findLowStockInventory();
    
    /**
     * Find inventory out of stock.
     */
    @Query("SELECT i FROM Inventory i WHERE i.active = true AND i.trackInventory = true AND i.availableQuantity = 0")
    List<Inventory> findOutOfStockInventory();
    
    /**
     * Find inventory that needs reorder.
     */
    @Query("SELECT i FROM Inventory i WHERE i.active = true AND i.trackInventory = true AND i.availableQuantity <= i.reorderPoint")
    List<Inventory> findInventoryNeedingReorder();
    
    /**
     * Find inventory by available quantity range.
     */
    Page<Inventory> findByAvailableQuantityBetween(Integer minQuantity, Integer maxQuantity, Pageable pageable);
    
    /**
     * Find inventory by total quantity range.
     */
    Page<Inventory> findByTotalQuantityBetween(Integer minQuantity, Integer maxQuantity, Pageable pageable);
    
    /**
     * Find inventory with reserved stock.
     */
    Page<Inventory> findByReservedQuantityGreaterThan(Integer quantity, Pageable pageable);
    
    /**
     * Find inventory with damaged stock.
     */
    Page<Inventory> findByDamagedQuantityGreaterThan(Integer quantity, Pageable pageable);
    
    /**
     * Find inventory updated after a specific date.
     */
    Page<Inventory> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find inventory by last stock check before date.
     */
    Page<Inventory> findByLastStockCheckBefore(LocalDateTime date, Pageable pageable);
    
    /**
     * Find inventory by multiple product IDs.
     */
    List<Inventory> findByProductIdIn(List<UUID> productIds);
    
    /**
     * Find inventory by multiple variant IDs.
     */
    List<Inventory> findByVariantIdIn(List<UUID> variantIds);
    
    /**
     * Find inventory by multiple location codes.
     */
    Page<Inventory> findByLocationCodeIn(List<String> locationCodes, Pageable pageable);
    
    /**
     * Check if inventory exists for product and location.
     */
    boolean existsByProductIdAndLocationCode(UUID productId, String locationCode);
    
    /**
     * Check if inventory exists for variant and location.
     */
    boolean existsByVariantIdAndLocationCode(UUID variantId, String locationCode);
    
    /**
     * Count inventory by product ID.
     */
    long countByProductId(UUID productId);
    
    /**
     * Count inventory by location code.
     */
    long countByLocationCode(String locationCode);
    
    /**
     * Count active inventory.
     */
    long countByActive(boolean active);
    
    /**
     * Count low stock inventory.
     */
    @Query("SELECT COUNT(i) FROM Inventory i WHERE i.active = true AND i.trackInventory = true AND i.availableQuantity <= i.reorderPoint")
    long countLowStockInventory();
    
    /**
     * Count out of stock inventory.
     */
    @Query("SELECT COUNT(i) FROM Inventory i WHERE i.active = true AND i.trackInventory = true AND i.availableQuantity = 0")
    long countOutOfStockInventory();
    
    /**
     * Sum total quantity by product ID.
     */
    @Query("SELECT COALESCE(SUM(i.totalQuantity), 0) FROM Inventory i WHERE i.productId = :productId AND i.active = true")
    Integer sumTotalQuantityByProductId(@Param("productId") UUID productId);
    
    /**
     * Sum available quantity by product ID.
     */
    @Query("SELECT COALESCE(SUM(i.availableQuantity), 0) FROM Inventory i WHERE i.productId = :productId AND i.active = true")
    Integer sumAvailableQuantityByProductId(@Param("productId") UUID productId);
    
    /**
     * Sum reserved quantity by product ID.
     */
    @Query("SELECT COALESCE(SUM(i.reservedQuantity), 0) FROM Inventory i WHERE i.productId = :productId AND i.active = true")
    Integer sumReservedQuantityByProductId(@Param("productId") UUID productId);
    
    /**
     * Sum total quantity by location code.
     */
    @Query("SELECT COALESCE(SUM(i.totalQuantity), 0) FROM Inventory i WHERE i.locationCode = :locationCode AND i.active = true")
    Integer sumTotalQuantityByLocationCode(@Param("locationCode") String locationCode);
    
    /**
     * Sum available quantity by location code.
     */
    @Query("SELECT COALESCE(SUM(i.availableQuantity), 0) FROM Inventory i WHERE i.locationCode = :locationCode AND i.active = true")
    Integer sumAvailableQuantityByLocationCode(@Param("locationCode") String locationCode);
    
    /**
     * Find inventory locations for product.
     */
    @Query("SELECT DISTINCT i.locationCode FROM Inventory i WHERE i.productId = :productId AND i.active = true")
    List<String> findLocationsByProductId(@Param("productId") UUID productId);
    
    /**
     * Find inventory locations for variant.
     */
    @Query("SELECT DISTINCT i.locationCode FROM Inventory i WHERE i.variantId = :variantId AND i.active = true")
    List<String> findLocationsByVariantId(@Param("variantId") UUID variantId);
    
    /**
     * Find all unique location codes.
     */
    @Query("SELECT DISTINCT i.locationCode FROM Inventory i WHERE i.active = true ORDER BY i.locationCode")
    List<String> findAllLocationCodes();
    
    /**
     * Find inventory summary by location.
     */
    @Query("SELECT i.locationCode, i.locationName, COUNT(i), SUM(i.totalQuantity), SUM(i.availableQuantity) " +
           "FROM Inventory i WHERE i.active = true GROUP BY i.locationCode, i.locationName ORDER BY i.locationCode")
    List<Object[]> findInventorySummaryByLocation();
    
    /**
     * Find inventory summary by product.
     */
    @Query("SELECT i.productId, COUNT(i), SUM(i.totalQuantity), SUM(i.availableQuantity) " +
           "FROM Inventory i WHERE i.active = true GROUP BY i.productId")
    List<Object[]> findInventorySummaryByProduct();
    
    /**
     * Delete inventory by product ID.
     */
    void deleteByProductId(UUID productId);
    
    /**
     * Delete inventory by variant ID.
     */
    void deleteByVariantId(UUID variantId);
}
