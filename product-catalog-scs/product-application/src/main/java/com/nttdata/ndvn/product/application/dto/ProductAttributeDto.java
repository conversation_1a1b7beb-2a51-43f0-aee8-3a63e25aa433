package com.nttdata.ndvn.product.application.dto;

import com.nttdata.ndvn.product.domain.model.ProductAttribute;
import jakarta.validation.constraints.*;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Data Transfer Object for ProductAttribute.
 */
public class ProductAttributeDto {
    
    private UUID id;
    private UUID productId;
    
    @NotBlank(message = "Attribute name is required")
    @Size(max = 255, message = "Attribute name must not exceed 255 characters")
    private String attributeName;
    
    @NotNull(message = "Attribute type is required")
    private ProductAttribute.AttributeType attributeType;
    
    @NotBlank(message = "Attribute value is required")
    @Size(max = 1000, message = "Attribute value must not exceed 1000 characters")
    private String attributeValue;
    
    @Size(max = 100, message = "Unit must not exceed 100 characters")
    private String unit;
    
    private boolean filterable;
    private boolean searchable;
    private boolean visible;
    private Integer sortOrder;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // Computed fields
    private String formattedValue;
    private boolean validValue;
    
    // Constructors
    public ProductAttributeDto() {}
    
    // Getters and setters
    public UUID getId() { return id; }
    public void setId(UUID id) { this.id = id; }
    
    public UUID getProductId() { return productId; }
    public void setProductId(UUID productId) { this.productId = productId; }
    
    public String getAttributeName() { return attributeName; }
    public void setAttributeName(String attributeName) { this.attributeName = attributeName; }
    
    public ProductAttribute.AttributeType getAttributeType() { return attributeType; }
    public void setAttributeType(ProductAttribute.AttributeType attributeType) { this.attributeType = attributeType; }
    
    public String getAttributeValue() { return attributeValue; }
    public void setAttributeValue(String attributeValue) { this.attributeValue = attributeValue; }
    
    public String getUnit() { return unit; }
    public void setUnit(String unit) { this.unit = unit; }
    
    public boolean isFilterable() { return filterable; }
    public void setFilterable(boolean filterable) { this.filterable = filterable; }
    
    public boolean isSearchable() { return searchable; }
    public void setSearchable(boolean searchable) { this.searchable = searchable; }
    
    public boolean isVisible() { return visible; }
    public void setVisible(boolean visible) { this.visible = visible; }
    
    public Integer getSortOrder() { return sortOrder; }
    public void setSortOrder(Integer sortOrder) { this.sortOrder = sortOrder; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public String getFormattedValue() { return formattedValue; }
    public void setFormattedValue(String formattedValue) { this.formattedValue = formattedValue; }
    
    public boolean isValidValue() { return validValue; }
    public void setValidValue(boolean validValue) { this.validValue = validValue; }
}
