package com.nttdata.ndvn.product.infrastructure.repository;

import com.nttdata.ndvn.product.domain.model.Product;
import com.nttdata.ndvn.product.domain.model.ProductStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA repository implementation for Product entity.
 * 
 * This repository provides data access operations for products using Spring Data JPA.
 */
@Repository
public interface JpaProductRepository extends JpaRepository<Product, UUID> {
    
    /**
     * Find product by SKU.
     */
    Optional<Product> findBySku(String sku);
    
    /**
     * Find products by status.
     */
    Page<Product> findByStatus(ProductStatus status, Pageable pageable);
    
    /**
     * Find products by category.
     */
    Page<Product> findByCategoryId(UUID categoryId, Pageable pageable);
    
    /**
     * Find products by brand.
     */
    Page<Product> findByBrandId(UUID brandId, Pageable pageable);
    
    /**
     * Find featured products.
     */
    Page<Product> findByFeatured(boolean featured, Pageable pageable);
    
    /**
     * Find products by price range.
     */
    Page<Product> findByBasePriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    /**
     * Find products on sale.
     */
    @Query("SELECT p FROM Product p WHERE p.salePrice IS NOT NULL AND p.salePrice < p.basePrice")
    Page<Product> findProductsOnSale(Pageable pageable);
    
    /**
     * Search products by name or description.
     */
    @Query("SELECT p FROM Product p WHERE " +
           "LOWER(p.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.longDescription) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(p.sku) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    Page<Product> searchProducts(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    /**
     * Find products created after a specific date.
     */
    Page<Product> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find products updated after a specific date.
     */
    Page<Product> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find products by multiple categories.
     */
    Page<Product> findByCategoryIdIn(List<UUID> categoryIds, Pageable pageable);
    
    /**
     * Find products by multiple brands.
     */
    Page<Product> findByBrandIdIn(List<UUID> brandIds, Pageable pageable);
    
    /**
     * Find products by multiple statuses.
     */
    Page<Product> findByStatusIn(List<ProductStatus> statuses, Pageable pageable);
    
    /**
     * Find digital products.
     */
    Page<Product> findByDigital(boolean digital, Pageable pageable);
    
    /**
     * Find products that require shipping.
     */
    Page<Product> findByRequiresShipping(boolean requiresShipping, Pageable pageable);
    
    /**
     * Find taxable products.
     */
    Page<Product> findByTaxable(boolean taxable, Pageable pageable);
    
    /**
     * Check if SKU exists.
     */
    boolean existsBySku(String sku);
    
    /**
     * Count products by status.
     */
    long countByStatus(ProductStatus status);
    
    /**
     * Count products by category.
     */
    long countByCategoryId(UUID categoryId);
    
    /**
     * Count products by brand.
     */
    long countByBrandId(UUID brandId);
    
    /**
     * Count featured products.
     */
    long countByFeatured(boolean featured);
    
    /**
     * Count products created after a specific date.
     */
    long countByCreatedAtAfter(LocalDateTime createdAfter);
    
    /**
     * Find products with low inventory.
     */
    @Query("SELECT DISTINCT p FROM Product p " +
           "JOIN Inventory i ON p.id = i.productId " +
           "WHERE i.active = true AND i.availableQuantity <= i.reorderPoint")
    List<Product> findProductsWithLowInventory();
    
    /**
     * Find products without images.
     */
    @Query("SELECT p FROM Product p WHERE p.id NOT IN " +
           "(SELECT DISTINCT pi.productId FROM ProductImage pi WHERE pi.active = true)")
    Page<Product> findProductsWithoutImages(Pageable pageable);
    
    /**
     * Find products without variants.
     */
    @Query("SELECT p FROM Product p WHERE p.id NOT IN " +
           "(SELECT DISTINCT pv.productId FROM ProductVariant pv WHERE pv.active = true)")
    Page<Product> findProductsWithoutVariants(Pageable pageable);
    
    /**
     * Find products by name containing (case-insensitive).
     */
    Page<Product> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find products by description containing (case-insensitive).
     */
    Page<Product> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find products by meta keywords containing.
     */
    Page<Product> findByMetaKeywordsContaining(String keywords, Pageable pageable);
    
    /**
     * Find products ordered by creation date.
     */
    Page<Product> findAllByOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * Find products ordered by update date.
     */
    Page<Product> findAllByOrderByUpdatedAtDesc(Pageable pageable);
    
    /**
     * Find products ordered by name.
     */
    Page<Product> findAllByOrderByNameAsc(Pageable pageable);
    
    /**
     * Find products ordered by price.
     */
    Page<Product> findAllByOrderByBasePriceAsc(Pageable pageable);
    
    /**
     * Find products ordered by sort order.
     */
    Page<Product> findAllByOrderBySortOrderAsc(Pageable pageable);
    
    /**
     * Advanced search with multiple criteria.
     */
    @Query("SELECT p FROM Product p WHERE " +
           "(:searchTerm IS NULL OR " +
           " LOWER(p.name) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           " LOWER(p.description) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           " LOWER(p.sku) LIKE LOWER(CONCAT('%', :searchTerm, '%'))) AND " +
           "(:categoryIds IS NULL OR p.categoryId IN :categoryIds) AND " +
           "(:brandIds IS NULL OR p.brandId IN :brandIds) AND " +
           "(:statuses IS NULL OR p.status IN :statuses) AND " +
           "(:minPrice IS NULL OR p.basePrice >= :minPrice) AND " +
           "(:maxPrice IS NULL OR p.basePrice <= :maxPrice) AND " +
           "(:featured IS NULL OR p.featured = :featured) AND " +
           "(:digital IS NULL OR p.digital = :digital) AND " +
           "(:onSale IS NULL OR (:onSale = true AND p.salePrice IS NOT NULL AND p.salePrice < p.basePrice) OR " +
           " (:onSale = false AND (p.salePrice IS NULL OR p.salePrice >= p.basePrice)))")
    Page<Product> findProductsByCriteria(
        @Param("searchTerm") String searchTerm,
        @Param("categoryIds") List<UUID> categoryIds,
        @Param("brandIds") List<UUID> brandIds,
        @Param("statuses") List<ProductStatus> statuses,
        @Param("minPrice") BigDecimal minPrice,
        @Param("maxPrice") BigDecimal maxPrice,
        @Param("featured") Boolean featured,
        @Param("digital") Boolean digital,
        @Param("onSale") Boolean onSale,
        Pageable pageable
    );
    
    /**
     * Find random products for recommendations.
     */
    @Query(value = "SELECT * FROM products WHERE status = 'ACTIVE' ORDER BY RANDOM() LIMIT :limit", 
           nativeQuery = true)
    List<Product> findRandomProducts(@Param("limit") int limit);
    
    /**
     * Find related products by category.
     */
    @Query("SELECT p FROM Product p WHERE p.categoryId = :categoryId AND p.id != :excludeProductId AND p.status = 'ACTIVE' ORDER BY p.createdAt DESC")
    List<Product> findRelatedProductsByCategory(@Param("categoryId") UUID categoryId,
                                               @Param("excludeProductId") UUID excludeProductId,
                                               Pageable pageable);

    /**
     * Find related products by brand.
     */
    @Query("SELECT p FROM Product p WHERE p.brandId = :brandId AND p.id != :excludeProductId AND p.status = 'ACTIVE' ORDER BY p.createdAt DESC")
    List<Product> findRelatedProductsByBrand(@Param("brandId") UUID brandId,
                                            @Param("excludeProductId") UUID excludeProductId,
                                            Pageable pageable);

    /**
     * Find newest products.
     */
    @Query("SELECT p FROM Product p WHERE p.status = 'ACTIVE' ORDER BY p.createdAt DESC")
    List<Product> findNewestProducts(Pageable pageable);

    /**
     * Find best selling products.
     */
    @Query(value = "SELECT p.* FROM products p " +
                   "LEFT JOIN order_items oi ON p.id = oi.product_id " +
                   "WHERE p.status = 'ACTIVE' " +
                   "GROUP BY p.id " +
                   "ORDER BY COALESCE(SUM(oi.quantity), 0) DESC " +
                   "LIMIT :limit",
           nativeQuery = true)
    List<Product> findBestSellingProducts(@Param("limit") int limit);
}
