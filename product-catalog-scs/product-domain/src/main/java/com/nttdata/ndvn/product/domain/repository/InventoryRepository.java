package com.nttdata.ndvn.product.domain.repository;

import com.nttdata.ndvn.product.domain.model.Inventory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Inventory entity.
 * 
 * This interface defines the contract for inventory data access operations.
 */
public interface InventoryRepository {
    
    /**
     * Save an inventory entity.
     */
    Inventory save(Inventory inventory);
    
    /**
     * Find inventory by ID.
     */
    Optional<Inventory> findById(UUID id);
    
    /**
     * Find inventory by product ID and location.
     */
    Optional<Inventory> findByProductIdAndLocationCode(UUID productId, String locationCode);
    
    /**
     * Find inventory by variant ID and location.
     */
    Optional<Inventory> findByVariantIdAndLocationCode(UUID variantId, String locationCode);
    
    /**
     * Find all inventory with pagination.
     */
    Page<Inventory> findAll(Pageable pageable);
    
    /**
     * Find inventory by product ID.
     */
    List<Inventory> findByProductId(UUID productId);
    
    /**
     * Find inventory by variant ID.
     */
    List<Inventory> findByVariantId(UUID variantId);
    
    /**
     * Find inventory by location code.
     */
    Page<Inventory> findByLocationCode(String locationCode, Pageable pageable);
    
    /**
     * Find active inventory.
     */
    Page<Inventory> findByActive(boolean active, Pageable pageable);
    
    /**
     * Find inventory that tracks stock.
     */
    Page<Inventory> findByTrackInventory(boolean trackInventory, Pageable pageable);
    
    /**
     * Find inventory that allows backorder.
     */
    Page<Inventory> findByAllowBackorder(boolean allowBackorder, Pageable pageable);
    
    /**
     * Find inventory with low stock.
     */
    List<Inventory> findLowStockInventory();
    
    /**
     * Find inventory out of stock.
     */
    List<Inventory> findOutOfStockInventory();
    
    /**
     * Find inventory that needs reorder.
     */
    List<Inventory> findInventoryNeedingReorder();
    
    /**
     * Find inventory by available quantity range.
     */
    Page<Inventory> findByAvailableQuantityBetween(Integer minQuantity, Integer maxQuantity, Pageable pageable);
    
    /**
     * Find inventory by total quantity range.
     */
    Page<Inventory> findByTotalQuantityBetween(Integer minQuantity, Integer maxQuantity, Pageable pageable);
    
    /**
     * Find inventory with reserved stock.
     */
    Page<Inventory> findByReservedQuantityGreaterThan(Integer quantity, Pageable pageable);
    
    /**
     * Find inventory with damaged stock.
     */
    Page<Inventory> findByDamagedQuantityGreaterThan(Integer quantity, Pageable pageable);
    
    /**
     * Find inventory updated after a specific date.
     */
    Page<Inventory> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find inventory by last stock check before date.
     */
    Page<Inventory> findByLastStockCheckBefore(LocalDateTime date, Pageable pageable);
    
    /**
     * Find inventory by multiple product IDs.
     */
    List<Inventory> findByProductIdIn(List<UUID> productIds);
    
    /**
     * Find inventory by multiple variant IDs.
     */
    List<Inventory> findByVariantIdIn(List<UUID> variantIds);
    
    /**
     * Find inventory by multiple location codes.
     */
    Page<Inventory> findByLocationCodeIn(List<String> locationCodes, Pageable pageable);
    
    /**
     * Check if inventory exists for product and location.
     */
    boolean existsByProductIdAndLocationCode(UUID productId, String locationCode);
    
    /**
     * Check if inventory exists for variant and location.
     */
    boolean existsByVariantIdAndLocationCode(UUID variantId, String locationCode);
    
    /**
     * Check if inventory exists by ID.
     */
    boolean existsById(UUID id);
    
    /**
     * Count inventory by product ID.
     */
    long countByProductId(UUID productId);
    
    /**
     * Count inventory by location code.
     */
    long countByLocationCode(String locationCode);
    
    /**
     * Count active inventory.
     */
    long countByActive(boolean active);
    
    /**
     * Count low stock inventory.
     */
    long countLowStockInventory();
    
    /**
     * Count out of stock inventory.
     */
    long countOutOfStockInventory();
    
    /**
     * Sum total quantity by product ID.
     */
    Integer sumTotalQuantityByProductId(UUID productId);
    
    /**
     * Sum available quantity by product ID.
     */
    Integer sumAvailableQuantityByProductId(UUID productId);
    
    /**
     * Sum reserved quantity by product ID.
     */
    Integer sumReservedQuantityByProductId(UUID productId);
    
    /**
     * Sum total quantity by location code.
     */
    Integer sumTotalQuantityByLocationCode(String locationCode);
    
    /**
     * Sum available quantity by location code.
     */
    Integer sumAvailableQuantityByLocationCode(String locationCode);
    
    /**
     * Find inventory locations for product.
     */
    List<String> findLocationsByProductId(UUID productId);
    
    /**
     * Find inventory locations for variant.
     */
    List<String> findLocationsByVariantId(UUID variantId);
    
    /**
     * Find all unique location codes.
     */
    List<String> findAllLocationCodes();
    
    /**
     * Find inventory summary by location.
     */
    List<Object[]> findInventorySummaryByLocation();
    
    /**
     * Find inventory summary by product.
     */
    List<Object[]> findInventorySummaryByProduct();
    
    /**
     * Delete inventory by ID.
     */
    void deleteById(UUID id);
    
    /**
     * Delete inventory entity.
     */
    void delete(Inventory inventory);
    
    /**
     * Delete inventory by product ID.
     */
    void deleteByProductId(UUID productId);
    
    /**
     * Delete inventory by variant ID.
     */
    void deleteByVariantId(UUID variantId);
    
    /**
     * Save all inventory.
     */
    List<Inventory> saveAll(Iterable<Inventory> inventories);
    
    /**
     * Find all inventory by IDs.
     */
    List<Inventory> findAllById(Iterable<UUID> ids);
    
    /**
     * Get inventory count.
     */
    long count();
}
