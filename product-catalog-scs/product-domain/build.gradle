plugins {
    id 'java-library'
}

description = 'Product Catalog Domain Layer - Core business logic and entities'

dependencies {
    // JPA for entities
    api 'org.springframework.boot:spring-boot-starter-data-jpa'
    api 'org.springframework.boot:spring-boot-starter-validation'
    
    // Common utilities
    api 'org.terasoluna.gfw:terasoluna-gfw-common'
    api 'org.apache.commons:commons-lang3'
    
    // Testing
    testImplementation 'com.h2database:h2'
}
