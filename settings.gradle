rootProject.name = 'ndvn-terasoluna-base'

// Include all modules
include 'ndvn-terasoluna-base-domain'
include 'ndvn-terasoluna-base-infrastructure'
include 'ndvn-terasoluna-base-application'
include 'ndvn-terasoluna-base-web'

// Set project directories
project(':ndvn-terasoluna-base-domain').projectDir = file('ndvn-terasoluna-base-domain')
project(':ndvn-terasoluna-base-infrastructure').projectDir = file('ndvn-terasoluna-base-infrastructure')
project(':ndvn-terasoluna-base-application').projectDir = file('ndvn-terasoluna-base-application')
project(':ndvn-terasoluna-base-web').projectDir = file('ndvn-terasoluna-base-web')
