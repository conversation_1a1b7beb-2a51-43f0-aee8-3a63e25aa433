-- Customer Management Database Initialization Script
-- This script creates the initial schema for the Customer Management SCS

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Customers table
CREATE TABLE customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_number VARCHAR(50) UNIQUE NOT NULL,
    user_id UUID, -- Reference to user management (no FK constraint for loose coupling)
    customer_type VARCHAR(20) DEFAULT 'INDIVIDUAL' CHECK (customer_type IN ('INDIVIDUAL', 'BUSINESS')),
    company_name VARCHAR(255),
    first_name VARCHAR(100),
    last_name VA<PERSON>HA<PERSON>(100),
    email VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    date_of_birth DATE,
    tax_id VARCHAR(50),
    status VARCHAR(20) DEFAULT 'ACTIVE' CHECK (status IN ('ACTIVE', 'INACTIVE', 'SUSPENDED', 'ARCHIVED')),
    classification VARCHAR(50) DEFAULT 'STANDARD',
    credit_limit DECIMAL(15,2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer addresses table
CREATE TABLE customer_addresses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    address_type VARCHAR(20) DEFAULT 'BILLING' CHECK (address_type IN ('BILLING', 'SHIPPING', 'MAILING', 'OTHER')),
    street_address VARCHAR(255) NOT NULL,
    address_line_2 VARCHAR(255),
    city VARCHAR(100) NOT NULL,
    state VARCHAR(50),
    postal_code VARCHAR(20) NOT NULL,
    country VARCHAR(50) NOT NULL DEFAULT 'VN',
    is_primary BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer contacts table
CREATE TABLE customer_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    contact_type VARCHAR(20) DEFAULT 'EMAIL' CHECK (contact_type IN ('EMAIL', 'PHONE', 'MOBILE', 'FAX', 'WEBSITE')),
    contact_value VARCHAR(255) NOT NULL,
    is_primary BOOLEAN DEFAULT false,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer preferences table
CREATE TABLE customer_preferences (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    preference_key VARCHAR(100) NOT NULL,
    preference_value TEXT,
    preference_type VARCHAR(20) DEFAULT 'STRING' CHECK (preference_type IN ('STRING', 'BOOLEAN', 'NUMBER', 'JSON')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(customer_id, preference_key)
);

-- Customer segments table
CREATE TABLE customer_segments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    criteria JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer segment assignments table
CREATE TABLE customer_segment_assignments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    segment_id UUID REFERENCES customer_segments(id) ON DELETE CASCADE,
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_by VARCHAR(100),
    UNIQUE(customer_id, segment_id)
);

-- Customer notes table
CREATE TABLE customer_notes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE CASCADE,
    note_type VARCHAR(50) DEFAULT 'GENERAL',
    subject VARCHAR(255),
    content TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT true,
    created_by VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Customer audit log table
CREATE TABLE customer_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_id UUID REFERENCES customers(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    old_values JSONB,
    new_values JSONB,
    changed_by VARCHAR(100),
    ip_address INET,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Indexes for performance
CREATE INDEX idx_customers_customer_number ON customers(customer_number);
CREATE INDEX idx_customers_email ON customers(email);
CREATE INDEX idx_customers_user_id ON customers(user_id);
CREATE INDEX idx_customers_status ON customers(status);
CREATE INDEX idx_customers_classification ON customers(classification);
CREATE INDEX idx_customers_created_at ON customers(created_at);

CREATE INDEX idx_customer_addresses_customer_id ON customer_addresses(customer_id);
CREATE INDEX idx_customer_addresses_type ON customer_addresses(address_type);
CREATE INDEX idx_customer_addresses_primary ON customer_addresses(is_primary);

CREATE INDEX idx_customer_contacts_customer_id ON customer_contacts(customer_id);
CREATE INDEX idx_customer_contacts_type ON customer_contacts(contact_type);
CREATE INDEX idx_customer_contacts_primary ON customer_contacts(is_primary);

CREATE INDEX idx_customer_preferences_customer_id ON customer_preferences(customer_id);
CREATE INDEX idx_customer_preferences_key ON customer_preferences(preference_key);

CREATE INDEX idx_customer_segments_active ON customer_segments(is_active);
CREATE INDEX idx_customer_segment_assignments_customer_id ON customer_segment_assignments(customer_id);
CREATE INDEX idx_customer_segment_assignments_segment_id ON customer_segment_assignments(segment_id);

CREATE INDEX idx_customer_notes_customer_id ON customer_notes(customer_id);
CREATE INDEX idx_customer_notes_type ON customer_notes(note_type);
CREATE INDEX idx_customer_notes_created_at ON customer_notes(created_at);

CREATE INDEX idx_customer_audit_log_customer_id ON customer_audit_log(customer_id);
CREATE INDEX idx_customer_audit_log_created_at ON customer_audit_log(created_at);

-- Insert default customer segments
INSERT INTO customer_segments (name, description, criteria) VALUES
    ('VIP', 'Very Important Person - High value customers', '{"credit_limit": {"min": 100000}, "status": "ACTIVE"}'),
    ('Premium', 'Premium customers with elevated privileges', '{"credit_limit": {"min": 50000}, "status": "ACTIVE"}'),
    ('Standard', 'Standard customers', '{"status": "ACTIVE"}'),
    ('New', 'Newly registered customers', '{"days_since_registration": {"max": 30}}');

-- Insert default customer preferences
INSERT INTO customer_preferences (customer_id, preference_key, preference_value, preference_type)
SELECT c.id, 'email_notifications', 'true', 'BOOLEAN'
FROM customers c
WHERE NOT EXISTS (
    SELECT 1 FROM customer_preferences cp 
    WHERE cp.customer_id = c.id AND cp.preference_key = 'email_notifications'
);

-- Function to generate customer number
CREATE OR REPLACE FUNCTION generate_customer_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- Get the next sequence number
    SELECT COALESCE(MAX(CAST(SUBSTRING(customer_number FROM 5) AS INTEGER)), 0) + 1
    INTO counter
    FROM customers
    WHERE customer_number ~ '^CUST[0-9]+$';
    
    -- Format as CUST followed by 6-digit number
    new_number := 'CUST' || LPAD(counter::TEXT, 6, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically generate customer number
CREATE OR REPLACE FUNCTION set_customer_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.customer_number IS NULL OR NEW.customer_number = '' THEN
        NEW.customer_number := generate_customer_number();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_customer_number_trigger
    BEFORE INSERT ON customers
    FOR EACH ROW
    EXECUTE FUNCTION set_customer_number();

-- Triggers to automatically update updated_at
CREATE TRIGGER update_customers_updated_at BEFORE UPDATE ON customers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_addresses_updated_at BEFORE UPDATE ON customer_addresses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_contacts_updated_at BEFORE UPDATE ON customer_contacts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_preferences_updated_at BEFORE UPDATE ON customer_preferences
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_customer_segments_updated_at BEFORE UPDATE ON customer_segments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to ensure only one primary address per type per customer
CREATE OR REPLACE FUNCTION ensure_single_primary_address()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_primary = true THEN
        UPDATE customer_addresses 
        SET is_primary = false 
        WHERE customer_id = NEW.customer_id 
          AND address_type = NEW.address_type 
          AND id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER ensure_single_primary_address_trigger
    BEFORE INSERT OR UPDATE ON customer_addresses
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_primary_address();

-- Function to ensure only one primary contact per type per customer
CREATE OR REPLACE FUNCTION ensure_single_primary_contact()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_primary = true THEN
        UPDATE customer_contacts 
        SET is_primary = false 
        WHERE customer_id = NEW.customer_id 
          AND contact_type = NEW.contact_type 
          AND id != NEW.id;
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER ensure_single_primary_contact_trigger
    BEFORE INSERT OR UPDATE ON customer_contacts
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_primary_contact();

-- Grant permissions to service user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO customer_service;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO customer_service;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO customer_service;
