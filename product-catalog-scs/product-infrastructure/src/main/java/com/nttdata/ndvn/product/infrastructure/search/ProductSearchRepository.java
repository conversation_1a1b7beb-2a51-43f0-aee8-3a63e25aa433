package com.nttdata.ndvn.product.infrastructure.search;

import com.nttdata.ndvn.product.domain.model.ProductStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Elasticsearch repository for product search operations.
 * 
 * This repository provides advanced search capabilities for products
 * using Elasticsearch's full-text search features.
 */
@Repository
public interface ProductSearchRepository extends ElasticsearchRepository<ProductSearchDocument, String> {
    
    /**
     * Find products by name containing search term.
     */
    Page<ProductSearchDocument> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find products by description containing search term.
     */
    Page<ProductSearchDocument> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find products by SKU.
     */
    Page<ProductSearchDocument> findBySkuContainingIgnoreCase(String sku, Pageable pageable);
    
    /**
     * Find products by status.
     */
    Page<ProductSearchDocument> findByStatus(ProductStatus status, Pageable pageable);
    
    /**
     * Find products by category ID.
     */
    Page<ProductSearchDocument> findByCategoryId(String categoryId, Pageable pageable);
    
    /**
     * Find products by brand ID.
     */
    Page<ProductSearchDocument> findByBrandId(String brandId, Pageable pageable);
    
    /**
     * Find featured products.
     */
    Page<ProductSearchDocument> findByFeatured(boolean featured, Pageable pageable);
    
    /**
     * Find products on sale.
     */
    Page<ProductSearchDocument> findByOnSale(boolean onSale, Pageable pageable);
    
    /**
     * Find products in stock.
     */
    Page<ProductSearchDocument> findByInStock(boolean inStock, Pageable pageable);
    
    /**
     * Find products by price range.
     */
    Page<ProductSearchDocument> findByEffectivePriceBetween(BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    /**
     * Find products by category path (hierarchical search).
     */
    Page<ProductSearchDocument> findByCategoryPathContaining(String categoryPath, Pageable pageable);
    
    /**
     * Find products by brand name.
     */
    Page<ProductSearchDocument> findByBrandNameContainingIgnoreCase(String brandName, Pageable pageable);
    
    /**
     * Find products by category name.
     */
    Page<ProductSearchDocument> findByCategoryNameContainingIgnoreCase(String categoryName, Pageable pageable);
    
    /**
     * Find digital products.
     */
    Page<ProductSearchDocument> findByDigital(boolean digital, Pageable pageable);
    
    /**
     * Advanced full-text search across multiple fields.
     */
    @Query("{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\", \"sku^2\", \"brandName\", \"categoryName\", \"attributes\", \"metaKeywords\"]}}")
    Page<ProductSearchDocument> searchProducts(String searchTerm, Pageable pageable);
    
    /**
     * Search products with filters.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\", \"sku^2\", \"brandName\", \"categoryName\"]}}], \"filter\": [{\"term\": {\"status\": \"ACTIVE\"}}]}}")
    Page<ProductSearchDocument> searchActiveProducts(String searchTerm, Pageable pageable);
    
    /**
     * Search products by category with text search.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\"]}}, {\"term\": {\"categoryId\": \"?1\"}}]}}")
    Page<ProductSearchDocument> searchProductsInCategory(String searchTerm, String categoryId, Pageable pageable);
    
    /**
     * Search products by brand with text search.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\"]}}, {\"term\": {\"brandId\": \"?1\"}}]}}")
    Page<ProductSearchDocument> searchProductsByBrand(String searchTerm, String brandId, Pageable pageable);
    
    /**
     * Search products with price filter.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\"]}}], \"filter\": [{\"range\": {\"effectivePrice\": {\"gte\": ?1, \"lte\": ?2}}}]}}")
    Page<ProductSearchDocument> searchProductsWithPriceRange(String searchTerm, BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    /**
     * Search featured products.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\"]}}], \"filter\": [{\"term\": {\"featured\": true}}]}}")
    Page<ProductSearchDocument> searchFeaturedProducts(String searchTerm, Pageable pageable);
    
    /**
     * Search products on sale.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\"]}}], \"filter\": [{\"term\": {\"onSale\": true}}]}}")
    Page<ProductSearchDocument> searchProductsOnSale(String searchTerm, Pageable pageable);
    
    /**
     * Search products in stock.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\"]}}], \"filter\": [{\"term\": {\"inStock\": true}}]}}")
    Page<ProductSearchDocument> searchProductsInStock(String searchTerm, Pageable pageable);
    
    /**
     * Suggest products based on partial input.
     */
    @Query("{\"bool\": {\"should\": [{\"prefix\": {\"name\": \"?0\"}}, {\"prefix\": {\"sku\": \"?0\"}}, {\"prefix\": {\"brandName\": \"?0\"}}]}}")
    Page<ProductSearchDocument> suggestProducts(String partialInput, Pageable pageable);
    
    /**
     * Find similar products based on category and brand.
     */
    @Query("{\"bool\": {\"should\": [{\"term\": {\"categoryId\": \"?0\"}}, {\"term\": {\"brandId\": \"?1\"}}], \"must_not\": [{\"term\": {\"_id\": \"?2\"}}]}}")
    Page<ProductSearchDocument> findSimilarProducts(String categoryId, String brandId, String excludeProductId, Pageable pageable);
    
    /**
     * Advanced search with multiple filters.
     */
    @Query("{\"bool\": {\"must\": [{\"multi_match\": {\"query\": \"?0\", \"fields\": [\"name^3\", \"description^2\", \"longDescription\", \"sku^2\"]}}], \"filter\": [" +
           "{\"terms\": {\"categoryId\": ?1}}, " +
           "{\"terms\": {\"brandId\": ?2}}, " +
           "{\"range\": {\"effectivePrice\": {\"gte\": ?3, \"lte\": ?4}}}, " +
           "{\"term\": {\"status\": \"ACTIVE\"}}" +
           "]}}")
    Page<ProductSearchDocument> advancedSearch(String searchTerm, List<String> categoryIds, List<String> brandIds, 
                                              BigDecimal minPrice, BigDecimal maxPrice, Pageable pageable);
    
    /**
     * Search products by attributes.
     */
    @Query("{\"bool\": {\"must\": [{\"terms\": {\"attributes\": ?0}}]}}")
    Page<ProductSearchDocument> findByAttributes(List<String> attributes, Pageable pageable);
    
    /**
     * Search products by tags.
     */
    @Query("{\"bool\": {\"must\": [{\"terms\": {\"tags\": ?0}}]}}")
    Page<ProductSearchDocument> findByTags(List<String> tags, Pageable pageable);
    
    /**
     * Find products by weight range.
     */
    Page<ProductSearchDocument> findByWeightBetween(Double minWeight, Double maxWeight, Pageable pageable);
    
    /**
     * Find recently added products.
     */
    @Query("{\"bool\": {\"filter\": [{\"range\": {\"createdAt\": {\"gte\": \"now-30d\"}}}], \"sort\": [{\"createdAt\": {\"order\": \"desc\"}}]}}")
    Page<ProductSearchDocument> findRecentProducts(Pageable pageable);
    
    /**
     * Find recently updated products.
     */
    @Query("{\"bool\": {\"filter\": [{\"range\": {\"updatedAt\": {\"gte\": \"now-7d\"}}}], \"sort\": [{\"updatedAt\": {\"order\": \"desc\"}}]}}")
    Page<ProductSearchDocument> findRecentlyUpdatedProducts(Pageable pageable);
    
    /**
     * Find products by availability.
     */
    Page<ProductSearchDocument> findByAvailableQuantityGreaterThan(Integer quantity, Pageable pageable);
    
    /**
     * Count products by category.
     */
    long countByCategoryId(String categoryId);
    
    /**
     * Count products by brand.
     */
    long countByBrandId(String brandId);
    
    /**
     * Count products by status.
     */
    long countByStatus(ProductStatus status);
    
    /**
     * Count featured products.
     */
    long countByFeatured(boolean featured);
    
    /**
     * Count products on sale.
     */
    long countByOnSale(boolean onSale);
    
    /**
     * Count products in stock.
     */
    long countByInStock(boolean inStock);
}
