package com.nttdata.ndvn.customer.infrastructure.repository;

import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import com.nttdata.ndvn.customer.domain.repository.CustomerRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * JPA implementation of CustomerRepository.
 * 
 * This repository provides data access operations for Customer entities using Spring Data JPA,
 * implementing the domain repository interface to maintain clean architecture boundaries.
 */
@Repository
public interface JpaCustomerRepository extends JpaRepository<Customer, UUID>, CustomerRepository {
    
    @Override
    Optional<Customer> findByCustomerNumber(String customerNumber);
    
    @Override
    Optional<Customer> findByEmail(String email);
    
    @Override
    Optional<Customer> findByUserId(UUID userId);
    
    @Override
    Page<Customer> findByStatus(CustomerStatus status, Pageable pageable);
    
    @Override
    Page<Customer> findByCustomerType(CustomerType customerType, Pageable pageable);
    
    @Override
    Page<Customer> findByClassification(String classification, Pageable pageable);
    
    @Override
    @Query("SELECT c FROM Customer c WHERE " +
           "LOWER(c.firstName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.lastName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.companyName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "c.customerNumber LIKE CONCAT('%', :searchTerm, '%')")
    Page<Customer> searchCustomers(@Param("searchTerm") String searchTerm, Pageable pageable);
    
    @Override
    Page<Customer> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    @Override
    @Query("SELECT c FROM Customer c WHERE LOWER(c.email) LIKE LOWER(CONCAT('%@', :domain))")
    Page<Customer> findByEmailDomain(@Param("domain") String domain, Pageable pageable);
    
    @Override
    boolean existsByCustomerNumber(String customerNumber);
    
    @Override
    boolean existsByEmail(String email);
    
    @Override
    boolean existsByUserId(UUID userId);
    
    @Override
    long countByStatus(CustomerStatus status);
    
    @Override
    long countByCustomerType(CustomerType customerType);
    
    @Override
    long countByCreatedAtAfter(LocalDateTime createdAfter);
    
    @Override
    @Query("SELECT c FROM Customer c JOIN c.segmentAssignments sa JOIN sa.segment s WHERE s.name = :segmentName")
    Page<Customer> findBySegmentName(@Param("segmentName") String segmentName, Pageable pageable);
    
    @Override
    @Query("SELECT c FROM Customer c WHERE c.id NOT IN " +
           "(SELECT DISTINCT sa.customer.id FROM CustomerSegmentAssignment sa)")
    Page<Customer> findCustomersWithoutSegments(Pageable pageable);
    
    @Override
    @Query("SELECT c FROM Customer c WHERE c.customerType = 'BUSINESS'")
    Page<Customer> findBusinessCustomers(Pageable pageable);
    
    @Override
    @Query("SELECT c FROM Customer c WHERE c.customerType = 'INDIVIDUAL'")
    Page<Customer> findIndividualCustomers(Pageable pageable);
    
    // Additional JPA-specific queries
    
    /**
     * Find customers by phone number.
     */
    Optional<Customer> findByPhone(String phone);
    
    /**
     * Find customers by tax ID.
     */
    Optional<Customer> findByTaxId(String taxId);
    
    /**
     * Find customers by status and type.
     */
    Page<Customer> findByStatusAndCustomerType(CustomerStatus status, CustomerType customerType, Pageable pageable);
    
    /**
     * Find customers created between dates.
     */
    @Query("SELECT c FROM Customer c WHERE c.createdAt BETWEEN :startDate AND :endDate")
    Page<Customer> findByCreatedAtBetween(@Param("startDate") LocalDateTime startDate, 
                                        @Param("endDate") LocalDateTime endDate, 
                                        Pageable pageable);
    
    /**
     * Find customers with credit limit greater than amount.
     */
    @Query("SELECT c FROM Customer c WHERE c.creditLimit > :amount")
    Page<Customer> findByCreditLimitGreaterThan(@Param("amount") java.math.BigDecimal amount, Pageable pageable);
    
    /**
     * Find customers by classification and status.
     */
    Page<Customer> findByClassificationAndStatus(String classification, CustomerStatus status, Pageable pageable);
    
    /**
     * Count customers by classification.
     */
    long countByClassification(String classification);
    
    /**
     * Find customers updated after specific date.
     */
    Page<Customer> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find customers with addresses in specific city.
     */
    @Query("SELECT DISTINCT c FROM Customer c JOIN c.addresses a WHERE LOWER(a.city) = LOWER(:city)")
    Page<Customer> findByAddressCity(@Param("city") String city, Pageable pageable);
    
    /**
     * Find customers with addresses in specific country.
     */
    @Query("SELECT DISTINCT c FROM Customer c JOIN c.addresses a WHERE LOWER(a.country) = LOWER(:country)")
    Page<Customer> findByAddressCountry(@Param("country") String country, Pageable pageable);
    
    /**
     * Find customers with notes containing specific text.
     */
    @Query("SELECT DISTINCT c FROM Customer c JOIN c.notes n WHERE LOWER(n.content) LIKE LOWER(CONCAT('%', :text, '%'))")
    Page<Customer> findByNotesContaining(@Param("text") String text, Pageable pageable);
    
    /**
     * Find customers by first name and last name.
     */
    Page<Customer> findByFirstNameIgnoreCaseAndLastNameIgnoreCase(String firstName, String lastName, Pageable pageable);
    
    /**
     * Find customers by company name containing.
     */
    Page<Customer> findByCompanyNameContainingIgnoreCase(String companyName, Pageable pageable);
    
    /**
     * Find customers with primary addresses.
     */
    @Query("SELECT DISTINCT c FROM Customer c JOIN c.addresses a WHERE a.isPrimary = true")
    Page<Customer> findCustomersWithPrimaryAddress(Pageable pageable);
    
    /**
     * Find customers without addresses.
     */
    @Query("SELECT c FROM Customer c WHERE c.addresses IS EMPTY")
    Page<Customer> findCustomersWithoutAddresses(Pageable pageable);
}
