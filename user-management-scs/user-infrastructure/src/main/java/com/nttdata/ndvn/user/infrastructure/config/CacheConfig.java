package com.nttdata.ndvn.user.infrastructure.config;

import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Cache configuration for User Management SCS.
 * 
 * This configuration sets up Redis-based caching with different TTL policies
 * for various types of data to optimize performance and reduce database load.
 */
@Configuration
@EnableCaching
public class CacheConfig {
    
    // Cache names
    public static final String USER_CACHE = "users";
    public static final String USER_BY_USERNAME_CACHE = "users-by-username";
    public static final String USER_BY_EMAIL_CACHE = "users-by-email";
    public static final String ROLE_CACHE = "roles";
    public static final String ROLE_BY_NAME_CACHE = "roles-by-name";
    public static final String PERMISSION_CACHE = "permissions";
    public static final String USER_PERMISSIONS_CACHE = "user-permissions";
    public static final String USER_ROLES_CACHE = "user-roles";
    public static final String SESSION_CACHE = "user-sessions";
    public static final String AUTHENTICATION_CACHE = "authentication";
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory redisConnectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(10))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()))
                .disableCachingNullValues();
        
        Map<String, RedisCacheConfiguration> cacheConfigurations = new HashMap<>();
        
        // User cache - 15 minutes TTL
        cacheConfigurations.put(USER_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(15)));
        cacheConfigurations.put(USER_BY_USERNAME_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(15)));
        cacheConfigurations.put(USER_BY_EMAIL_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(15)));
        
        // Role cache - 30 minutes TTL (roles change less frequently)
        cacheConfigurations.put(ROLE_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(30)));
        cacheConfigurations.put(ROLE_BY_NAME_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // Permission cache - 1 hour TTL (permissions change rarely)
        cacheConfigurations.put(PERMISSION_CACHE, defaultConfig.entryTtl(Duration.ofHours(1)));
        
        // User permissions and roles - 20 minutes TTL
        cacheConfigurations.put(USER_PERMISSIONS_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(20)));
        cacheConfigurations.put(USER_ROLES_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(20)));
        
        // Session cache - 30 minutes TTL
        cacheConfigurations.put(SESSION_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(30)));
        
        // Authentication cache - 5 minutes TTL (for failed attempts tracking)
        cacheConfigurations.put(AUTHENTICATION_CACHE, defaultConfig.entryTtl(Duration.ofMinutes(5)));
        
        return RedisCacheManager.builder(redisConnectionFactory)
                .cacheDefaults(defaultConfig)
                .withInitialCacheConfigurations(cacheConfigurations)
                .build();
    }
}
