package com.nttdata.ndvn.customer.events;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import java.time.LocalDateTime;
import java.util.UUID;

public class CustomerUpdatedEvent {
    private UUID eventId;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime eventTime;
    private UUID customerId;
    private String customerNumber;
    private CustomerType customerType;
    private String email;
    private String displayName;
    private CustomerStatus status;
    private String classification;
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss")
    private LocalDateTime updatedAt;

    public CustomerUpdatedEvent() {}

    private CustomerUpdatedEvent(Builder builder) {
        this.eventId = builder.eventId;
        this.eventTime = builder.eventTime;
        this.customerId = builder.customerId;
        this.customerNumber = builder.customerNumber;
        this.customerType = builder.customerType;
        this.email = builder.email;
        this.displayName = builder.displayName;
        this.status = builder.status;
        this.classification = builder.classification;
        this.updatedAt = builder.updatedAt;
    }

    public static Builder builder() { return new Builder(); }

    public static class Builder {
        private UUID eventId;
        private LocalDateTime eventTime;
        private UUID customerId;
        private String customerNumber;
        private CustomerType customerType;
        private String email;
        private String displayName;
        private CustomerStatus status;
        private String classification;
        private LocalDateTime updatedAt;

        public Builder eventId(UUID eventId) { this.eventId = eventId; return this; }
        public Builder eventTime(LocalDateTime eventTime) { this.eventTime = eventTime; return this; }
        public Builder customerId(UUID customerId) { this.customerId = customerId; return this; }
        public Builder customerNumber(String customerNumber) { this.customerNumber = customerNumber; return this; }
        public Builder customerType(CustomerType customerType) { this.customerType = customerType; return this; }
        public Builder email(String email) { this.email = email; return this; }
        public Builder displayName(String displayName) { this.displayName = displayName; return this; }
        public Builder status(CustomerStatus status) { this.status = status; return this; }
        public Builder classification(String classification) { this.classification = classification; return this; }
        public Builder updatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; return this; }
        public CustomerUpdatedEvent build() { return new CustomerUpdatedEvent(this); }
    }

    // Getters and setters
    public UUID getEventId() { return eventId; }
    public void setEventId(UUID eventId) { this.eventId = eventId; }
    public LocalDateTime getEventTime() { return eventTime; }
    public void setEventTime(LocalDateTime eventTime) { this.eventTime = eventTime; }
    public UUID getCustomerId() { return customerId; }
    public void setCustomerId(UUID customerId) { this.customerId = customerId; }
    public String getCustomerNumber() { return customerNumber; }
    public void setCustomerNumber(String customerNumber) { this.customerNumber = customerNumber; }
    public CustomerType getCustomerType() { return customerType; }
    public void setCustomerType(CustomerType customerType) { this.customerType = customerType; }
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    public CustomerStatus getStatus() { return status; }
    public void setStatus(CustomerStatus status) { this.status = status; }
    public String getClassification() { return classification; }
    public void setClassification(String classification) { this.classification = classification; }
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
