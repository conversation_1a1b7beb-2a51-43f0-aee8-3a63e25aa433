package com.nttdata.ndvn.user.domain.repository;

import com.nttdata.ndvn.user.domain.model.Role;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;

/**
 * Repository interface for Role entity.
 * 
 * This interface defines the contract for persisting and retrieving Role entities
 * within the User Management bounded context.
 */
public interface RoleRepository {
    
    /**
     * Finds a role by its unique identifier.
     * 
     * @param id the role ID
     * @return an Optional containing the role if found, empty otherwise
     */
    Optional<Role> findById(UUID id);
    
    /**
     * Finds a role by its name.
     * 
     * @param name the role name
     * @return an Optional containing the role if found, empty otherwise
     */
    Optional<Role> findByName(String name);
    
    /**
     * Finds roles by their active status.
     * 
     * @param active the active status
     * @param pageable pagination information
     * @return a page of roles with the specified active status
     */
    Page<Role> findByActive(boolean active, Pageable pageable);
    
    /**
     * Finds roles by their system role status.
     * 
     * @param systemRole the system role status
     * @param pageable pagination information
     * @return a page of roles with the specified system role status
     */
    Page<Role> findBySystemRole(boolean systemRole, Pageable pageable);
    
    /**
     * Finds roles by name containing the specified text (case-insensitive).
     * 
     * @param name the text to search for in role names
     * @param pageable pagination information
     * @return a page of roles matching the search criteria
     */
    Page<Role> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Finds roles that have a specific permission.
     * 
     * @param permissionName the name of the permission
     * @param pageable pagination information
     * @return a page of roles with the specified permission
     */
    Page<Role> findByPermissionsName(String permissionName, Pageable pageable);
    
    /**
     * Finds all roles with pagination.
     * 
     * @param pageable pagination information
     * @return a page of all roles
     */
    Page<Role> findAll(Pageable pageable);
    
    /**
     * Finds roles by their names.
     * 
     * @param names the role names
     * @return a list of roles with the specified names
     */
    List<Role> findByNameIn(Set<String> names);
    
    /**
     * Saves a role entity.
     * 
     * @param role the role to save
     * @return the saved role
     */
    Role save(Role role);
    
    /**
     * Deletes a role entity.
     * 
     * @param role the role to delete
     */
    void delete(Role role);
    
    /**
     * Deletes a role by its ID.
     * 
     * @param id the role ID
     */
    void deleteById(UUID id);
    
    /**
     * Checks if a role exists with the specified name.
     * 
     * @param name the role name to check
     * @return true if a role exists with the name, false otherwise
     */
    boolean existsByName(String name);
    
    /**
     * Checks if a role exists with the specified ID.
     * 
     * @param id the role ID to check
     * @return true if a role exists with the ID, false otherwise
     */
    boolean existsById(UUID id);
    
    /**
     * Counts the total number of roles.
     * 
     * @return the total number of roles
     */
    long count();
    
    /**
     * Counts roles by their active status.
     * 
     * @param active the active status
     * @return the number of roles with the specified active status
     */
    long countByActive(boolean active);
    
    /**
     * Counts roles by their system role status.
     * 
     * @param systemRole the system role status
     * @return the number of roles with the specified system role status
     */
    long countBySystemRole(boolean systemRole);
    
    /**
     * Finds roles that can be deleted (non-system roles with no assigned users).
     * 
     * @param pageable pagination information
     * @return a page of roles that can be deleted
     */
    Page<Role> findDeletableRoles(Pageable pageable);
    
    /**
     * Batch saves multiple roles.
     *
     * @param roles the roles to save
     * @return the saved roles
     */
    List<Role> saveAllRoles(Iterable<Role> roles);
    
    /**
     * Flushes any pending changes to the database.
     */
    void flush();
}
