# Customer Management SCS

A Self-Contained System (SCS) for comprehensive customer lifecycle management, built with Spring Boot and following Domain-Driven Design principles.

## 🏗️ Architecture Overview

This Customer Management SCS implements a clean, layered architecture with clear separation of concerns:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Layer     │    │  Events Layer   │    │ Infrastructure  │
│   (REST API)    │    │   (Kafka)       │    │   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Application     │    │    Domain       │    │ Infrastructure  │
│   Services      │◄──►│    Services     │◄──►│  Repositories   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 📦 Module Structure

- **customer-domain**: Core business logic, entities, and domain services
- **customer-infrastructure**: Data access, caching, and external integrations
- **customer-application**: Application services, DTOs, and use case orchestration
- **customer-events**: Event publishing and domain event definitions
- **customer-web**: REST API controllers, security, and web configuration

## 🚀 Key Features

### Customer Lifecycle Management
- ✅ Customer registration and onboarding
- ✅ Profile management (individual and business customers)
- ✅ Status management (active, inactive, suspended, archived)
- ✅ Customer classification and credit limit management

### Address Management
- ✅ Multiple addresses per customer
- ✅ Address types (billing, shipping, mailing)
- ✅ Primary address designation
- ✅ Address validation and formatting

### Customer Segmentation
- ✅ Dynamic customer segments with JSON criteria
- ✅ Automatic and manual segment assignment
- ✅ Segment-based customer analytics

### Notes and Communication
- ✅ Customer notes and comments
- ✅ Internal and external note types
- ✅ Audit trail for customer interactions

### Event-Driven Architecture
- ✅ Domain events for customer lifecycle changes
- ✅ Kafka integration for inter-service communication
- ✅ Event sourcing capabilities

## 🛠️ Technology Stack

- **Framework**: Spring Boot 3.4.1
- **Language**: Java 21
- **Database**: PostgreSQL 15
- **Caching**: Redis
- **Messaging**: Apache Kafka
- **Security**: OAuth 2.0 JWT (Keycloak)
- **Service Discovery**: Consul
- **Monitoring**: Prometheus, Jaeger
- **Documentation**: OpenAPI 3.0 (Swagger)
- **Build Tool**: Gradle 8.5

## 📋 Prerequisites

- Java 21+
- Docker and Docker Compose
- PostgreSQL 15+
- Redis 7+
- Apache Kafka 3.5+
- Keycloak 23+
- Consul 1.16+

## 🚀 Quick Start

### 1. Start Infrastructure Services

```bash
# Start all required infrastructure services
cd ../infrastructure
docker-compose up -d postgres redis kafka keycloak consul
```

### 2. Build the Application

```bash
# Build all modules
./gradlew clean build

# Build without tests (faster)
./gradlew clean build -x test
```

### 3. Run the Service

```bash
# Run with local profile
./gradlew :customer-web:bootRun

# Or run with specific profile
./gradlew :customer-web:bootRun --args='--spring.profiles.active=docker'
```

### 4. Verify the Service

```bash
# Check health
curl http://localhost:8082/actuator/health

# Access API documentation
open http://localhost:8082/swagger-ui.html
```

## 📚 API Documentation

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/v1/customers` | Create new customer |
| GET | `/api/v1/customers/{id}` | Get customer by ID |
| GET | `/api/v1/customers/number/{number}` | Get customer by number |
| GET | `/api/v1/customers/email/{email}` | Get customer by email |
| GET | `/api/v1/customers` | List all customers |
| GET | `/api/v1/customers/search?q={term}` | Search customers |
| PUT | `/api/v1/customers/{id}` | Update customer |
| PATCH | `/api/v1/customers/{id}/status` | Update customer status |
| DELETE | `/api/v1/customers/{id}` | Delete customer |

### Authentication

All protected endpoints require OAuth 2.0 JWT tokens with appropriate scopes:

- `customer:read` - Read customer data
- `customer:write` - Create and update customers
- `customer:delete` - Delete customers

### Example Usage

```bash
# Get JWT token from Keycloak
TOKEN=$(curl -s -X POST "http://localhost:8090/realms/ndvn/protocol/openid-connect/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=admin&password=admin123&grant_type=password&client_id=api-gateway&client_secret=api-gateway-secret" \
  | jq -r '.access_token')

# Create a new customer
curl -X POST "http://localhost:8082/api/v1/customers" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "customerType": "INDIVIDUAL",
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+84-90-123-4567",
    "status": "ACTIVE"
  }'

# Search customers
curl -X GET "http://localhost:8082/api/v1/customers/search?q=john" \
  -H "Authorization: Bearer $TOKEN"
```

## 🗄️ Database Schema

### Core Tables

- **customers**: Main customer entity with profile information
- **customer_addresses**: Customer address information
- **customer_notes**: Customer notes and comments
- **customer_segments**: Customer segmentation definitions
- **customer_segment_assignments**: Customer-to-segment relationships

### Sample Data

The service includes sample data for testing:
- 10 sample customers (5 business, 5 individual)
- Default customer segments (VIP, Enterprise, SME, Retail, etc.)
- Sample addresses and notes
- Pre-configured segment assignments

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `SPRING_PROFILES_ACTIVE` | Active Spring profile | `local` |
| `DATABASE_URL` | PostgreSQL connection URL | `****************************************************` |
| `REDIS_HOST` | Redis host | `localhost` |
| `KAFKA_BOOTSTRAP_SERVERS` | Kafka bootstrap servers | `localhost:9092` |
| `KEYCLOAK_ISSUER_URI` | Keycloak issuer URI | `http://localhost:8090/realms/ndvn` |

### Profiles

- **local**: Development with local services
- **docker**: Docker environment with container networking
- **test**: Testing with in-memory databases

## 📊 Monitoring and Observability

### Health Checks
- **Endpoint**: `/actuator/health`
- **Checks**: Database, Redis, Kafka connectivity

### Metrics
- **Endpoint**: `/actuator/prometheus`
- **Metrics**: JVM, HTTP requests, database connections, cache hit rates

### Distributed Tracing
- **Provider**: Jaeger
- **Sampling**: 100% (configurable)
- **Correlation**: Request correlation IDs

## 🧪 Testing

```bash
# Run all tests
./gradlew test

# Run tests with coverage
./gradlew test jacocoTestReport

# Run integration tests
./gradlew integrationTest
```

## 🔒 Security

### Authentication
- OAuth 2.0 Resource Server
- JWT token validation
- Keycloak integration

### Authorization
- Method-level security with `@PreAuthorize`
- Scope-based access control
- Role-based permissions

### Data Protection
- Input validation and sanitization
- SQL injection prevention
- XSS protection

## 🚀 Deployment

### Docker

```bash
# Build Docker image
./gradlew bootBuildImage

# Run with Docker Compose
docker-compose up customer-management-service
```

### Kubernetes

```yaml
# Example Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: customer-management-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: customer-management-service
  template:
    metadata:
      labels:
        app: customer-management-service
    spec:
      containers:
      - name: customer-management-service
        image: customer-management-service:latest
        ports:
        - containerPort: 8082
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "kubernetes"
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 Support

For support and questions:
- **Email**: <EMAIL>
- **Documentation**: [Internal Wiki](http://wiki.nttdata.com/customer-management)
- **Issues**: [GitHub Issues](https://github.com/nttdata/customer-management-scs/issues)
