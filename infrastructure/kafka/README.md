# Kafka Infrastructure Setup

This directory contains the Apache Kafka infrastructure setup for the NDVN SCS platform, providing event-driven communication between Self-Contained Systems.

## Overview

The Kafka infrastructure includes:
- **Apache Kafka** - Message broker for event streaming
- **Zookeeper** - Coordination service for Kafka
- **Schema Registry** - Avro schema management
- **Kafka Connect** - Data integration platform
- **Kafka UI** - Web-based management interface

## Quick Start

### Prerequisites
- Docker and Docker Compose installed
- At least 4GB of available RAM
- Ports 2181, 8080, 8081, 8083, 9092, 9101 available

### Start Kafka Infrastructure

```bash
# Make setup script executable (if not already)
chmod +x scripts/setup-kafka.sh

# Start all Kafka services
./scripts/setup-kafka.sh start
```

This will:
1. Create Docker network `ndvn-scs-network`
2. Start Zookeeper, Kafka, Schema Registry, Kafka Connect, and Kafka UI
3. Create all required topics for SCS services
4. Register Avro schemas
5. Verify the setup

### Access Points

Once started, you can access:
- **Kafka UI**: http://localhost:8080 - Web interface for managing Kafka
- **Schema Registry**: http://localhost:8081 - REST API for schema management
- **Kafka Bootstrap Servers**: localhost:9092 - For client connections

### Other Commands

```bash
# Stop Kafka infrastructure
./scripts/setup-kafka.sh stop

# Restart Kafka infrastructure
./scripts/setup-kafka.sh restart

# Show status of all services
./scripts/setup-kafka.sh status

# Verify setup and list topics/schemas
./scripts/setup-kafka.sh verify

# Clean up all data and containers (WARNING: destructive)
./scripts/setup-kafka.sh cleanup
```

## Topics Structure

### Event Topics
Each SCS service has dedicated event topics:

| Service | Topic | Partitions | Retention | Purpose |
|---------|-------|------------|-----------|---------|
| User Management | `user.events` | 3 | 30 days | User lifecycle events |
| Customer Management | `customer.events` | 3 | 30 days | Customer lifecycle events |
| Product Catalog | `catalog.events` | 3 | 30 days | Product and catalog events |
| Product Catalog | `inventory.events` | 3 | 14 days | Inventory level changes |
| Order Management | `order.events` | 3 | 90 days | Order lifecycle events |
| Notification | `notification.events` | 3 | 30 days | Notification delivery events |

### Command Topics
For request/response patterns:

| Service | Topic | Partitions | Retention | Purpose |
|---------|-------|------------|-----------|---------|
| User Management | `user.commands` | 3 | 1 day | User management commands |
| Customer Management | `customer.commands` | 3 | 1 day | Customer management commands |
| Product Catalog | `catalog.commands` | 3 | 1 day | Catalog management commands |
| Order Management | `order.commands` | 3 | 1 day | Order processing commands |
| Notification | `notification.commands` | 3 | 1 day | Notification requests |

### Dead Letter Queue Topics
For failed message handling:

| Service | Topic | Partitions | Retention | Purpose |
|---------|-------|------------|-----------|---------|
| User Management | `user.dlq` | 1 | 30 days | Failed user events |
| Customer Management | `customer.dlq` | 1 | 30 days | Failed customer events |
| Product Catalog | `catalog.dlq` | 1 | 30 days | Failed catalog events |
| Order Management | `order.dlq` | 1 | 30 days | Failed order events |
| Notification | `notification.dlq` | 1 | 30 days | Failed notification events |

### System Topics

| Topic | Partitions | Retention | Purpose |
|-------|------------|-----------|---------|
| `system.audit` | 1 | 1 year | Audit trail events |
| `system.metrics` | 1 | 7 days | System metrics and monitoring |

## Schema Management

### Avro Schemas
Event schemas are defined in the `schemas/` directory:
- `user-events.avsc` - User management event schema
- `customer-events.avsc` - Customer management event schema
- Additional schemas will be added for other services

### Schema Evolution
- Schemas support backward and forward compatibility
- Use Schema Registry for version management
- Follow Avro schema evolution best practices

### Registering New Schemas

```bash
# Register a new schema
curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" \
  --data '{"schema":"..."}' \
  http://localhost:8081/subjects/topic-name-value/versions

# List all subjects
curl http://localhost:8081/subjects

# Get latest schema for a subject
curl http://localhost:8081/subjects/user.events-value/versions/latest
```

## Configuration

### Topic Configuration
Topic settings are defined in `config/kafka-topics.properties`:
- Partition counts
- Replication factors
- Retention policies
- Compression settings

### Consumer Configuration
Default consumer settings:
- `auto.offset.reset=earliest`
- `enable.auto.commit=false` (manual commit for reliability)
- `session.timeout.ms=30000`
- `max.poll.records=500`

### Producer Configuration
Default producer settings:
- `acks=1` (wait for leader acknowledgment)
- `retries=3`
- `compression.type=snappy`
- `enable.idempotence=true`

## Monitoring

### Kafka UI Features
- Topic management and browsing
- Consumer group monitoring
- Schema registry integration
- Message inspection and publishing

### JMX Metrics
Kafka exposes JMX metrics on port 9101:
- Broker metrics
- Topic metrics
- Consumer group metrics
- Producer metrics

### Health Checks
Services include health check endpoints:
- Kafka: `kafka-broker-api-versions --bootstrap-server localhost:9092`
- Schema Registry: `curl -f http://localhost:8081/subjects`

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   ```bash
   # Check if ports are in use
   netstat -tulpn | grep -E ':(2181|8080|8081|8083|9092|9101)'
   ```

2. **Insufficient Memory**
   ```bash
   # Check Docker memory allocation
   docker system info | grep -i memory
   ```

3. **Network Issues**
   ```bash
   # Verify Docker network
   docker network ls | grep ndvn-scs-network
   ```

### Logs
View service logs:
```bash
# All services
docker-compose -f docker-compose-kafka.yml logs

# Specific service
docker-compose -f docker-compose-kafka.yml logs kafka
docker-compose -f docker-compose-kafka.yml logs schema-registry
```

### Reset Everything
If you encounter persistent issues:
```bash
# Stop and remove everything
./scripts/setup-kafka.sh cleanup

# Start fresh
./scripts/setup-kafka.sh start
```

## Production Considerations

### Security
- Enable SASL/SSL authentication
- Configure ACLs for topic access
- Use encrypted communication

### High Availability
- Deploy multiple Kafka brokers
- Use replication factor > 1
- Deploy across multiple availability zones

### Performance Tuning
- Adjust JVM heap sizes
- Configure appropriate batch sizes
- Monitor and tune consumer lag

### Backup and Recovery
- Regular topic backups
- Schema registry backups
- Disaster recovery procedures

## Integration with SCS Services

### Spring Boot Configuration
Services should use these connection properties:
```yaml
spring:
  kafka:
    bootstrap-servers: localhost:9092
    consumer:
      group-id: ${spring.application.name}
      auto-offset-reset: earliest
      enable-auto-commit: false
    producer:
      acks: 1
      retries: 3
      compression-type: snappy
```

### Event Publishing Pattern
```java
@Component
public class EventPublisher {
    private final KafkaTemplate<String, Object> kafkaTemplate;
    
    public void publishEvent(String topic, String key, Object event) {
        kafkaTemplate.send(topic, key, event);
    }
}
```

### Event Consumption Pattern
```java
@KafkaListener(topics = "user.events", groupId = "customer-service")
public void handleUserEvent(UserEvent event) {
    // Process event
}
```

This Kafka infrastructure provides a robust foundation for event-driven communication in the NDVN SCS platform.
