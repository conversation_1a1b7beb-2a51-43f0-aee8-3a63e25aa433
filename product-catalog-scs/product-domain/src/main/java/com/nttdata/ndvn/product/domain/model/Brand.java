package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Brand entity representing a product brand in the catalog system.
 * 
 * This entity manages brand information and provides brand-related business logic.
 */
@Entity
@Table(name = "brands", indexes = {
    @Index(name = "idx_brand_name", columnList = "name"),
    @Index(name = "idx_brand_slug", columnList = "slug", unique = true),
    @Index(name = "idx_brand_active", columnList = "active")
})
@Getter
@Setter
@NoArgsConstructor
public class Brand {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotBlank(message = "Brand name is required")
    @Size(max = 255, message = "Brand name must not exceed 255 characters")
    @Column(name = "name", nullable = false)
    private String name;
    
    @NotBlank(message = "Brand slug is required")
    @Size(max = 255, message = "Brand slug must not exceed 255 characters")
    @Pattern(regexp = "^[a-z0-9-]+$", message = "Slug must contain only lowercase letters, numbers, and hyphens")
    @Column(name = "slug", unique = true, nullable = false)
    private String slug;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @Size(max = 500, message = "Logo URL must not exceed 500 characters")
    @Column(name = "logo_url", length = 500)
    private String logoUrl;
    
    @Size(max = 500, message = "Website URL must not exceed 500 characters")
    @Column(name = "website_url", length = 500)
    private String websiteUrl;
    
    @Column(name = "active", nullable = false)
    private boolean active = true;
    
    @Min(value = 0, message = "Sort order must be non-negative")
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Size(max = 500, message = "Meta title must not exceed 500 characters")
    @Column(name = "meta_title", length = 500)
    private String metaTitle;
    
    @Size(max = 1000, message = "Meta description must not exceed 1000 characters")
    @Column(name = "meta_description", length = 1000)
    private String metaDescription;
    
    @Size(max = 500, message = "Meta keywords must not exceed 500 characters")
    @Column(name = "meta_keywords", length = 500)
    private String metaKeywords;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Business methods
    
    /**
     * Updates the brand's basic information.
     */
    public void updateBasicInfo(String name, String slug, String description) {
        this.name = name;
        this.slug = slug;
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the brand's visual assets.
     */
    public void updateAssets(String logoUrl, String websiteUrl) {
        this.logoUrl = logoUrl;
        this.websiteUrl = websiteUrl;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Activates the brand.
     */
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates the brand.
     */
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the sort order.
     */
    public void updateSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates SEO metadata.
     */
    public void updateSeoMetadata(String metaTitle, String metaDescription, String metaKeywords) {
        this.metaTitle = metaTitle;
        this.metaDescription = metaDescription;
        this.metaKeywords = metaKeywords;
        this.updatedAt = LocalDateTime.now();
    }
    
    // Builder pattern
    public static class Builder {
        private String name;
        private String slug;
        private String description;
        private String logoUrl;
        private String websiteUrl;
        private boolean active = true;
        private Integer sortOrder = 0;
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder slug(String slug) {
            this.slug = slug;
            return this;
        }
        
        public Builder description(String description) {
            this.description = description;
            return this;
        }
        
        public Builder logoUrl(String logoUrl) {
            this.logoUrl = logoUrl;
            return this;
        }
        
        public Builder websiteUrl(String websiteUrl) {
            this.websiteUrl = websiteUrl;
            return this;
        }
        
        public Builder active(boolean active) {
            this.active = active;
            return this;
        }
        
        public Builder sortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
            return this;
        }
        
        public Brand build() {
            Brand brand = new Brand();
            brand.name = this.name;
            brand.slug = this.slug;
            brand.description = this.description;
            brand.logoUrl = this.logoUrl;
            brand.websiteUrl = this.websiteUrl;
            brand.active = this.active;
            brand.sortOrder = this.sortOrder;
            return brand;
        }
    }
}
