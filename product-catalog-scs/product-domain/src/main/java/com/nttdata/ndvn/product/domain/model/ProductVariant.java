package com.nttdata.ndvn.product.domain.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * ProductVariant entity representing a specific variant of a product.
 * 
 * This entity manages product variations such as size, color, material, etc.
 * Each variant can have its own SKU, pricing, and inventory.
 */
@Entity
@Table(name = "product_variants", indexes = {
    @Index(name = "idx_variant_sku", columnList = "sku", unique = true),
    @Index(name = "idx_variant_product", columnList = "productId"),
    @Index(name = "idx_variant_active", columnList = "active")
})
@Getter
@Setter
@NoArgsConstructor
public class ProductVariant {
    
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;
    
    @NotNull(message = "Product is required")
    @Column(name = "product_id", nullable = false)
    private UUID productId;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "product_id", insertable = false, updatable = false)
    private Product product;
    
    @NotBlank(message = "Variant SKU is required")
    @Size(max = 100, message = "Variant SKU must not exceed 100 characters")
    @Column(name = "sku", unique = true, nullable = false, length = 100)
    private String sku;
    
    @NotBlank(message = "Variant name is required")
    @Size(max = 255, message = "Variant name must not exceed 255 characters")
    @Column(name = "name", nullable = false)
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    @Column(name = "description", length = 1000)
    private String description;
    
    @DecimalMin(value = "0.0", message = "Price adjustment must be non-negative")
    @Digits(integer = 10, fraction = 2, message = "Price adjustment must have at most 10 integer digits and 2 decimal places")
    @Column(name = "price_adjustment", precision = 12, scale = 2)
    private BigDecimal priceAdjustment = BigDecimal.ZERO;
    
    @DecimalMin(value = "0.0", message = "Weight must be non-negative")
    @Digits(integer = 8, fraction = 3, message = "Weight must have at most 8 integer digits and 3 decimal places")
    @Column(name = "weight", precision = 11, scale = 3)
    private BigDecimal weight;
    
    @DecimalMin(value = "0.0", message = "Length must be non-negative")
    @Digits(integer = 8, fraction = 2, message = "Length must have at most 8 integer digits and 2 decimal places")
    @Column(name = "length", precision = 10, scale = 2)
    private BigDecimal length;
    
    @DecimalMin(value = "0.0", message = "Width must be non-negative")
    @Digits(integer = 8, fraction = 2, message = "Width must have at most 8 integer digits and 2 decimal places")
    @Column(name = "width", precision = 10, scale = 2)
    private BigDecimal width;
    
    @DecimalMin(value = "0.0", message = "Height must be non-negative")
    @Digits(integer = 8, fraction = 2, message = "Height must have at most 8 integer digits and 2 decimal places")
    @Column(name = "height", precision = 10, scale = 2)
    private BigDecimal height;
    
    @Size(max = 100, message = "Color must not exceed 100 characters")
    @Column(name = "color", length = 100)
    private String color;
    
    @Size(max = 100, message = "Size must not exceed 100 characters")
    @Column(name = "size", length = 100)
    private String size;
    
    @Size(max = 100, message = "Material must not exceed 100 characters")
    @Column(name = "material", length = 100)
    private String material;
    
    @Size(max = 100, message = "Style must not exceed 100 characters")
    @Column(name = "style", length = 100)
    private String style;
    
    @Column(name = "active", nullable = false)
    private boolean active = true;
    
    @Column(name = "is_default")
    private boolean defaultVariant = false;
    
    @Min(value = 0, message = "Sort order must be non-negative")
    @Column(name = "sort_order")
    private Integer sortOrder = 0;
    
    @Size(max = 500, message = "Image URL must not exceed 500 characters")
    @Column(name = "image_url", length = 500)
    private String imageUrl;
    
    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    // Business methods
    
    /**
     * Updates the variant's basic information.
     */
    public void updateBasicInfo(String name, String description) {
        this.name = name;
        this.description = description;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the variant's pricing.
     */
    public void updatePricing(BigDecimal priceAdjustment) {
        if (priceAdjustment.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("Price adjustment must be non-negative");
        }
        this.priceAdjustment = priceAdjustment;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the variant's physical properties.
     */
    public void updatePhysicalProperties(String color, String size, String material, String style) {
        this.color = color;
        this.size = size;
        this.material = material;
        this.style = style;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the variant's dimensions.
     */
    public void updateDimensions(BigDecimal length, BigDecimal width, BigDecimal height, BigDecimal weight) {
        this.length = length;
        this.width = width;
        this.height = height;
        this.weight = weight;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Activates the variant.
     */
    public void activate() {
        this.active = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Deactivates the variant.
     */
    public void deactivate() {
        this.active = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Sets this variant as the default variant for the product.
     */
    public void setAsDefault() {
        this.defaultVariant = true;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Removes the default status from this variant.
     */
    public void removeDefaultStatus() {
        this.defaultVariant = false;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the sort order.
     */
    public void updateSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Updates the variant image.
     */
    public void updateImage(String imageUrl) {
        this.imageUrl = imageUrl;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Gets the effective price for this variant (base product price + adjustment).
     */
    public BigDecimal getEffectivePrice() {
        if (product == null) {
            return priceAdjustment;
        }
        return product.getEffectivePrice().add(priceAdjustment);
    }
    
    /**
     * Checks if this variant is available.
     */
    public boolean isAvailable() {
        return active && (product == null || product.isAvailable());
    }
    
    // Builder pattern
    public static class Builder {
        private UUID productId;
        private String sku;
        private String name;
        private String description;
        private BigDecimal priceAdjustment = BigDecimal.ZERO;
        private String color;
        private String size;
        private String material;
        private String style;
        private boolean active = true;
        private boolean defaultVariant = false;
        private Integer sortOrder = 0;
        
        public Builder productId(UUID productId) {
            this.productId = productId;
            return this;
        }
        
        public Builder sku(String sku) {
            this.sku = sku;
            return this;
        }
        
        public Builder name(String name) {
            this.name = name;
            return this;
        }
        
        public Builder description(String description) {
            this.description = description;
            return this;
        }
        
        public Builder priceAdjustment(BigDecimal priceAdjustment) {
            this.priceAdjustment = priceAdjustment;
            return this;
        }
        
        public Builder color(String color) {
            this.color = color;
            return this;
        }
        
        public Builder size(String size) {
            this.size = size;
            return this;
        }
        
        public Builder material(String material) {
            this.material = material;
            return this;
        }
        
        public Builder style(String style) {
            this.style = style;
            return this;
        }
        
        public Builder active(boolean active) {
            this.active = active;
            return this;
        }
        
        public Builder defaultVariant(boolean defaultVariant) {
            this.defaultVariant = defaultVariant;
            return this;
        }
        
        public Builder sortOrder(Integer sortOrder) {
            this.sortOrder = sortOrder;
            return this;
        }
        
        public ProductVariant build() {
            ProductVariant variant = new ProductVariant();
            variant.productId = this.productId;
            variant.sku = this.sku;
            variant.name = this.name;
            variant.description = this.description;
            variant.priceAdjustment = this.priceAdjustment;
            variant.color = this.color;
            variant.size = this.size;
            variant.material = this.material;
            variant.style = this.style;
            variant.active = this.active;
            variant.defaultVariant = this.defaultVariant;
            variant.sortOrder = this.sortOrder;
            return variant;
        }
    }
}
