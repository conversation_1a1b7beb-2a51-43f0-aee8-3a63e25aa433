package com.nttdata.ndvn.customer.domain.repository;

import com.nttdata.ndvn.customer.domain.model.Customer;
import com.nttdata.ndvn.customer.domain.model.CustomerStatus;
import com.nttdata.ndvn.customer.domain.model.CustomerType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Customer aggregate.
 * 
 * This interface defines the contract for customer data access operations,
 * following the Repository pattern from Domain-Driven Design.
 */
public interface CustomerRepository {
    
    /**
     * Save a customer entity.
     */
    Customer save(Customer customer);
    
    /**
     * Find customer by ID.
     */
    Optional<Customer> findById(UUID id);
    
    /**
     * Find customer by customer number.
     */
    Optional<Customer> findByCustomerNumber(String customerNumber);
    
    /**
     * Find customer by email address.
     */
    Optional<Customer> findByEmail(String email);
    
    /**
     * Find customer by user ID.
     */
    Optional<Customer> findByUserId(UUID userId);
    
    /**
     * Find all customers with pagination.
     */
    Page<Customer> findAll(Pageable pageable);
    
    /**
     * Find customers by status.
     */
    Page<Customer> findByStatus(CustomerStatus status, Pageable pageable);
    
    /**
     * Find customers by type.
     */
    Page<Customer> findByCustomerType(CustomerType customerType, Pageable pageable);
    
    /**
     * Find customers by classification.
     */
    Page<Customer> findByClassification(String classification, Pageable pageable);
    
    /**
     * Search customers by various criteria.
     */
    Page<Customer> searchCustomers(String searchTerm, Pageable pageable);
    
    /**
     * Find customers created after a specific date.
     */
    Page<Customer> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find customers by email domain.
     */
    Page<Customer> findByEmailDomain(String domain, Pageable pageable);
    
    /**
     * Check if customer number exists.
     */
    boolean existsByCustomerNumber(String customerNumber);
    
    /**
     * Check if email exists.
     */
    boolean existsByEmail(String email);
    
    /**
     * Check if user ID is already linked to a customer.
     */
    boolean existsByUserId(UUID userId);
    
    /**
     * Count customers by status.
     */
    long countByStatus(CustomerStatus status);
    
    /**
     * Count customers by type.
     */
    long countByCustomerType(CustomerType customerType);
    
    /**
     * Count customers created after a specific date.
     */
    long countByCreatedAtAfter(LocalDateTime createdAfter);
    
    /**
     * Find customers with specific segment.
     */
    Page<Customer> findBySegmentName(String segmentName, Pageable pageable);
    
    /**
     * Find customers without any segments.
     */
    Page<Customer> findCustomersWithoutSegments(Pageable pageable);
    
    /**
     * Find business customers.
     */
    Page<Customer> findBusinessCustomers(Pageable pageable);
    
    /**
     * Find individual customers.
     */
    Page<Customer> findIndividualCustomers(Pageable pageable);
    
    /**
     * Delete customer by ID.
     */
    void deleteById(UUID id);
    
    /**
     * Delete customer entity.
     */
    void delete(Customer customer);
    
    /**
     * Check if customer exists by ID.
     */
    boolean existsById(UUID id);
    
    /**
     * Find all customers by IDs.
     */
    List<Customer> findAllById(Iterable<UUID> ids);
}
