package com.nttdata.ndvn.product.domain.repository;

import com.nttdata.ndvn.product.domain.model.Category;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Repository interface for Category entity.
 * 
 * This interface defines the contract for category data access operations,
 * supporting hierarchical category management.
 */
public interface CategoryRepository {
    
    /**
     * Save a category entity.
     */
    Category save(Category category);
    
    /**
     * Find category by ID.
     */
    Optional<Category> findById(UUID id);
    
    /**
     * Find category by slug.
     */
    Optional<Category> findBySlug(String slug);
    
    /**
     * Find all categories with pagination.
     */
    Page<Category> findAll(Pageable pageable);
    
    /**
     * Find active categories.
     */
    Page<Category> findByActive(boolean active, Pageable pageable);
    
    /**
     * Find root categories (no parent).
     */
    List<Category> findRootCategories();
    
    /**
     * Find categories by parent ID.
     */
    List<Category> findByParentId(UUID parentId);
    
    /**
     * Find categories by parent ID with pagination.
     */
    Page<Category> findByParentId(UUID parentId, Pageable pageable);
    
    /**
     * Find categories by level.
     */
    List<Category> findByLevel(Integer level);
    
    /**
     * Find categories by level with pagination.
     */
    Page<Category> findByLevel(Integer level, Pageable pageable);
    
    /**
     * Find categories by name containing (case-insensitive).
     */
    Page<Category> findByNameContainingIgnoreCase(String name, Pageable pageable);
    
    /**
     * Find categories by description containing (case-insensitive).
     */
    Page<Category> findByDescriptionContainingIgnoreCase(String description, Pageable pageable);
    
    /**
     * Find categories created after a specific date.
     */
    Page<Category> findByCreatedAtAfter(LocalDateTime createdAfter, Pageable pageable);
    
    /**
     * Find categories updated after a specific date.
     */
    Page<Category> findByUpdatedAtAfter(LocalDateTime updatedAfter, Pageable pageable);
    
    /**
     * Find categories ordered by sort order.
     */
    List<Category> findAllOrderBySortOrderAsc();
    
    /**
     * Find categories by parent ordered by sort order.
     */
    List<Category> findByParentIdOrderBySortOrderAsc(UUID parentId);
    
    /**
     * Find categories ordered by name.
     */
    Page<Category> findAllOrderByNameAsc(Pageable pageable);
    
    /**
     * Find categories ordered by creation date.
     */
    Page<Category> findAllOrderByCreatedAtDesc(Pageable pageable);
    
    /**
     * Search categories by name or description.
     */
    Page<Category> searchCategories(String searchTerm, Pageable pageable);
    
    /**
     * Find leaf categories (no children).
     */
    List<Category> findLeafCategories();
    
    /**
     * Find categories with children.
     */
    List<Category> findCategoriesWithChildren();
    
    /**
     * Find categories by path containing.
     */
    Page<Category> findByPathContaining(String pathSegment, Pageable pageable);
    
    /**
     * Check if slug exists.
     */
    boolean existsBySlug(String slug);
    
    /**
     * Check if category exists by ID.
     */
    boolean existsById(UUID id);
    
    /**
     * Check if category has children.
     */
    boolean hasChildren(UUID categoryId);
    
    /**
     * Check if category has products.
     */
    boolean hasProducts(UUID categoryId);
    
    /**
     * Count categories by parent.
     */
    long countByParentId(UUID parentId);
    
    /**
     * Count active categories.
     */
    long countByActive(boolean active);
    
    /**
     * Count categories by level.
     */
    long countByLevel(Integer level);
    
    /**
     * Count root categories.
     */
    long countRootCategories();
    
    /**
     * Count leaf categories.
     */
    long countLeafCategories();
    
    /**
     * Find all descendants of a category.
     */
    List<Category> findDescendants(UUID categoryId);
    
    /**
     * Find all ancestors of a category.
     */
    List<Category> findAncestors(UUID categoryId);
    
    /**
     * Find siblings of a category.
     */
    List<Category> findSiblings(UUID categoryId);
    
    /**
     * Find categories by multiple IDs.
     */
    List<Category> findAllById(Iterable<UUID> ids);
    
    /**
     * Find categories by multiple parent IDs.
     */
    List<Category> findByParentIdIn(List<UUID> parentIds);
    
    /**
     * Find categories by multiple levels.
     */
    List<Category> findByLevelIn(List<Integer> levels);
    
    /**
     * Find categories with images.
     */
    Page<Category> findByImageUrlIsNotNull(Pageable pageable);
    
    /**
     * Find categories without images.
     */
    Page<Category> findByImageUrlIsNull(Pageable pageable);
    
    /**
     * Find categories with icons.
     */
    Page<Category> findByIconIsNotNull(Pageable pageable);
    
    /**
     * Find categories without icons.
     */
    Page<Category> findByIconIsNull(Pageable pageable);
    
    /**
     * Find categories by meta keywords containing.
     */
    Page<Category> findByMetaKeywordsContaining(String keywords, Pageable pageable);
    
    /**
     * Find categories for sitemap (active categories with paths).
     */
    List<Category> findCategoriesForSitemap();
    
    /**
     * Find categories for navigation menu.
     */
    List<Category> findCategoriesForNavigation(Integer maxLevel);
    
    /**
     * Find popular categories (with most products).
     */
    List<Category> findPopularCategories(int limit);
    
    /**
     * Delete category by ID.
     */
    void deleteById(UUID id);
    
    /**
     * Delete category entity.
     */
    void delete(Category category);
    
    /**
     * Save all categories.
     */
    List<Category> saveAll(Iterable<Category> categories);
    
    /**
     * Get categories count.
     */
    long count();
    
    /**
     * Build category tree from root.
     */
    List<Category> buildCategoryTree();
    
    /**
     * Build category tree from specific parent.
     */
    List<Category> buildCategoryTree(UUID parentId);
    
    /**
     * Get category breadcrumb path.
     */
    List<Category> getCategoryBreadcrumb(UUID categoryId);
    
    /**
     * Find categories by depth level.
     */
    List<Category> findByMaxDepth(Integer maxDepth);
    
    /**
     * Update category paths recursively.
     */
    void updateCategoryPaths(UUID categoryId);
    
    /**
     * Reorder categories within parent.
     */
    void reorderCategories(UUID parentId, List<UUID> categoryIds);
}
