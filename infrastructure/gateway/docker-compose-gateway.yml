version: '3.8'

services:
  # Consul for Service Discovery
  consul:
    image: consul:1.16
    hostname: consul
    container_name: ndvn-consul
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    environment:
      CONSUL_BIND_INTERFACE: eth0
      CONSUL_CLIENT_INTERFACE: eth0
    command: >
      consul agent 
      -server 
      -bootstrap-expect=1 
      -ui 
      -client=0.0.0.0 
      -bind=0.0.0.0
    volumes:
      - consul-data:/consul/data
      - ./consul/config:/consul/config
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "consul", "members"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API Gateway using Spring Cloud Gateway
  api-gateway:
    image: ndvn/api-gateway:latest
    hostname: api-gateway
    container_name: ndvn-api-gateway
    build:
      context: ./gateway-service
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
      - "8081:8081"  # Management port
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_CLOUD_CONSUL_HOST: consul
      SPRING_CLOUD_CONSUL_PORT: 8500
      SPRING_CLOUD_CONSUL_DISCOVERY_ENABLED: true
      SPRING_CLOUD_CONSUL_DISCOVERY_REGISTER: true
      SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_ENABLED: true
      SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_PATH: /actuator/health
      SPRING_CLOUD_CONSUL_DISCOVERY_HEALTH_CHECK_INTERVAL: 30s
      SPRING_CLOUD_CONSUL_DISCOVERY_INSTANCE_ID: api-gateway-1
      SPRING_CLOUD_CONSUL_DISCOVERY_SERVICE_NAME: api-gateway
      SPRING_CLOUD_CONSUL_DISCOVERY_HOSTNAME: api-gateway
      SPRING_CLOUD_CONSUL_DISCOVERY_PORT: 8080
      SPRING_CLOUD_GATEWAY_DISCOVERY_LOCATOR_ENABLED: true
      SPRING_CLOUD_GATEWAY_DISCOVERY_LOCATOR_LOWER_CASE_SERVICE_ID: true
      MANAGEMENT_ENDPOINTS_WEB_EXPOSURE_INCLUDE: health,info,metrics,prometheus
      MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS: always
      LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_CLOUD_GATEWAY: DEBUG
      LOGGING_LEVEL_ORG_SPRINGFRAMEWORK_CLOUD_CONSUL: DEBUG
    depends_on:
      consul:
        condition: service_healthy
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Load Balancer (HAProxy) for high availability
  load-balancer:
    image: haproxy:2.8
    hostname: load-balancer
    container_name: ndvn-load-balancer
    ports:
      - "80:80"
      - "443:443"
      - "8404:8404"  # HAProxy stats
    volumes:
      - ./haproxy/haproxy.cfg:/usr/local/etc/haproxy/haproxy.cfg:ro
      - ./haproxy/ssl:/etc/ssl/certs:ro
    depends_on:
      - api-gateway
    networks:
      - ndvn-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8404/stats"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Service Registry UI (Consul UI is built-in, but adding a custom dashboard)
  service-dashboard:
    image: nginx:alpine
    hostname: service-dashboard
    container_name: ndvn-service-dashboard
    ports:
      - "8090:80"
    volumes:
      - ./dashboard/html:/usr/share/nginx/html:ro
      - ./dashboard/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - consul
    networks:
      - ndvn-network
    restart: unless-stopped

volumes:
  consul-data:
    driver: local

networks:
  ndvn-network:
    external: true
    name: ndvn-scs-network
