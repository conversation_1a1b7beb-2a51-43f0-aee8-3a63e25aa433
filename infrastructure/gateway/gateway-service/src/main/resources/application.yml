server:
  port: 8080

spring:
  application:
    name: api-gateway
  
  profiles:
    active: local
  
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        enabled: true
        register: true
        health-check-enabled: true
        health-check-path: /actuator/health
        health-check-interval: 30s
        instance-id: ${spring.application.name}-${server.port}
        service-name: ${spring.application.name}
        hostname: localhost
        port: ${server.port}
        prefer-ip-address: false
        tags:
          - gateway
          - api
          - ndvn-scs
    
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          predicates:
            - name: Path
              args:
                pattern: "'/api/v1/' + serviceId + '/**'"
          filters:
            - name: StripPrefix
              args:
                parts: 2
      
      default-filters:
        - name: AddRequestHeader
          args:
            name: X-Gateway-Timestamp
            value: "#{T(java.time.Instant).now().toString()}"
        - name: AddRequestHeader
          args:
            name: X-Gateway-Version
            value: "1.0.0"
        - name: AddResponseHeader
          args:
            name: X-Gateway-Response-Time
            value: "#{T(java.time.Instant).now().toString()}"
      
      globalcors:
        cors-configurations:
          '[/**]':
            allowed-origins: 
              - "http://localhost:3000"
              - "http://localhost:8080"
              - "https://*.ndvn.com"
            allowed-methods:
              - GET
              - POST
              - PUT
              - DELETE
              - OPTIONS
            allowed-headers:
              - "*"
            allow-credentials: true
            max-age: 3600
      
      httpclient:
        connect-timeout: 5000
        response-timeout: 30s
        pool:
          max-connections: 100
          max-idle-time: 30s

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:8090/auth/realms/ndvn
          jwk-set-uri: http://localhost:8090/auth/realms/ndvn/protocol/openid-connect/certs

  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,gateway
  endpoint:
    health:
      show-details: always
    gateway:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5, 0.95, 0.99
  tracing:
    sampling:
      probability: 1.0
    zipkin:
      tracing:
        endpoint: http://localhost:9411/api/v2/spans

logging:
  level:
    org.springframework.cloud.gateway: INFO
    org.springframework.cloud.consul: INFO
    org.springframework.security: INFO
    com.nttdata.ndvn.gateway: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"

# Rate Limiting Configuration
resilience4j:
  circuitbreaker:
    instances:
      user-service-cb:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
      customer-service-cb:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
      catalog-service-cb:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
      order-service-cb:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
      notification-service-cb:
        sliding-window-size: 10
        minimum-number-of-calls: 5
        failure-rate-threshold: 50
        wait-duration-in-open-state: 30s
        permitted-number-of-calls-in-half-open-state: 3
  
  ratelimiter:
    instances:
      default:
        limit-for-period: 100
        limit-refresh-period: 1s
        timeout-duration: 0s
      auth-endpoints:
        limit-for-period: 10
        limit-refresh-period: 1s
        timeout-duration: 0s

---
spring:
  config:
    activate:
      on-profile: docker
  cloud:
    consul:
      host: consul
      port: 8500
  data:
    redis:
      host: redis
      port: 6379

management:
  tracing:
    zipkin:
      tracing:
        endpoint: http://zipkin:9411/api/v2/spans

---
spring:
  config:
    activate:
      on-profile: kubernetes
  cloud:
    consul:
      host: consul-service
      port: 8500
    kubernetes:
      discovery:
        enabled: true
      config:
        enabled: true
