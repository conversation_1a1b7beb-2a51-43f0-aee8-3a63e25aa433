# SCS Service Design Specification

## Overview

This document provides detailed design specifications for each Self-Contained System (SCS) in the NDVN Terasoluna Base Project migration. Each SCS maintains the proven layered architecture pattern while operating as an independent, deployable service.

## Common SCS Architecture Pattern

### Layered Architecture Template
Each SCS follows the established Terasoluna layered architecture:

```
{service-name}-scs/
├── {service-name}-domain/           # Pure business logic
├── {service-name}-infrastructure/   # Data access & external integrations
├── {service-name}-application/      # Use cases & application services
├── {service-name}-web/             # REST API & web configuration
├── {service-name}-events/          # Event handling & messaging (new layer)
└── docker/                         # Docker configuration
```

### Technology Stack per SCS
- **Framework:** Spring Boot 3.4.1 + Terasoluna 5.10.0
- **Language:** Java 21
- **Build Tool:** Gradle 8.5
- **Database:** PostgreSQL (per service)
- **Messaging:** Apache Kafka
- **API Documentation:** OpenAPI 3.0
- **Testing:** JUnit 5, TestContainers

## Service Specifications

### 1. User Management SCS (`user-scs`)

#### Service Responsibilities
- User authentication and authorization
- JWT token management
- Role and permission management
- Password policies and security
- User session management

#### Module Structure
```
user-scs/
├── user-domain/
│   ├── model/
│   │   ├── User.java (aggregate root)
│   │   ├── Role.java
│   │   ├── Permission.java
│   │   └── UserSession.java
│   ├── repository/
│   │   ├── UserRepository.java
│   │   └── RoleRepository.java
│   └── service/
│       ├── AuthenticationService.java
│       └── AuthorizationService.java
├── user-infrastructure/
│   ├── repository/
│   │   ├── JpaUserRepository.java
│   │   └── JpaRoleRepository.java
│   ├── security/
│   │   ├── JwtTokenProvider.java
│   │   └── PasswordEncoder.java
│   └── config/
│       └── SecurityConfig.java
├── user-application/
│   ├── service/
│   │   ├── UserApplicationService.java
│   │   └── AuthenticationApplicationService.java
│   └── dto/
│       ├── UserDto.java
│       ├── LoginRequest.java
│       └── AuthenticationResponse.java
├── user-events/
│   ├── publisher/
│   │   └── UserEventPublisher.java
│   └── events/
│       ├── UserCreatedEvent.java
│       └── UserUpdatedEvent.java
└── user-web/
    ├── controller/
    │   ├── AuthenticationController.java
    │   └── UserController.java
    └── config/
        └── WebConfig.java
```

#### Key APIs
- `POST /auth/login` - User authentication
- `POST /auth/logout` - User logout
- `POST /auth/refresh` - Token refresh
- `GET /users/{id}` - Get user details
- `POST /users` - Create user
- `PUT /users/{id}` - Update user
- `GET /users/{id}/roles` - Get user roles

#### Database Schema
```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Roles table
CREATE TABLE roles (
    id UUID PRIMARY KEY,
    name VARCHAR(50) UNIQUE NOT NULL,
    description VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User roles junction table
CREATE TABLE user_roles (
    user_id UUID REFERENCES users(id),
    role_id UUID REFERENCES roles(id),
    PRIMARY KEY (user_id, role_id)
);
```

#### Events Published
- `UserCreatedEvent` - When new user is registered
- `UserUpdatedEvent` - When user profile is modified
- `UserDeactivatedEvent` - When user is deactivated

### 2. Customer Management SCS (`customer-scs`)

#### Service Responsibilities
- Customer profile management
- Contact information management
- Customer segmentation and classification
- Customer communication preferences
- Customer relationship tracking

#### Module Structure
```
customer-scs/
├── customer-domain/
│   ├── model/
│   │   ├── Customer.java (aggregate root)
│   │   ├── CustomerProfile.java
│   │   ├── ContactInformation.java
│   │   └── Address.java (value object)
│   ├── repository/
│   │   └── CustomerRepository.java
│   └── service/
│       ├── CustomerService.java
│       └── CustomerClassificationService.java
├── customer-infrastructure/
│   ├── repository/
│   │   └── JpaCustomerRepository.java
│   └── external/
│       └── AddressValidationService.java
├── customer-application/
│   ├── service/
│   │   ├── CustomerApplicationService.java
│   │   └── CustomerSearchService.java
│   └── dto/
│       ├── CustomerDto.java
│       ├── CreateCustomerRequest.java
│       └── CustomerSearchCriteria.java
├── customer-events/
│   ├── publisher/
│   │   └── CustomerEventPublisher.java
│   ├── consumer/
│   │   └── UserEventConsumer.java
│   └── events/
│       ├── CustomerCreatedEvent.java
│       └── CustomerUpdatedEvent.java
└── customer-web/
    ├── controller/
    │   ├── CustomerController.java
    │   └── CustomerSearchController.java
    └── config/
        └── WebConfig.java
```

#### Key APIs
- `GET /customers/{id}` - Get customer details
- `POST /customers` - Create customer
- `PUT /customers/{id}` - Update customer
- `GET /customers/search` - Search customers
- `GET /customers/{id}/addresses` - Get customer addresses
- `POST /customers/{id}/addresses` - Add customer address

#### Events Published
- `CustomerCreatedEvent` - When new customer is created
- `CustomerUpdatedEvent` - When customer information changes
- `CustomerClassificationChangedEvent` - When customer segment changes

#### Events Consumed
- `UserCreatedEvent` - To link users with customers

### 3. Product Catalog SCS (`catalog-scs`)

#### Service Responsibilities
- Product information management
- Category and taxonomy management
- Inventory tracking and management
- Pricing and promotion rules
- Product search and filtering

#### Module Structure
```
catalog-scs/
├── catalog-domain/
│   ├── model/
│   │   ├── Product.java (aggregate root)
│   │   ├── Category.java
│   │   ├── InventoryItem.java
│   │   └── PriceRule.java
│   ├── repository/
│   │   ├── ProductRepository.java
│   │   └── CategoryRepository.java
│   └── service/
│       ├── ProductService.java
│       ├── InventoryService.java
│       └── PricingService.java
├── catalog-infrastructure/
│   ├── repository/
│   │   ├── JpaProductRepository.java
│   │   └── ElasticsearchProductRepository.java
│   └── external/
│       └── SupplierIntegrationService.java
├── catalog-application/
│   ├── service/
│   │   ├── ProductApplicationService.java
│   │   ├── InventoryApplicationService.java
│   │   └── ProductSearchService.java
│   └── dto/
│       ├── ProductDto.java
│       ├── InventoryDto.java
│       └── ProductSearchRequest.java
├── catalog-events/
│   ├── publisher/
│   │   └── CatalogEventPublisher.java
│   └── events/
│       ├── ProductCreatedEvent.java
│       ├── InventoryUpdatedEvent.java
│       └── PriceChangedEvent.java
└── catalog-web/
    ├── controller/
    │   ├── ProductController.java
    │   ├── CategoryController.java
    │   └── InventoryController.java
    └── config/
        └── WebConfig.java
```

#### Key APIs
- `GET /products/{id}` - Get product details
- `GET /products/search` - Search products
- `POST /products` - Create product
- `PUT /products/{id}` - Update product
- `GET /products/{id}/inventory` - Get inventory status
- `POST /products/{id}/inventory/reserve` - Reserve inventory

#### Events Published
- `ProductCreatedEvent` - When new product is added
- `ProductUpdatedEvent` - When product information changes
- `InventoryUpdatedEvent` - When inventory levels change
- `PriceChangedEvent` - When product price changes

### 4. Order Management SCS (`order-scs`)

#### Service Responsibilities
- Order creation and management
- Order fulfillment workflows
- Order status tracking
- Payment processing coordination
- Shipping and delivery management

#### Module Structure
```
order-scs/
├── order-domain/
│   ├── model/
│   │   ├── Order.java (aggregate root)
│   │   ├── OrderItem.java
│   │   ├── OrderStatus.java
│   │   └── Fulfillment.java
│   ├── repository/
│   │   └── OrderRepository.java
│   └── service/
│       ├── OrderService.java
│       ├── FulfillmentService.java
│       └── OrderValidationService.java
├── order-infrastructure/
│   ├── repository/
│   │   └── JpaOrderRepository.java
│   └── external/
│       ├── PaymentGatewayService.java
│       └── ShippingService.java
├── order-application/
│   ├── service/
│   │   ├── OrderApplicationService.java
│   │   ├── OrderFulfillmentService.java
│   │   └── OrderTrackingService.java
│   └── dto/
│       ├── OrderDto.java
│       ├── CreateOrderRequest.java
│       └── OrderStatusDto.java
├── order-events/
│   ├── publisher/
│   │   └── OrderEventPublisher.java
│   ├── consumer/
│   │   ├── CustomerEventConsumer.java
│   │   └── ProductEventConsumer.java
│   └── events/
│       ├── OrderPlacedEvent.java
│       ├── OrderShippedEvent.java
│       └── OrderCancelledEvent.java
└── order-web/
    ├── controller/
    │   ├── OrderController.java
    │   └── OrderTrackingController.java
    └── config/
        └── WebConfig.java
```

#### Key APIs
- `POST /orders` - Create order
- `GET /orders/{id}` - Get order details
- `PUT /orders/{id}/status` - Update order status
- `GET /orders/customer/{customerId}` - Get customer orders
- `POST /orders/{id}/cancel` - Cancel order
- `GET /orders/{id}/tracking` - Get order tracking info

#### Events Published
- `OrderPlacedEvent` - When order is successfully placed
- `OrderShippedEvent` - When order is shipped
- `OrderDeliveredEvent` - When order is delivered
- `OrderCancelledEvent` - When order is cancelled

#### Events Consumed
- `CustomerUpdatedEvent` - To update customer information
- `InventoryUpdatedEvent` - To validate product availability
- `PriceChangedEvent` - To handle price changes

### 5. Notification SCS (`notification-scs`)

#### Service Responsibilities
- Multi-channel notification delivery (email, SMS, push)
- Notification template management
- Delivery tracking and analytics
- User communication preferences
- Notification scheduling and retry logic

#### Module Structure
```
notification-scs/
├── notification-domain/
│   ├── model/
│   │   ├── Notification.java (aggregate root)
│   │   ├── NotificationTemplate.java
│   │   ├── DeliveryChannel.java
│   │   └── DeliveryLog.java
│   ├── repository/
│   │   ├── NotificationRepository.java
│   │   └── TemplateRepository.java
│   └── service/
│       ├── NotificationService.java
│       └── TemplateService.java
├── notification-infrastructure/
│   ├── repository/
│   │   ├── JpaNotificationRepository.java
│   │   └── JpaTemplateRepository.java
│   └── external/
│       ├── EmailService.java
│       ├── SmsService.java
│       └── PushNotificationService.java
├── notification-application/
│   ├── service/
│   │   ├── NotificationApplicationService.java
│   │   ├── TemplateApplicationService.java
│   │   └── DeliveryTrackingService.java
│   └── dto/
│       ├── NotificationDto.java
│       ├── SendNotificationRequest.java
│       └── DeliveryStatusDto.java
├── notification-events/
│   ├── publisher/
│   │   └── NotificationEventPublisher.java
│   ├── consumer/
│   │   ├── OrderEventConsumer.java
│   │   ├── CustomerEventConsumer.java
│   │   └── UserEventConsumer.java
│   └── events/
│       ├── NotificationSentEvent.java
│       └── NotificationFailedEvent.java
└── notification-web/
    ├── controller/
    │   ├── NotificationController.java
    │   └── TemplateController.java
    └── config/
        └── WebConfig.java
```

#### Key APIs
- `POST /notifications/send` - Send notification
- `GET /notifications/{id}/status` - Get delivery status
- `POST /templates` - Create notification template
- `GET /templates` - List templates
- `GET /notifications/analytics` - Get delivery analytics

#### Events Published
- `NotificationSentEvent` - When notification is successfully sent
- `NotificationFailedEvent` - When notification delivery fails

#### Events Consumed
- `OrderPlacedEvent` - To send order confirmation
- `OrderShippedEvent` - To send shipping notification
- `CustomerCreatedEvent` - To send welcome message
- `UserCreatedEvent` - To send account activation

## Cross-Cutting Concerns

### Security
- JWT token validation in all services
- Role-based access control per service
- API rate limiting and throttling
- Input validation and sanitization

### Monitoring and Observability
- Health check endpoints (`/actuator/health`)
- Metrics endpoints (`/actuator/metrics`)
- Distributed tracing with correlation IDs
- Structured logging with service context

### Configuration Management
- Environment-specific configuration
- External configuration via Spring Cloud Config
- Secret management for sensitive data
- Feature flags for gradual rollouts

### Error Handling
- Consistent error response format
- Circuit breaker patterns for external calls
- Retry logic with exponential backoff
- Dead letter queues for failed events

This design maintains the architectural principles of the original Terasoluna framework while enabling independent development, deployment, and scaling of each service.
